# 🧪 API 测试用例设计文档

## 📋 测试策略概览

### 🎯 测试目标
1. **功能性测试**: 验证所有API端点的基本功能
2. **边界条件测试**: 测试参数边界值和异常情况
3. **错误处理测试**: 验证错误响应和异常处理
4. **认证授权测试**: 验证用户权限和访问控制
5. **数据一致性测试**: 验证数据库操作的完整性
6. **性能测试**: 测试API响应时间和并发处理能力

### 🔧 测试工具
- **Playwright**: 自动化测试框架
- **HTTP请求**: 直接API调用测试
- **数据库验证**: SQLite数据一致性检查

---

## 👤 用户管理 API 测试用例

### 1. 用户注册 API (`POST /users/register`)

#### 正常情况测试
```javascript
test('用户注册 - 正常情况', async ({ request }) => {
  const response = await request.post('/api/v1/users/register', {
    data: {
      openid: 'test_openid_001',
      nickname: '测试用户',
      avatar_url: 'https://example.com/avatar.jpg',
      phone: '13800138000'
    }
  });
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data.openid).toBe('test_openid_001');
});
```

#### 边界条件测试
```javascript
test('用户注册 - 重复openid', async ({ request }) => {
  // 第一次注册
  await request.post('/api/v1/users/register', {
    data: { openid: 'duplicate_openid', nickname: '用户1' }
  });
  
  // 第二次注册相同openid
  const response = await request.post('/api/v1/users/register', {
    data: { openid: 'duplicate_openid', nickname: '用户2' }
  });
  
  expect(response.status()).toBe(200);
  // 应该返回已存在的用户信息
});
```

#### 错误处理测试
```javascript
test('用户注册 - 缺少必填参数', async ({ request }) => {
  const response = await request.post('/api/v1/users/register', {
    data: { nickname: '测试用户' } // 缺少openid
  });
  
  expect(response.status()).toBe(400);
  const data = await response.json();
  expect(data.code).toBe(400);
});
```

### 2. 用户登录 API (`POST /users/login`)

#### 正常情况测试
```javascript
test('用户登录 - 正常情况', async ({ request }) => {
  // 先注册用户
  await request.post('/api/v1/users/register', {
    data: { openid: 'login_test_001', nickname: '登录测试用户' }
  });
  
  // 登录
  const response = await request.post('/api/v1/users/login', {
    data: { openid: 'login_test_001' }
  });
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data.openid).toBe('login_test_001');
});
```

#### 错误处理测试
```javascript
test('用户登录 - 用户不存在', async ({ request }) => {
  const response = await request.post('/api/v1/users/login', {
    data: { openid: 'nonexistent_user' }
  });
  
  expect(response.status()).toBe(500);
  const data = await response.json();
  expect(data.message).toContain('用户不存在');
});
```

---

## 🏠 房间管理 API 测试用例

### 1. 获取房间列表 API (`GET /rooms`)

#### 正常情况测试
```javascript
test('获取房间列表 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/rooms');
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(Array.isArray(data.data)).toBe(true);
});
```

#### 分页测试
```javascript
test('获取房间列表 - 分页参数', async ({ request }) => {
  const response = await request.get('/api/v1/rooms?page=1&page_size=5');
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data.page).toBe(1);
  expect(data.data.page_size).toBe(5);
});
```

### 2. 创建房间 API (`POST /admin/rooms`)

#### 正常情况测试
```javascript
test('创建房间 - 正常情况', async ({ request }) => {
  const response = await request.post('/api/v1/admin/rooms', {
    data: {
      room_number: 'R001',
      name: '测试房间1',
      description: '这是一个测试房间',
      pricing_rule_id: 1
    }
  });
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data.room_number).toBe('R001');
});
```

#### 边界条件测试
```javascript
test('创建房间 - 重复房间号', async ({ request }) => {
  // 创建第一个房间
  await request.post('/api/v1/admin/rooms', {
    data: { room_number: 'R002', name: '房间2' }
  });
  
  // 创建重复房间号的房间
  const response = await request.post('/api/v1/admin/rooms', {
    data: { room_number: 'R002', name: '房间2重复' }
  });
  
  expect(response.status()).toBe(500);
});
```

---

## 📋 订单管理 API 测试用例

### 1. 创建订单 API (`POST /orders`)

#### 正常情况测试
```javascript
test('创建订单 - 正常情况', async ({ request }) => {
  // 先创建用户和房间
  const userResponse = await request.post('/api/v1/users/register', {
    data: { openid: 'order_user_001', nickname: '订单测试用户' }
  });
  const userId = userResponse.json().data.id;
  
  const roomResponse = await request.post('/api/v1/admin/rooms', {
    data: { room_number: 'R003', name: '订单测试房间' }
  });
  const roomId = roomResponse.json().data.id;
  
  // 创建订单
  const response = await request.post('/api/v1/orders', {
    headers: { 'X-User-ID': userId.toString() },
    data: {
      room_id: roomId,
      start_time: new Date().toISOString(),
      total_amount: 50.00
    }
  });
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data.room_id).toBe(roomId);
});
```

#### 认证测试
```javascript
test('创建订单 - 未登录用户', async ({ request }) => {
  const response = await request.post('/api/v1/orders', {
    data: {
      room_id: 1,
      start_time: new Date().toISOString(),
      total_amount: 50.00
    }
  });
  
  expect(response.status()).toBe(401);
  const data = await response.json();
  expect(data.message).toContain('用户未登录');
});
```

### 2. 订单支付 API (`POST /orders/{id}/payment`)

#### 正常情况测试
```javascript
test('订单支付 - 正常情况', async ({ request }) => {
  // 先创建订单
  const orderResponse = await createTestOrder(request);
  const orderId = orderResponse.data.id;
  
  const response = await request.post(`/api/v1/orders/${orderId}/payment`, {
    data: {
      payment_method: 'wechat',
      amount: 50.00
    }
  });
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
});
```

---

## 🔧 设备控制 API 测试用例

### 1. 设备心跳 API (`POST /mqtt/heartbeat`)

#### 正常情况测试
```javascript
test('设备心跳 - 正常情况', async ({ request }) => {
  const response = await request.post('/api/v1/mqtt/heartbeat', {
    data: {
      device_id: 'device_001',
      mac_address: '00:11:22:33:44:55',
      status: 'online'
    }
  });
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
});
```

### 2. 控制房间门锁 API (`POST /mqtt/room/{room_id}/lock`)

#### 正常情况测试
```javascript
test('控制门锁 - 开锁', async ({ request }) => {
  const response = await request.post('/api/v1/mqtt/room/1/lock', {
    data: { action: 'open' }
  });
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
});
```

#### 错误处理测试
```javascript
test('控制门锁 - 无效动作', async ({ request }) => {
  const response = await request.post('/api/v1/mqtt/room/1/lock', {
    data: { action: 'invalid_action' }
  });
  
  expect(response.status()).toBe(400);
});
```

---

## 📊 管理端 API 测试用例

### 1. 仪表盘数据 API (`GET /admin/dashboard`)

#### 正常情况测试
```javascript
test('仪表盘数据 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/admin/dashboard');
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data).toHaveProperty('todayIncome');
  expect(data.data).toHaveProperty('totalRooms');
  expect(data.data).toHaveProperty('occupiedRooms');
});
```

### 2. 用户列表 API (`GET /admin/users`)

#### 正常情况测试
```javascript
test('用户列表 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/admin/users');
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data).toHaveProperty('data');
  expect(data.data).toHaveProperty('total');
});
```

#### 筛选测试
```javascript
test('用户列表 - 昵称筛选', async ({ request }) => {
  const response = await request.get('/api/v1/admin/users?nickname=测试');
  
  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
});
```

---

## 🧪 数据一致性测试

### 数据库状态验证
```javascript
test('数据一致性 - 订单创建后房间状态', async ({ request }) => {
  // 创建订单
  const orderResponse = await createTestOrder(request);
  
  // 验证房间状态是否更新为occupied
  const roomResponse = await request.get(`/api/v1/rooms/${orderResponse.data.room_id}`);
  const roomData = await roomResponse.json();
  
  expect(roomData.data.status).toBe('occupied');
});
```

### 余额变动验证
```javascript
test('数据一致性 - 充值后余额更新', async ({ request }) => {
  // 获取用户初始余额
  const initialProfile = await request.get('/api/v1/users/profile', {
    headers: { 'X-User-ID': '1' }
  });
  const initialBalance = initialProfile.json().data.balance;
  
  // 充值
  await request.post('/api/v1/users/recharge', {
    headers: { 'X-User-ID': '1' },
    data: { amount: 100.00, payment_method: 'wechat' }
  });
  
  // 验证余额更新
  const updatedProfile = await request.get('/api/v1/users/profile', {
    headers: { 'X-User-ID': '1' }
  });
  const updatedBalance = updatedProfile.json().data.balance;
  
  expect(updatedBalance).toBe(initialBalance + 100.00);
});
```

---

## ⚡ 性能测试

### 并发请求测试
```javascript
test('性能测试 - 并发获取房间列表', async ({ request }) => {
  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(request.get('/api/v1/rooms'));
  }
  
  const responses = await Promise.all(promises);
  
  responses.forEach(response => {
    expect(response.status()).toBe(200);
  });
});
```

### 响应时间测试
```javascript
test('性能测试 - API响应时间', async ({ request }) => {
  const startTime = Date.now();
  const response = await request.get('/api/v1/admin/dashboard');
  const endTime = Date.now();
  
  expect(response.status()).toBe(200);
  expect(endTime - startTime).toBeLessThan(2000); // 响应时间小于2秒
});
```

---

## 🔒 安全性测试

### SQL注入测试
```javascript
test('安全性测试 - SQL注入防护', async ({ request }) => {
  const response = await request.get('/api/v1/rooms?status=\'; DROP TABLE rooms; --');

  // 应该正常处理，不会执行SQL注入
  expect(response.status()).toBe(200);
});
```

### XSS防护测试
```javascript
test('安全性测试 - XSS防护', async ({ request }) => {
  const response = await request.post('/api/v1/users/register', {
    data: {
      openid: 'xss_test',
      nickname: '<script>alert("xss")</script>'
    }
  });

  expect(response.status()).toBe(200);
  const data = await response.json();
  // 昵称应该被正确转义或过滤
  expect(data.data.nickname).not.toContain('<script>');
});
```

---

## 🎁 套餐管理 API 测试用例

### 1. 获取活跃套餐 API (`GET /packages`)

#### 正常情况测试
```javascript
test('获取活跃套餐 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/packages');

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(Array.isArray(data.data)).toBe(true);
});
```

### 2. 购买套餐 API (`POST /packages/{id}/purchase`)

#### 正常情况测试
```javascript
test('购买套餐 - 正常情况', async ({ request }) => {
  // 先获取可用套餐
  const packagesResponse = await request.get('/api/v1/packages');
  const packages = await packagesResponse.json();

  if (packages.data.length > 0) {
    const packageId = packages.data[0].id;

    const response = await request.post(`/api/v1/packages/${packageId}/purchase`, {
      headers: { 'X-User-ID': '1' },
      data: { payment_method: 'wechat' }
    });

    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
  }
});
```

#### 认证测试
```javascript
test('购买套餐 - 未登录用户', async ({ request }) => {
  const response = await request.post('/api/v1/packages/1/purchase', {
    data: { payment_method: 'wechat' }
  });

  expect(response.status()).toBe(401);
});
```

---

## 🏪 外卖平台 API 测试用例

### 1. 获取平台订单 API (`GET /admin/platform/orders`)

#### 正常情况测试
```javascript
test('获取平台订单 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/admin/platform/orders');

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data).toHaveProperty('data');
  expect(data.data).toHaveProperty('total');
});
```

#### 筛选测试
```javascript
test('获取平台订单 - 平台类型筛选', async ({ request }) => {
  const response = await request.get('/api/v1/admin/platform/orders?platform_type=meituan');

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
});
```

### 2. 订单核销 API (`POST /admin/platform/orders/verify`)

#### 正常情况测试
```javascript
test('订单核销 - 正常情况', async ({ request }) => {
  const response = await request.post('/api/v1/admin/platform/orders/verify', {
    data: {
      platform_order_id: 'MT123456789',
      verification_code: '1234'
    }
  });

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
});
```

#### 错误处理测试
```javascript
test('订单核销 - 无效核销码', async ({ request }) => {
  const response = await request.post('/api/v1/admin/platform/orders/verify', {
    data: {
      platform_order_id: 'MT123456789',
      verification_code: 'invalid'
    }
  });

  expect(response.status()).toBe(400);
});
```

---

## ⚙️ 系统配置 API 测试用例

### 1. 获取系统配置 API (`GET /system/config`)

#### 正常情况测试
```javascript
test('获取系统配置 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/system/config');

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data).toHaveProperty('version');
});
```

### 2. 计费规则管理 API

#### 获取计费规则测试
```javascript
test('获取计费规则 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/admin/pricing-rules');

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(Array.isArray(data.data)).toBe(true);
});
```

#### 创建计费规则测试
```javascript
test('创建计费规则 - 正常情况', async ({ request }) => {
  const response = await request.post('/api/v1/admin/pricing-rules', {
    data: {
      name: '测试计费规则',
      price_per_hour: 25.00,
      overnight_price: 20.00,
      start_time: '09:00',
      end_time: '22:00'
    }
  });

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data.name).toBe('测试计费规则');
});
```

---

## 📈 财务管理 API 测试用例

### 1. 财务报告 API (`GET /admin/finance/report`)

#### 正常情况测试
```javascript
test('财务报告 - 正常情况', async ({ request }) => {
  const today = new Date().toISOString().split('T')[0];
  const response = await request.get(`/api/v1/admin/finance/report?start_date=${today}&end_date=${today}`);

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data).toHaveProperty('totalIncome');
  expect(data.data).toHaveProperty('totalExpense');
});
```

### 2. 支出记录 API (`GET /admin/finance/expenses`)

#### 正常情况测试
```javascript
test('支出记录 - 正常情况', async ({ request }) => {
  const response = await request.get('/api/v1/admin/finance/expenses');

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
  expect(data.data).toHaveProperty('data');
  expect(data.data).toHaveProperty('total');
});
```

#### 添加支出测试
```javascript
test('添加支出 - 正常情况', async ({ request }) => {
  const response = await request.post('/api/v1/admin/finance/expenses', {
    data: {
      type: 'utilities',
      amount: 500.00,
      date: new Date().toISOString().split('T')[0],
      description: '电费支出'
    }
  });

  expect(response.status()).toBe(200);
  const data = await response.json();
  expect(data.code).toBe(200);
});
```

---

## 🔄 集成测试场景

### 完整业务流程测试
```javascript
test('完整业务流程 - 用户开台到结账', async ({ request }) => {
  // 1. 用户注册
  const userResponse = await request.post('/api/v1/users/register', {
    data: { openid: 'flow_test_user', nickname: '流程测试用户' }
  });
  const userId = userResponse.json().data.id;

  // 2. 用户充值
  await request.post('/api/v1/users/recharge', {
    headers: { 'X-User-ID': userId.toString() },
    data: { amount: 100.00, payment_method: 'wechat' }
  });

  // 3. 获取可用房间
  const roomsResponse = await request.get('/api/v1/rooms/available');
  const rooms = await roomsResponse.json();
  const roomId = rooms.data[0].id;

  // 4. 创建订单
  const orderResponse = await request.post('/api/v1/orders', {
    headers: { 'X-User-ID': userId.toString() },
    data: {
      room_id: roomId,
      start_time: new Date().toISOString(),
      total_amount: 50.00
    }
  });
  const orderId = orderResponse.json().data.id;

  // 5. 支付订单
  await request.post(`/api/v1/orders/${orderId}/payment`, {
    data: { payment_method: 'balance', amount: 50.00 }
  });

  // 6. 结束订单
  const endResponse = await request.put(`/api/v1/orders/${orderId}/end`);

  expect(endResponse.status()).toBe(200);

  // 7. 验证房间状态恢复
  const finalRoomResponse = await request.get(`/api/v1/rooms/${roomId}`);
  const finalRoom = await finalRoomResponse.json();
  expect(finalRoom.data.status).toBe('available');
});
```

---

## 📊 测试执行计划

### 测试优先级
1. **P0 - 核心功能**: 用户注册登录、房间管理、订单创建支付
2. **P1 - 重要功能**: 设备控制、财务管理、套餐管理
3. **P2 - 辅助功能**: 统计报表、系统配置、外卖平台

### 测试环境要求
- 后端服务正常运行 (localhost:8080)
- 数据库已初始化
- MQTT服务可用（可选）

### 测试数据准备
- 预置测试用户数据
- 预置房间和设备数据
- 预置计费规则数据
