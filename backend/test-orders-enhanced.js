const { chromium } = require('playwright');

(async () => {
  console.log('🚀 启动增强订单界面测试...');
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-dev-shm-usage']
  });
  
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const text = msg.text();
    if (!text.includes('ElementPlusError') && !text.includes('[vite]')) {
      console.log('🖥️ 浏览器控制台:', text);
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log('❌ 页面错误:', error.message);
  });
  
  try {
    console.log('📄 访问订单列表页面...');
    await page.goto('http://localhost:3000/orders/list');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('📊 检查统计卡片...');
    
    // 检查统计卡片
    const statsCards = await page.locator('.stats-card').count();
    console.log('统计卡片数量:', statsCards);
    
    if (statsCards > 0) {
      for (let i = 0; i < statsCards; i++) {
        const card = page.locator('.stats-card').nth(i);
        const value = await card.locator('.stats-value').textContent();
        const label = await card.locator('.stats-label').textContent();
        console.log(`统计卡片 ${i + 1}: ${label} = ${value}`);
      }
    }
    
    console.log('📋 检查订单表格列...');
    
    // 检查表格列标题
    const headers = await page.locator('.el-table th .cell').allTextContents();
    console.log('表格列标题:', headers);
    
    console.log('📝 检查订单数据详情...');
    
    // 检查订单表格
    const tableRows = await page.locator('.el-table tbody tr').count();
    console.log('订单行数:', tableRows);
    
    if (tableRows > 0) {
      console.log('📝 检查第一个订单的详细信息...');
      
      // 检查第一行订单数据
      const firstRow = page.locator('.el-table tbody tr').first();
      const cells = await firstRow.locator('td .cell').allTextContents();
      
      console.log('第一个订单详细信息:');
      for (let i = 0; i < Math.min(cells.length, headers.length); i++) {
        console.log(`  ${headers[i]}: ${cells[i]?.trim()}`);
      }
    }
    
    console.log('🎨 检查订单状态标签...');
    
    // 检查状态标签
    const statusTags = await page.locator('.el-table .el-tag').count();
    console.log('状态标签数量:', statusTags);
    
    if (statusTags > 0) {
      for (let i = 0; i < statusTags; i++) {
        const tag = page.locator('.el-table .el-tag').nth(i);
        const text = await tag.textContent();
        const type = await tag.getAttribute('class');
        console.log(`状态标签 ${i + 1}: ${text?.trim()} (${type?.includes('success') ? '绿色' : type?.includes('danger') ? '红色' : type?.includes('warning') ? '橙色' : type?.includes('info') ? '灰色' : type?.includes('primary') ? '蓝色' : '未知'})`);
      }
    }
    
    console.log('🔘 检查操作按钮...');
    
    // 检查操作按钮
    const actionButtons = await page.locator('.el-table .el-button').count();
    console.log('操作按钮总数:', actionButtons);
    
    if (actionButtons > 0) {
      const buttonTexts = await page.locator('.el-table .el-button').allTextContents();
      console.log('操作按钮:', buttonTexts.map(text => text.trim()).filter(text => text));
    }
    
    console.log('⏰ 检查时间格式...');
    
    // 检查时间格式
    const timeColumns = ['开始时间', '结束时间', '创建时间'];
    for (const timeCol of timeColumns) {
      const colIndex = headers.indexOf(timeCol);
      if (colIndex >= 0) {
        const timeCell = await page.locator('.el-table tbody tr').first().locator('td').nth(colIndex).textContent();
        console.log(`${timeCol}格式: ${timeCell?.trim()}`);
      }
    }
    
    console.log('🔍 测试详情按钮...');
    
    // 测试详情按钮
    const detailButtons = page.locator('.el-table .el-button').filter({ hasText: '详情' });
    const detailCount = await detailButtons.count();
    
    if (detailCount > 0) {
      console.log('点击第一个详情按钮...');
      await detailButtons.first().click();
      await page.waitForTimeout(2000);
      
      const currentUrl = page.url();
      if (currentUrl.includes('/orders/detail/')) {
        console.log('✅ 详情页面跳转成功:', currentUrl);
        
        // 返回列表页面
        await page.goBack();
        await page.waitForTimeout(1000);
      }
    }
    
    console.log('✅ 增强订单界面测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('🔒 关闭浏览器...');
  await browser.close();
  console.log('✅ 增强订单界面测试完成!');
})().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
