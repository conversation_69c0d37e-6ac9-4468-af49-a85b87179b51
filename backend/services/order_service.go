package services

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/repositories"
	"time"
)

// OrderService 订单服务接口
type OrderService interface {
	CreateOrder(userID int, req *models.OrderCreateRequest) (*models.OrderResponse, error)
	GetOrder(id int) (*models.OrderResponse, error)
	UpdateOrder(id int, order *models.Order) (*models.OrderResponse, error)
	GetOrderList(page, pageSize int, filters map[string]interface{}) ([]*models.OrderResponse, int64, error)
	GetUserOrders(userID int, page, pageSize int) ([]*models.OrderResponse, int64, error)
	PayOrder(id int, req *models.OrderPaymentRequest) error
	ExtendOrder(id int, req *models.OrderExtendRequest) error
	CompleteOrder(id int) error
	CancelOrder(id int) error
	GetActiveOrderByRoom(roomID int) (*models.OrderResponse, error)
	GetTodayIncome() (float64, error)
	GetTodayOrderCount() (int, error)
	GetActiveOrderCount() (int, error)
	GetIncomeReport(startDate, endDate time.Time) (*models.FinanceReport, error)
	GetOrderTrendData(startDate, endDate time.Time) ([]models.OrderTrendData, error)
	CalculateOrderAmount(roomID int, hours int) (float64, error)
	GetOrderStats() (*models.OrderStats, error)
}

// orderService 订单服务实现
type orderService struct {
	orderRepo   repositories.OrderRepository
	userRepo    repositories.UserRepository
	roomRepo    repositories.RoomRepository
	pricingRepo repositories.PricingRuleRepository
	userService UserService
	roomService RoomService
}

// NewOrderService 创建订单服务
func NewOrderService(orderRepo repositories.OrderRepository, userRepo repositories.UserRepository,
	roomRepo repositories.RoomRepository, pricingRepo repositories.PricingRuleRepository,
	userService UserService, roomService RoomService) OrderService {
	return &orderService{
		orderRepo:   orderRepo,
		userRepo:    userRepo,
		roomRepo:    roomRepo,
		pricingRepo: pricingRepo,
		userService: userService,
		roomService: roomService,
	}
}

// CreateOrder 创建订单
func (s *orderService) CreateOrder(userID int, req *models.OrderCreateRequest) (*models.OrderResponse, error) {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 检查房间是否存在且可用
	room, err := s.roomRepo.GetByID(req.RoomID)
	if err != nil {
		return nil, fmt.Errorf("获取房间信息失败: %v", err)
	}
	if room == nil {
		return nil, fmt.Errorf("房间不存在")
	}
	if room.Status != models.RoomStatusAvailable {
		return nil, fmt.Errorf("房间不可用")
	}

	// 检查房间是否有活跃订单
	activeOrder, err := s.orderRepo.GetActiveOrderByRoom(req.RoomID)
	if err != nil {
		return nil, fmt.Errorf("检查房间订单失败: %v", err)
	}
	if activeOrder != nil {
		return nil, fmt.Errorf("房间已被占用")
	}

	// 创建订单
	order := &models.Order{
		OrderNumber: models.GenerateOrderNumber(),
		UserID:      &userID,
		RoomID:      &req.RoomID,
		StartTime:   time.Now(),
		TotalAmount: req.TotalAmount,
		PaidAmount:  0,
		Status:      models.OrderStatusPending,
	}

	err = s.orderRepo.Create(order)
	if err != nil {
		return nil, fmt.Errorf("创建订单失败: %v", err)
	}

	// 更新房间状态为占用
	err = s.roomService.UpdateRoomStatus(req.RoomID, models.RoomStatusOccupied)
	if err != nil {
		return nil, fmt.Errorf("更新房间状态失败: %v", err)
	}

	return s.buildOrderResponse(order)
}

// GetOrder 获取订单信息
func (s *orderService) GetOrder(id int) (*models.OrderResponse, error) {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取订单信息失败: %v", err)
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}

	return s.buildOrderResponse(order)
}

// UpdateOrder 更新订单
func (s *orderService) UpdateOrder(id int, order *models.Order) (*models.OrderResponse, error) {
	existingOrder, err := s.orderRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取订单信息失败: %v", err)
	}
	if existingOrder == nil {
		return nil, fmt.Errorf("订单不存在")
	}

	order.ID = id
	err = s.orderRepo.Update(order)
	if err != nil {
		return nil, fmt.Errorf("更新订单失败: %v", err)
	}

	return s.buildOrderResponse(order)
}

// GetOrderList 获取订单列表
func (s *orderService) GetOrderList(page, pageSize int, filters map[string]interface{}) ([]*models.OrderResponse, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()

	// 构建过滤条件
	if orderNumber, ok := filters["order_number"]; ok && orderNumber != "" {
		filterParams.AddCondition("order_number LIKE ?", "%"+orderNumber.(string)+"%")
	}
	if userID, ok := filters["user_id"]; ok && userID != 0 {
		filterParams.AddCondition("user_id = ?", userID)
	}
	if roomID, ok := filters["room_id"]; ok && roomID != 0 {
		filterParams.AddCondition("room_id = ?", roomID)
	}
	if status, ok := filters["status"]; ok && status != "" {
		filterParams.AddCondition("status = ?", status)
	}
	if startDate, ok := filters["start_date"]; ok && startDate != "" {
		filterParams.AddCondition("DATE(created_at) >= ?", startDate)
	}
	if endDate, ok := filters["end_date"]; ok && endDate != "" {
		filterParams.AddCondition("DATE(created_at) <= ?", endDate)
	}

	orders, total, err := s.orderRepo.List(pagination, filterParams)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*models.OrderResponse, len(orders))
	for i, order := range orders {
		response, _ := s.buildOrderResponse(order)
		responses[i] = response
	}

	return responses, total, nil
}

// GetUserOrders 获取用户订单列表
func (s *orderService) GetUserOrders(userID int, page, pageSize int) ([]*models.OrderResponse, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	orders, total, err := s.orderRepo.GetByUserID(userID, pagination)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*models.OrderResponse, len(orders))
	for i, order := range orders {
		response, _ := s.buildOrderResponse(order)
		responses[i] = response
	}

	return responses, total, nil
}

// PayOrder 支付订单
func (s *orderService) PayOrder(id int, req *models.OrderPaymentRequest) error {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("获取订单信息失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	if order.Status != models.OrderStatusPending {
		return fmt.Errorf("订单状态不允许支付")
	}

	// 检查支付金额
	if req.Amount < order.TotalAmount-order.PaidAmount {
		return fmt.Errorf("支付金额不足")
	}

	// 扣除用户余额
	if order.UserID != nil {
		err = s.userService.DeductBalance(*order.UserID, req.Amount, fmt.Sprintf("订单支付 - 订单号: %d", order.ID))
		if err != nil {
			return fmt.Errorf("扣除余额失败: %v", err)
		}
	} else {
		return fmt.Errorf("订单用户信息缺失")
	}

	// 更新订单状态
	order.PaidAmount += req.Amount
	order.Status = models.OrderStatusPaid
	err = s.orderRepo.Update(order)
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %v", err)
	}

	return nil
}

// ExtendOrder 订单续费
func (s *orderService) ExtendOrder(id int, req *models.OrderExtendRequest) error {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("获取订单信息失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	if order.Status != models.OrderStatusPaid {
		return fmt.Errorf("订单状态不允许续费")
	}

	// 扣除用户余额
	if order.UserID != nil {
		err = s.userService.DeductBalance(*order.UserID, req.Amount, fmt.Sprintf("订单续费 - 订单号: %d", order.ID))
		if err != nil {
			return fmt.Errorf("扣除余额失败: %v", err)
		}
	} else {
		return fmt.Errorf("订单用户信息缺失")
	}

	// 更新订单金额
	order.TotalAmount += req.Amount
	order.PaidAmount += req.Amount
	err = s.orderRepo.Update(order)
	if err != nil {
		return fmt.Errorf("更新订单失败: %v", err)
	}

	return nil
}

// CompleteOrder 完成订单
func (s *orderService) CompleteOrder(id int) error {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("获取订单信息失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	if order.Status != models.OrderStatusPaid {
		return fmt.Errorf("订单状态不允许完成")
	}

	// 更新订单状态
	now := time.Now()
	order.EndTime = &now
	order.Status = models.OrderStatusCompleted
	err = s.orderRepo.Update(order)
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %v", err)
	}

	// 更新房间状态为可用
	if order.RoomID != nil {
		err = s.roomService.UpdateRoomStatus(*order.RoomID, models.RoomStatusAvailable)
		if err != nil {
			return fmt.Errorf("更新房间状态失败: %v", err)
		}
	}

	return nil
}

// CancelOrder 取消订单
func (s *orderService) CancelOrder(id int) error {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("获取订单信息失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	if order.Status == models.OrderStatusCompleted || order.Status == models.OrderStatusCancelled {
		return fmt.Errorf("订单状态不允许取消")
	}

	// 如果已支付，退款给用户
	if order.PaidAmount > 0 && order.UserID != nil {
		err = s.userService.RefundBalance(*order.UserID, order.PaidAmount, fmt.Sprintf("订单取消退款 - 订单号: %d", order.ID))
		if err != nil {
			return fmt.Errorf("退款失败: %v", err)
		}
	}

	// 更新订单状态
	order.Status = models.OrderStatusCancelled
	err = s.orderRepo.Update(order)
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %v", err)
	}

	// 更新房间状态为可用
	if order.RoomID != nil {
		err = s.roomService.UpdateRoomStatus(*order.RoomID, models.RoomStatusAvailable)
		if err != nil {
			return fmt.Errorf("更新房间状态失败: %v", err)
		}
	}

	return nil
}

// GetActiveOrderByRoom 获取房间的活跃订单
func (s *orderService) GetActiveOrderByRoom(roomID int) (*models.OrderResponse, error) {
	order, err := s.orderRepo.GetActiveOrderByRoom(roomID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, nil
	}

	return s.buildOrderResponse(order)
}

// GetTodayIncome 获取今日收入
func (s *orderService) GetTodayIncome() (float64, error) {
	return s.orderRepo.GetTodayIncome()
}

// GetTodayOrderCount 获取今日订单数
func (s *orderService) GetTodayOrderCount() (int, error) {
	today := time.Now().Format("2006-01-02")
	count, _, err := s.orderRepo.GetDailyStats(today)
	return int(count), err
}

// GetActiveOrderCount 获取活跃订单数量（正在进行中的订单）
func (s *orderService) GetActiveOrderCount() (int, error) {
	return s.orderRepo.GetActiveOrderCount()
}

// GetIncomeReport 获取收入报表
func (s *orderService) GetIncomeReport(startDate, endDate time.Time) (*models.FinanceReport, error) {
	// 获取日收入数据
	dailyIncome, err := s.orderRepo.GetIncomeByDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 计算总收入和总订单数
	var totalIncome float64
	for _, data := range dailyIncome {
		totalIncome += data.Income
	}

	totalOrders, err := s.orderRepo.GetOrderCountByDateRange(startDate, endDate)
	if err != nil {
		totalOrders = 0
	}

	// 计算平均订单价值
	var avgOrderValue float64
	if totalOrders > 0 {
		avgOrderValue = totalIncome / float64(totalOrders)
	}

	return &models.FinanceReport{
		Period:            fmt.Sprintf("%s 至 %s", startDate.Format("2006-01-02"), endDate.Format("2006-01-02")),
		TotalIncome:       totalIncome,
		TotalOrders:       int(totalOrders),
		AverageOrderValue: avgOrderValue,
		DailyIncome:       dailyIncome,
	}, nil
}

// GetOrderTrendData 获取订单趋势数据
func (s *orderService) GetOrderTrendData(startDate, endDate time.Time) ([]models.OrderTrendData, error) {
	// 生成日期范围内的订单趋势数据
	var trendData []models.OrderTrendData

	for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		count, _, err := s.orderRepo.GetDailyStats(dateStr)
		if err != nil {
			count = 0
		}

		trendData = append(trendData, models.OrderTrendData{
			Date:  dateStr,
			Count: int(count),
		})
	}

	return trendData, nil
}

// CalculateOrderAmount 计算订单金额
func (s *orderService) CalculateOrderAmount(roomID int, hours int) (float64, error) {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return 0, fmt.Errorf("获取房间信息失败: %v", err)
	}
	if room == nil {
		return 0, fmt.Errorf("房间不存在")
	}

	pricingRule, err := s.pricingRepo.GetByID(room.PricingRuleID)
	if err != nil {
		return 0, fmt.Errorf("获取计费规则失败: %v", err)
	}
	if pricingRule == nil {
		return 0, fmt.Errorf("计费规则不存在")
	}

	// 简单按小时计费
	amount := pricingRule.PricePerHour * float64(hours)
	return amount, nil
}

// buildOrderResponse 构建订单响应数据
func (s *orderService) buildOrderResponse(order *models.Order) (*models.OrderResponse, error) {
	response := order.ToResponse()

	// 获取用户信息（如果 UserID 不为空）
	if order.UserID != nil {
		user, err := s.userRepo.GetByID(*order.UserID)
		if err == nil && user != nil {
			response.User = user.ToResponse()
		}
	}

	// 获取房间信息（如果 RoomID 不为空）
	if order.RoomID != nil {
		roomResponse, err := s.roomService.GetRoom(*order.RoomID)
		if err == nil && roomResponse != nil {
			response.Room = roomResponse
		}
	}

	return response, nil
}

// GetOrderStats 获取订单统计数据
func (s *orderService) GetOrderStats() (*models.OrderStats, error) {
	stats := &models.OrderStats{}

	// 获取总订单数和总金额
	totalOrders, totalAmount, err := s.orderRepo.GetTotalStats()
	if err != nil {
		return nil, fmt.Errorf("获取总统计数据失败: %v", err)
	}
	stats.Total = totalOrders
	stats.TotalAmount = totalAmount

	// 获取今日订单数和今日收入
	today := time.Now().Format("2006-01-02")
	todayOrders, todayAmount, err := s.orderRepo.GetDailyStats(today)
	if err != nil {
		return nil, fmt.Errorf("获取今日统计数据失败: %v", err)
	}
	stats.TodayOrders = todayOrders
	stats.TodayAmount = todayAmount

	return stats, nil
}
