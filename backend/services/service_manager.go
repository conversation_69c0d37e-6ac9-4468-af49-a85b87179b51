package services

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/repositories"
	"time"
)

// ServiceManager 服务管理器
type ServiceManager struct {
	User         UserService
	Room         RoomService
	Order        OrderService
	Device       DeviceService
	Reservation  ReservationService
	Promotion    PromotionService
	PlatformOrder PlatformOrderService
	Dashboard    DashboardService
	PricingRule  PricingRuleService
	Finance      FinanceService
	Package      PackageService
	UserPackage  UserPackageService
}

// NewServiceManager 创建服务管理器
func NewServiceManager(repos *repositories.Repositories) *ServiceManager {
	// 创建基础服务
	userService := NewUserService(repos.User)
	pricingRuleService := NewPricingRuleService(repos.PricingRule)
	roomService := NewRoomService(repos.Room, repos.Device, repos.PricingRule, repos.Order)
	orderService := NewOrderService(repos.Order, repos.User, repos.Room, repos.PricingRule, userService, roomService)
	deviceService := NewDeviceService(repos.Device, repos.Room)
	reservationService := NewReservationService(repos.Reservation, repos.User, repos.Room)
	promotionService := NewPromotionService(repos.Promotion)
	platformOrderService := NewPlatformOrderService(repos.PlatformOrder, repos.User, repos.Room)
	dashboardService := NewDashboardService(repos.Order, repos.Room, repos.User, repos.Device)
	financeService := NewFinanceService(repos.Order, repos.Room, repos.User, repos.Expense)

	// 创建套餐相关服务
	packageService := NewPackageService(repos.Package, repos.UserPackage, repos.User, repos.Order, repos.Room)
	userPackageService := NewUserPackageService(repos.UserPackage, repos.PackageUsageLog, repos.User, repos.Room, repos.Order)

	return &ServiceManager{
		User:          userService,
		Room:          roomService,
		Order:         orderService,
		Device:        deviceService,
		Reservation:   reservationService,
		Promotion:     promotionService,
		PlatformOrder: platformOrderService,
		Dashboard:     dashboardService,
		PricingRule:   pricingRuleService,
		Finance:       financeService,
		Package:       packageService,
		UserPackage:   userPackageService,
	}
}

// DeviceService 设备服务接口
type DeviceService interface {
	// 基础设备管理
	GetDeviceList(page, pageSize int, filters map[string]interface{}) ([]*models.DeviceResponse, int64, error)
	GetDeviceByID(id int) (*models.DeviceResponse, error)
	GetDeviceByMacAddress(macAddress string) (*models.DeviceResponse, error)
	GetDevicesByRoomID(roomID int) ([]*models.DeviceResponse, error)
	UpdateDeviceStatus(id int, status string) error
	UpdateHeartbeat(deviceID int) error
	GetDeviceStatistics() (map[string]int, error)
	GetOfflineDevices() ([]*models.DeviceResponse, error)

	// 新增功能
	GetDeviceListGrouped() (*models.DeviceListResponse, error)
	GetDevicesByType(deviceType string) ([]*models.DeviceResponse, error)
	BatchControlDevices(deviceIDs []int, command string, params map[string]interface{}) error
	ControlRoomDevice(roomID int, deviceType string, command string, params map[string]interface{}) error
	SendAudioMessage(roomID int, message string, messageType string, volume int) error
}

// ReservationService 预约服务接口（简化版）
type ReservationService interface {
	CreateReservation(userID int, req *models.ReservationCreateRequest) (*models.ReservationResponse, error)
	GetReservationList(page, pageSize int, filters map[string]interface{}) ([]*models.ReservationResponse, int64, error)
	CancelReservation(id int) error
}

// PromotionService 优惠活动服务接口（简化版）
type PromotionService interface {
	GetActivePromotions() ([]*models.PromotionResponse, error)
	GetPromotionList(page, pageSize int, filters map[string]interface{}) ([]*models.PromotionResponse, int64, error)
}

// PlatformOrderService 外卖平台订单服务接口
type PlatformOrderService interface {
	GetPlatformOrderList(page, pageSize int, filters map[string]interface{}) ([]*models.PlatformOrder, int64, error)
	GetPlatformOrder(id int) (*models.PlatformOrder, error)
	GetPlatformOrderStats(filters map[string]interface{}) (*models.PlatformOrderStats, error)
	VerifyOrder(req *models.PlatformOrderVerifyRequest) error
	BatchVerifyOrders(orderIds []string) error
	GetPendingOrders() ([]*models.PlatformOrder, error)
	RefundOrder(id int) error
	DeletePlatformOrder(id int) error
	BatchDeleteOrders(orderIds []int) error
	ExportOrders(filters map[string]interface{}) ([]byte, string, error)
	GetVerificationStats() (*models.VerificationStats, error)
	GetVerificationHistory(page, pageSize int, filter string) ([]*models.PlatformOrder, int64, error)
	GetAnalytics(startDate, endDate string) (*models.PlatformAnalytics, error)
	GetRoomAnalysis(startDate, endDate string) ([]*models.RoomAnalysis, error)
}

// DashboardService 仪表盘服务接口
type DashboardService interface {
	GetDashboardData() (*models.DashboardData, error)
	GetFinanceReport(startDate, endDate string) (*models.FinanceReport, error)
}

// 简化的服务实现（占位符）
type deviceService struct {
	deviceRepo repositories.DeviceRepository
	roomRepo   repositories.RoomRepository
}

func NewDeviceService(deviceRepo repositories.DeviceRepository, roomRepo repositories.RoomRepository) DeviceService {
	return &deviceService{
		deviceRepo: deviceRepo,
		roomRepo:   roomRepo,
	}
}

func (s *deviceService) GetDeviceList(page, pageSize int, filters map[string]interface{}) ([]*models.DeviceResponse, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()

	// 构建过滤条件
	if deviceType, ok := filters["type"]; ok && deviceType != "" {
		filterParams.AddCondition("type = ?", deviceType)
	}
	if roomID, ok := filters["room_id"]; ok && roomID != 0 {
		filterParams.AddCondition("room_id = ?", roomID)
	}
	if status, ok := filters["status"]; ok && status != "" {
		filterParams.AddCondition("status = ?", status)
	}

	devices, total, err := s.deviceRepo.List(pagination, filterParams)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*models.DeviceResponse, len(devices))
	for i, device := range devices {
		responses[i] = device.ToResponse()
	}

	return responses, total, nil
}

func (s *deviceService) GetDeviceByID(id int) (*models.DeviceResponse, error) {
	device, err := s.deviceRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	if device == nil {
		return nil, fmt.Errorf("设备不存在")
	}

	return device.ToResponse(), nil
}

func (s *deviceService) GetDeviceByMacAddress(macAddress string) (*models.DeviceResponse, error) {
	device, err := s.deviceRepo.GetByMacAddress(macAddress)
	if err != nil {
		return nil, err
	}
	if device == nil {
		return nil, fmt.Errorf("设备不存在")
	}

	return device.ToResponse(), nil
}

func (s *deviceService) GetDevicesByRoomID(roomID int) ([]*models.DeviceResponse, error) {
	devices, err := s.deviceRepo.GetByRoomID(roomID)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.DeviceResponse, len(devices))
	for i, device := range devices {
		responses[i] = device.ToResponse()
	}

	return responses, nil
}

func (s *deviceService) UpdateDeviceStatus(id int, status string) error {
	return s.deviceRepo.UpdateStatus(id, status)
}

func (s *deviceService) UpdateHeartbeat(deviceID int) error {
	return s.deviceRepo.UpdateHeartbeat(deviceID, time.Now())
}

func (s *deviceService) GetDeviceStatistics() (map[string]int, error) {
	return s.deviceRepo.GetDeviceStatistics()
}

func (s *deviceService) GetOfflineDevices() ([]*models.DeviceResponse, error) {
	devices, err := s.deviceRepo.GetOfflineDevices()
	if err != nil {
		return nil, err
	}

	responses := make([]*models.DeviceResponse, len(devices))
	for i, device := range devices {
		responses[i] = device.ToResponse()
	}

	return responses, nil
}

// GetDeviceListGrouped 获取按类型分组的设备列表
func (s *deviceService) GetDeviceListGrouped() (*models.DeviceListResponse, error) {
	// 获取所有设备
	devices, _, err := s.GetDeviceList(1, 1000, map[string]interface{}{})
	if err != nil {
		return nil, err
	}

	// 按设备类型分组
	groups := make(map[string]*models.DeviceGroupResponse)
	stats := &models.DeviceStats{}

	for _, device := range devices {
		// 统计总数
		stats.Total++
		if device.IsOnline {
			stats.Online++
		} else {
			stats.Offline++
		}
		if device.Status == models.DeviceStatusMaintenance {
			stats.Fault++
		}

		// 按类型分组
		if group, exists := groups[device.Type]; exists {
			group.Count++
			if device.IsOnline {
				group.OnlineCount++
			}
			group.Devices = append(group.Devices, device)
		} else {
			groups[device.Type] = &models.DeviceGroupResponse{
				Type:        device.Type,
				TypeName:    models.GetDeviceTypeName(device.Type),
				Count:       1,
				OnlineCount: 0,
				Devices:     []*models.DeviceResponse{device},
			}
			if device.IsOnline {
				groups[device.Type].OnlineCount = 1
			}
		}
	}

	// 转换为切片
	groupList := make([]*models.DeviceGroupResponse, 0, len(groups))
	for _, group := range groups {
		groupList = append(groupList, group)
	}

	return &models.DeviceListResponse{
		Groups: groupList,
		Stats:  stats,
	}, nil
}

// GetDevicesByType 根据设备类型获取设备列表
func (s *deviceService) GetDevicesByType(deviceType string) ([]*models.DeviceResponse, error) {
	devices, err := s.deviceRepo.GetByType(deviceType)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.DeviceResponse, len(devices))
	for i, device := range devices {
		responses[i] = device.ToResponse()
	}

	return responses, nil
}

// BatchControlDevices 批量控制设备
func (s *deviceService) BatchControlDevices(deviceIDs []int, command string, params map[string]interface{}) error {
	// 这里需要MQTT客户端支持，暂时返回未实现错误
	return fmt.Errorf("批量设备控制功能需要MQTT客户端支持")
}

// ControlRoomDevice 控制房间指定类型的设备
func (s *deviceService) ControlRoomDevice(roomID int, deviceType string, command string, params map[string]interface{}) error {
	// 获取房间指定类型的设备
	devices, err := s.GetDevicesByRoomID(roomID)
	if err != nil {
		return fmt.Errorf("获取房间设备失败: %v", err)
	}

	// 查找指定类型的设备
	var targetDevice *models.DeviceResponse
	for _, device := range devices {
		if device.Type == deviceType {
			targetDevice = device
			break
		}
	}

	if targetDevice == nil {
		return fmt.Errorf("房间 %d 没有找到类型为 %s 的设备", roomID, deviceType)
	}

	// 这里需要MQTT客户端支持，暂时返回未实现错误
	return fmt.Errorf("设备控制功能需要MQTT客户端支持")
}

// SendAudioMessage 发送音频消息到房间喇叭
func (s *deviceService) SendAudioMessage(roomID int, message string, messageType string, volume int) error {
	// 获取房间的音响设备
	devices, err := s.GetDevicesByRoomID(roomID)
	if err != nil {
		return fmt.Errorf("获取房间设备失败: %v", err)
	}

	// 查找音响设备
	var speakerDevice *models.DeviceResponse
	for _, device := range devices {
		if device.Type == models.DeviceTypeSpeaker {
			speakerDevice = device
			break
		}
	}

	if speakerDevice == nil {
		return fmt.Errorf("房间 %d 没有找到音响设备", roomID)
	}

	// 这里需要MQTT客户端支持，暂时返回未实现错误
	return fmt.Errorf("音频消息发送功能需要MQTT客户端支持")
}

// 其他服务的简化实现...
type reservationService struct {
	reservationRepo repositories.ReservationRepository
	userRepo        repositories.UserRepository
	roomRepo        repositories.RoomRepository
}

func NewReservationService(reservationRepo repositories.ReservationRepository, userRepo repositories.UserRepository, roomRepo repositories.RoomRepository) ReservationService {
	return &reservationService{
		reservationRepo: reservationRepo,
		userRepo:        userRepo,
		roomRepo:        roomRepo,
	}
}

func (s *reservationService) CreateReservation(userID int, req *models.ReservationCreateRequest) (*models.ReservationResponse, error) {
	// 检查时间冲突
	hasConflict, err := s.reservationRepo.CheckConflict(req.RoomID, req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}
	if hasConflict {
		return nil, fmt.Errorf("预约时间冲突")
	}

	reservation := &models.Reservation{
		UserID:    userID,
		RoomID:    req.RoomID,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Status:    models.ReservationStatusConfirmed,
	}

	err = s.reservationRepo.Create(reservation)
	if err != nil {
		return nil, err
	}

	return reservation.ToResponse(), nil
}

func (s *reservationService) GetReservationList(page, pageSize int, filters map[string]interface{}) ([]*models.ReservationResponse, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()

	reservations, total, err := s.reservationRepo.List(pagination, filterParams)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]*models.ReservationResponse, len(reservations))
	for i, reservation := range reservations {
		responses[i] = reservation.ToResponse()
	}

	return responses, total, nil
}

func (s *reservationService) CancelReservation(id int) error {
	return s.reservationRepo.UpdateStatus(id, models.ReservationStatusCancelled)
}

type promotionService struct {
	promotionRepo repositories.PromotionRepository
}

func NewPromotionService(promotionRepo repositories.PromotionRepository) PromotionService {
	return &promotionService{
		promotionRepo: promotionRepo,
	}
}

func (s *promotionService) GetActivePromotions() ([]*models.PromotionResponse, error) {
	promotions, err := s.promotionRepo.GetValidPromotions()
	if err != nil {
		return nil, err
	}

	responses := make([]*models.PromotionResponse, len(promotions))
	for i, promotion := range promotions {
		responses[i] = promotion.ToResponse()
	}

	return responses, nil
}

func (s *promotionService) GetPromotionList(page, pageSize int, filters map[string]interface{}) ([]*models.PromotionResponse, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()

	promotions, total, err := s.promotionRepo.List(pagination, filterParams)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]*models.PromotionResponse, len(promotions))
	for i, promotion := range promotions {
		responses[i] = promotion.ToResponse()
	}

	return responses, total, nil
}

type platformOrderService struct {
	platformOrderRepo repositories.PlatformOrderRepository
	userRepo          repositories.UserRepository
	roomRepo          repositories.RoomRepository
}

func NewPlatformOrderService(platformOrderRepo repositories.PlatformOrderRepository, userRepo repositories.UserRepository, roomRepo repositories.RoomRepository) PlatformOrderService {
	return &platformOrderService{
		platformOrderRepo: platformOrderRepo,
		userRepo:          userRepo,
		roomRepo:          roomRepo,
	}
}

func (s *platformOrderService) GetPlatformOrderList(page, pageSize int, filters map[string]interface{}) ([]*models.PlatformOrder, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()

	// 应用筛选条件
	if platformType, ok := filters["platform_type"]; ok && platformType != "" {
		filterParams.AddCondition("po.platform_type = ?", platformType)
	}
	if orderStatus, ok := filters["order_status"]; ok && orderStatus != "" {
		filterParams.AddCondition("po.order_status = ?", orderStatus)
	}
	if verificationStatus, ok := filters["verification_status"]; ok && verificationStatus != "" {
		filterParams.AddCondition("po.verification_status = ?", verificationStatus)
	}
	if roomNumber, ok := filters["room_number"]; ok && roomNumber != "" {
		filterParams.AddCondition("po.room_id = (SELECT id FROM rooms WHERE room_number = ?)", roomNumber)
	}
	if platformOrderId, ok := filters["platform_order_id"]; ok && platformOrderId != "" {
		filterParams.AddCondition("po.platform_order_id LIKE ?", "%"+platformOrderId.(string)+"%")
	}
	if startDate, ok := filters["start_date"]; ok && startDate != "" {
		filterParams.AddCondition("DATE(po.created_at) >= ?", startDate)
	}
	if endDate, ok := filters["end_date"]; ok && endDate != "" {
		filterParams.AddCondition("DATE(po.created_at) <= ?", endDate)
	}

	return s.platformOrderRepo.List(pagination, filterParams)
}

func (s *platformOrderService) GetPlatformOrder(id int) (*models.PlatformOrder, error) {
	return s.platformOrderRepo.GetByID(id)
}

func (s *platformOrderService) GetPlatformOrderStats(filters map[string]interface{}) (*models.PlatformOrderStats, error) {
	// 暂时返回基于实际数据的简化统计
	// 获取所有订单进行统计
	pagination := repositories.NewPaginationParams(1, 1000) // 获取足够多的数据进行统计
	filterParams := repositories.NewFilterParams()

	// 应用筛选条件
	if startDate, ok := filters["start_date"]; ok && startDate != "" {
		filterParams.AddCondition("DATE(created_at) >= ?", startDate)
	}
	if endDate, ok := filters["end_date"]; ok && endDate != "" {
		filterParams.AddCondition("DATE(created_at) <= ?", endDate)
	}

	orders, _, err := s.platformOrderRepo.List(pagination, filterParams)
	if err != nil {
		return nil, fmt.Errorf("获取订单数据失败: %v", err)
	}

	// 统计数据
	stats := &models.PlatformOrderStats{
		Meituan:  models.PlatformStats{Count: 0, Amount: 0},
		Eleme:    models.PlatformStats{Count: 0, Amount: 0},
		Pending:  models.PlatformStats{Count: 0, Amount: 0},
		Verified: models.PlatformStats{Count: 0, Amount: 0},
	}

	for _, order := range orders {
		// 按平台统计
		switch order.PlatformType {
		case "meituan":
			stats.Meituan.Count++
			stats.Meituan.Amount += order.PaidAmount
		case "eleme":
			stats.Eleme.Count++
			stats.Eleme.Amount += order.PaidAmount
		}

		// 按核销状态统计
		switch order.VerificationStatus {
		case "pending":
			stats.Pending.Count++
			stats.Pending.Amount += order.PaidAmount
		case "verified":
			stats.Verified.Count++
			stats.Verified.Amount += order.PaidAmount
		}
	}

	return stats, nil
}

func (s *platformOrderService) VerifyOrder(req *models.PlatformOrderVerifyRequest) error {
	order, err := s.platformOrderRepo.GetByPlatformOrderID(req.PlatformOrderID)
	if err != nil {
		return err
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	return s.platformOrderRepo.UpdateVerificationStatus(order.ID, models.VerificationStatusVerified)
}

func (s *platformOrderService) BatchVerifyOrders(orderIds []string) error {
	for _, orderId := range orderIds {
		req := &models.PlatformOrderVerifyRequest{
			PlatformOrderID: orderId,
		}
		err := s.VerifyOrder(req)
		if err != nil {
			return fmt.Errorf("核销订单 %s 失败: %v", orderId, err)
		}
	}
	return nil
}

func (s *platformOrderService) GetPendingOrders() ([]*models.PlatformOrder, error) {
	pagination := repositories.NewPaginationParams(1, 50)
	filterParams := repositories.NewFilterParams()
	filterParams.AddCondition("po.verification_status = ?", models.VerificationStatusPending)

	orders, _, err := s.platformOrderRepo.List(pagination, filterParams)
	return orders, err
}

func (s *platformOrderService) RefundOrder(id int) error {
	return s.platformOrderRepo.UpdateVerificationStatus(id, models.VerificationStatusRefunded)
}

func (s *platformOrderService) DeletePlatformOrder(id int) error {
	return s.platformOrderRepo.Delete(id)
}

func (s *platformOrderService) BatchDeleteOrders(orderIds []int) error {
	for _, id := range orderIds {
		err := s.DeletePlatformOrder(id)
		if err != nil {
			return fmt.Errorf("删除订单 %d 失败: %v", id, err)
		}
	}
	return nil
}

func (s *platformOrderService) ExportOrders(filters map[string]interface{}) ([]byte, string, error) {
	// 简化实现，返回模拟的Excel数据
	excelContent := "平台订单导出数据\n订单号,平台,金额,状态,时间\n"
	filename := fmt.Sprintf("platform_orders_%s.xlsx", time.Now().Format("2006-01-02"))
	return []byte(excelContent), filename, nil
}

func (s *platformOrderService) GetVerificationStats() (*models.VerificationStats, error) {
	now := time.Now()
	today := now.Format("2006-01-02")

	// 查询今日已核销数量
	todayVerifiedPagination := repositories.NewPaginationParams(1, 1000)
	todayVerifiedFilters := repositories.NewFilterParams()
	todayVerifiedFilters.AddCondition("po.verification_status = ?", models.VerificationStatusVerified)
	todayVerifiedFilters.AddCondition("DATE(po.created_at) = ?", today)
	todayVerifiedOrders, _, err := s.platformOrderRepo.List(todayVerifiedPagination, todayVerifiedFilters)
	if err != nil {
		return nil, err
	}

	// 查询待核销订单
	pendingPagination := repositories.NewPaginationParams(1, 1000)
	pendingFilters := repositories.NewFilterParams()
	pendingFilters.AddCondition("po.verification_status = ?", models.VerificationStatusPending)
	pendingOrders, _, err := s.platformOrderRepo.List(pendingPagination, pendingFilters)
	if err != nil {
		return nil, err
	}

	// 计算待核销订单总金额
	var pendingAmount float64
	for _, order := range pendingOrders {
		pendingAmount += order.PaidAmount
	}

	// 计算核销效率（简化计算）
	totalOrders := len(todayVerifiedOrders) + len(pendingOrders)
	var efficiency float64
	if totalOrders > 0 {
		efficiency = float64(len(todayVerifiedOrders)) / float64(totalOrders) * 100
	}

	return &models.VerificationStats{
		Today: models.TodayStats{
			Verified: len(todayVerifiedOrders),
			Trend:    8.5, // 趋势数据需要历史对比，暂时使用固定值
		},
		Pending: models.PendingStats{
			Count:  len(pendingOrders),
			Amount: pendingAmount,
		},
		Efficiency: models.EfficiencyStats{
			Rate:    efficiency,
			AvgTime: 3.2, // 平均时间需要复杂计算，暂时使用固定值
		},
	}, nil
}

func (s *platformOrderService) GetVerificationHistory(page, pageSize int, filter string) ([]*models.PlatformOrder, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()
	filterParams.AddCondition("po.verification_status = ?", models.VerificationStatusVerified)

	// 根据filter参数设置日期范围
	now := time.Now()
	switch filter {
	case "today":
		filterParams.AddCondition("DATE(po.created_at) = ?", now.Format("2006-01-02"))
	case "week":
		weekStart := now.AddDate(0, 0, -int(now.Weekday()))
		filterParams.AddCondition("DATE(po.created_at) >= ?", weekStart.Format("2006-01-02"))
		filterParams.AddCondition("DATE(po.created_at) <= ?", now.Format("2006-01-02"))
	case "month":
		monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		filterParams.AddCondition("DATE(po.created_at) >= ?", monthStart.Format("2006-01-02"))
		filterParams.AddCondition("DATE(po.created_at) <= ?", now.Format("2006-01-02"))
	}

	return s.platformOrderRepo.List(pagination, filterParams)
}

func (s *platformOrderService) GetAnalytics(startDate, endDate string) (*models.PlatformAnalytics, error) {
	// 简化实现，返回模拟数据
	return &models.PlatformAnalytics{
		Summary: models.AnalyticsSummary{
			TotalRevenue:    2230.80,
			TotalOrders:     27,
			AvgOrderValue:   82.62,
			ConversionRate:  85.2,
			RevenueTrend:    12.5,
			OrdersTrend:     8.3,
			AvgOrderTrend:   4.1,
			ConversionTrend: 2.8,
		},
		RevenueChart:        []models.ChartData{},
		PlatformComparison:  []models.PlatformComparisonData{},
		TimeDistribution:    []models.TimeDistributionData{},
		PlatformPerformance: []models.PlatformPerformanceData{},
		PeakHours:          []models.PeakHourData{},
	}, nil
}

func (s *platformOrderService) GetRoomAnalysis(startDate, endDate string) ([]*models.RoomAnalysis, error) {
	// 简化实现，返回模拟数据
	return []*models.RoomAnalysis{
		{
			RoomNumber:        1,
			RoomName:         "豪华包间A",
			TotalOrders:      8,
			TotalRevenue:     680.50,
			AvgOrderValue:    85.06,
			OrderFrequency:   75.5,
			PreferredPlatform: "meituan",
			PeakHour:         "19:00-21:00",
		},
		{
			RoomNumber:        2,
			RoomName:         "标准包间B",
			TotalOrders:      6,
			TotalRevenue:     420.30,
			AvgOrderValue:    70.05,
			OrderFrequency:   60.2,
			PreferredPlatform: "eleme",
			PeakHour:         "18:00-20:00",
		},
	}, nil
}

type dashboardService struct {
	orderRepo  repositories.OrderRepository
	roomRepo   repositories.RoomRepository
	userRepo   repositories.UserRepository
	deviceRepo repositories.DeviceRepository
}

func NewDashboardService(orderRepo repositories.OrderRepository, roomRepo repositories.RoomRepository, userRepo repositories.UserRepository, deviceRepo repositories.DeviceRepository) DashboardService {
	return &dashboardService{
		orderRepo:  orderRepo,
		roomRepo:   roomRepo,
		userRepo:   userRepo,
		deviceRepo: deviceRepo,
	}
}

func (s *dashboardService) GetDashboardData() (*models.DashboardData, error) {
	// 获取今日收入
	todayIncome, _ := s.orderRepo.GetTodayIncome()

	// 获取房间统计
	roomStats := map[string]int{
		"total":     0,
		"available": 0,
		"occupied":  0,
	}

	// 获取设备统计
	deviceStats, _ := s.deviceRepo.GetDeviceStatistics()

	return &models.DashboardData{
		TodayIncome:    todayIncome,
		TotalRooms:     roomStats["total"],
		OccupiedRooms:  roomStats["occupied"],
		AvailableRooms: roomStats["available"],
		OnlineDevices:  deviceStats["online"],
		OfflineDevices: deviceStats["offline"],
	}, nil
}

func (s *dashboardService) GetFinanceReport(startDate, endDate string) (*models.FinanceReport, error) {
	// 简化实现
	return &models.FinanceReport{
		Period:      fmt.Sprintf("%s 至 %s", startDate, endDate),
		TotalIncome: 0,
		TotalOrders: 0,
	}, nil
}
