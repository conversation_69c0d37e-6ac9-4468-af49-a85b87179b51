package services

import (
	"fmt"
	"time"

	"mahjong-system/models"
	"mahjong-system/repositories"
)

// FinanceService 财务服务接口
type FinanceService interface {
	GetFinanceReport(startDate, endDate time.Time) (*models.FinanceReportData, error)
	GetFinanceCharts(startDate, endDate time.Time, revenueType, profitType string) (*models.FinanceChartsData, error)
	AddExpense(req *models.ExpenseCreateRequest) (*models.Expense, error)
	GetExpenseList(page, pageSize int, filters map[string]interface{}) ([]*models.Expense, int64, error)
	ExportFinanceReport(startDate, endDate time.Time) ([]byte, string, error)
	GetFinanceSummary(startDate, endDate time.Time) (*models.FinanceSummary, error)
}

// financeService 财务服务实现
type financeService struct {
	orderRepo   repositories.OrderRepository
	roomRepo    repositories.RoomRepository
	userRepo    repositories.UserRepository
	expenseRepo repositories.ExpenseRepository
}

// NewFinanceService 创建财务服务
func NewFinanceService(
	orderRepo repositories.OrderRepository,
	roomRepo repositories.RoomRepository,
	userRepo repositories.UserRepository,
	expenseRepo repositories.ExpenseRepository,
) FinanceService {
	return &financeService{
		orderRepo:   orderRepo,
		roomRepo:    roomRepo,
		userRepo:    userRepo,
		expenseRepo: expenseRepo,
	}
}

// GetFinanceReport 获取财务报表数据
func (s *financeService) GetFinanceReport(startDate, endDate time.Time) (*models.FinanceReportData, error) {
	// 获取收入数据
	revenueData, err := s.getRevenueData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取收入数据失败: %v", err)
	}

	// 获取支出数据
	expenseData, err := s.getExpenseData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取支出数据失败: %v", err)
	}

	// 获取房间排行数据
	roomRanking, err := s.getRoomRankingData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取房间排行数据失败: %v", err)
	}

	// 获取收入来源分布
	revenueSource, err := s.getRevenueSourceData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取收入来源数据失败: %v", err)
	}

	// 获取支出分类数据
	expenseCategories, err := s.getExpenseCategoriesData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取支出分类数据失败: %v", err)
	}

	// 计算财务指标
	summary := s.calculateFinanceSummary(revenueData, expenseData, startDate, endDate)

	return &models.FinanceReportData{
		Summary:           summary,
		RevenueChart:      s.generateRevenueChart(revenueData, "daily"),
		RevenueSource:     revenueSource,
		RoomRanking:       roomRanking,
		ExpenseCategories: expenseCategories,
	}, nil
}

// GetFinanceCharts 获取图表数据
func (s *financeService) GetFinanceCharts(startDate, endDate time.Time, revenueType, profitType string) (*models.FinanceChartsData, error) {
	// 获取收入数据
	revenueData, err := s.getRevenueData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取收入数据失败: %v", err)
	}

	// 获取支出数据
	expenseData, err := s.getExpenseData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取支出数据失败: %v", err)
	}

	return &models.FinanceChartsData{
		ExpenseTrend:    s.generateExpenseTrend(expenseData),
		ProfitAnalysis:  s.generateProfitAnalysis(revenueData, expenseData, profitType),
	}, nil
}

// AddExpense 添加支出记录
func (s *financeService) AddExpense(req *models.ExpenseCreateRequest) (*models.Expense, error) {
	// 解析日期
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, fmt.Errorf("日期格式错误: %v", err)
	}

	expense := &models.Expense{
		Type:        req.Type,
		Amount:      req.Amount,
		Date:        date,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.expenseRepo.Create(expense)
	if err != nil {
		return nil, fmt.Errorf("创建支出记录失败: %v", err)
	}

	return expense, nil
}

// GetExpenseList 获取支出记录列表
func (s *financeService) GetExpenseList(page, pageSize int, filters map[string]interface{}) ([]*models.Expense, int64, error) {
	return s.expenseRepo.GetList(page, pageSize, filters)
}

// ExportFinanceReport 导出财务报表
func (s *financeService) ExportFinanceReport(startDate, endDate time.Time) ([]byte, string, error) {
	// 获取财务数据
	report, err := s.GetFinanceReport(startDate, endDate)
	if err != nil {
		return nil, "", fmt.Errorf("获取财务数据失败: %v", err)
	}

	// 生成Excel文件
	fileData, err := s.generateExcelReport(report, startDate, endDate)
	if err != nil {
		return nil, "", fmt.Errorf("生成Excel文件失败: %v", err)
	}

	filename := fmt.Sprintf("财务报表_%s_%s.xlsx", 
		startDate.Format("2006-01-02"), 
		endDate.Format("2006-01-02"))

	return fileData, filename, nil
}

// GetFinanceSummary 获取财务概要数据
func (s *financeService) GetFinanceSummary(startDate, endDate time.Time) (*models.FinanceSummary, error) {
	// 获取收入数据
	revenueData, err := s.getRevenueData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取收入数据失败: %v", err)
	}

	// 获取支出数据
	expenseData, err := s.getExpenseData(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取支出数据失败: %v", err)
	}

	return s.calculateFinanceSummary(revenueData, expenseData, startDate, endDate), nil
}

// 私有方法：获取收入数据
func (s *financeService) getRevenueData(startDate, endDate time.Time) ([]models.RevenueData, error) {
	// 从订单表获取收入数据
	orders, err := s.orderRepo.GetOrdersByDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 按日期聚合收入数据
	revenueMap := make(map[string]float64)
	for _, order := range orders {
		if order.Status == models.OrderStatusPaid || order.Status == models.OrderStatusCompleted {
			dateKey := order.CreatedAt.Format("2006-01-02")
			revenueMap[dateKey] += order.TotalAmount
		}
	}

	// 转换为切片
	var revenueData []models.RevenueData
	for date, amount := range revenueMap {
		revenueData = append(revenueData, models.RevenueData{
			Date:    date,
			Revenue: amount,
		})
	}

	return revenueData, nil
}

// 私有方法：获取支出数据
func (s *financeService) getExpenseData(startDate, endDate time.Time) ([]models.ExpenseData, error) {
	expenses, err := s.expenseRepo.GetByDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 按日期聚合支出数据
	expenseMap := make(map[string]float64)
	for _, expense := range expenses {
		dateKey := expense.Date.Format("2006-01-02")
		expenseMap[dateKey] += expense.Amount
	}

	// 转换为切片
	var expenseData []models.ExpenseData
	for date, amount := range expenseMap {
		expenseData = append(expenseData, models.ExpenseData{
			Date:   date,
			Amount: amount,
		})
	}

	return expenseData, nil
}

// 私有方法：获取房间排行数据
func (s *financeService) getRoomRankingData(startDate, endDate time.Time) ([]models.RoomRankingData, error) {
	// 获取所有房间
	pagination := &repositories.PaginationParams{Page: 1, PageSize: 100}
	filters := repositories.NewFilterParams()
	rooms, _, err := s.roomRepo.List(pagination, filters)
	if err != nil {
		return nil, err
	}

	var rankingData []models.RoomRankingData
	for _, room := range rooms {
		// 获取房间订单数据
		orders, err := s.orderRepo.GetOrdersByRoomAndDateRange(room.ID, startDate, endDate)
		if err != nil {
			continue
		}

		var revenue float64
		var ordersCount int
		for _, order := range orders {
			if order.Status == models.OrderStatusPaid || order.Status == models.OrderStatusCompleted {
				revenue += order.TotalAmount
				ordersCount++
			}
		}

		// 计算使用率（简化计算）
		usageRate := float64(ordersCount) * 2.5 // 假设每单平均2.5小时

		rankingData = append(rankingData, models.RoomRankingData{
			RoomID:      room.ID,
			RoomName:    room.Name,
			Revenue:     revenue,
			OrdersCount: ordersCount,
			UsageRate:   usageRate,
			Trend:       0, // 简化处理，实际应该计算趋势
		})
	}

	return rankingData, nil
}

// 私有方法：获取收入来源分布数据
func (s *financeService) getRevenueSourceData(startDate, endDate time.Time) ([]models.RevenueSourceData, error) {
	// 简化实现，实际应该根据支付方式分类
	orders, err := s.orderRepo.GetOrdersByDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	var totalRevenue float64
	for _, order := range orders {
		if order.Status == models.OrderStatusPaid || order.Status == models.OrderStatusCompleted {
			totalRevenue += order.TotalAmount
		}
	}

	// 模拟收入来源分布
	return []models.RevenueSourceData{
		{Name: "微信支付", Amount: totalRevenue * 0.6},
		{Name: "支付宝", Amount: totalRevenue * 0.3},
		{Name: "现金支付", Amount: totalRevenue * 0.1},
	}, nil
}

// 私有方法：获取支出分类数据
func (s *financeService) getExpenseCategoriesData(startDate, endDate time.Time) ([]models.ExpenseCategoryData, error) {
	expenses, err := s.expenseRepo.GetByDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// 按类型聚合支出
	categoryMap := make(map[string]float64)
	for _, expense := range expenses {
		categoryMap[expense.Type] += expense.Amount
	}

	var categories []models.ExpenseCategoryData
	for expenseType, amount := range categoryMap {
		categories = append(categories, models.ExpenseCategoryData{
			Type:   expenseType,
			Amount: amount,
		})
	}

	return categories, nil
}

// 私有方法：计算财务概要
func (s *financeService) calculateFinanceSummary(revenueData []models.RevenueData, expenseData []models.ExpenseData, startDate, endDate time.Time) *models.FinanceSummary {
	var totalRevenue, totalExpenses float64
	var totalOrders int64

	// 计算总收入
	for _, data := range revenueData {
		totalRevenue += data.Revenue
	}

	// 计算总支出
	for _, data := range expenseData {
		totalExpenses += data.Amount
	}

	// 从数据库获取真实的订单数量
	orderCount, err := s.orderRepo.GetOrderCountByDateRange(startDate, endDate)
	if err != nil {
		// 如果查询失败，使用0作为默认值
		totalOrders = 0
	} else {
		totalOrders = orderCount
	}

	// 计算其他指标
	avgOrderValue := float64(0)
	if totalOrders > 0 {
		avgOrderValue = totalRevenue / float64(totalOrders)
	}

	netProfit := totalRevenue - totalExpenses
	profitMargin := float64(0)
	if totalRevenue > 0 {
		profitMargin = (netProfit / totalRevenue) * 100
	}

	return &models.FinanceSummary{
		TotalRevenue:          totalRevenue,
		TotalOrders:           int(totalOrders),
		AvgOrderValue:         avgOrderValue,
		EstimatedProfit:       netProfit,
		TotalExpenses:         totalExpenses,
		NetProfit:             netProfit,
		ProfitMargin:          profitMargin,
		RevenueTrend:          5.2,  // 模拟数据
		OrdersTrend:           3.8,  // 模拟数据
		AvgOrderTrend:         1.5,  // 模拟数据
		ProfitTrend:           7.3,  // 模拟数据
		AvgUsageDuration:      2.5,  // 模拟数据
		CustomerRetentionRate: 68.5, // 模拟数据
		RoomUtilizationRate:   75.2, // 模拟数据
		RevenuePerSqm:         125.8, // 模拟数据
		PeakHourRevenueRatio:  62.3, // 模拟数据
	}
}

// 私有方法：生成收入图表数据
func (s *financeService) generateRevenueChart(revenueData []models.RevenueData, chartType string) []models.ChartData {
	var chartData []models.ChartData
	for _, data := range revenueData {
		chartData = append(chartData, models.ChartData{
			Date:    data.Date,
			Revenue: data.Revenue,
		})
	}
	return chartData
}

// 私有方法：生成支出趋势数据
func (s *financeService) generateExpenseTrend(expenseData []models.ExpenseData) []models.ExpenseTrendData {
	var trendData []models.ExpenseTrendData
	for _, data := range expenseData {
		trendData = append(trendData, models.ExpenseTrendData{
			Date:   data.Date,
			Amount: data.Amount,
		})
	}
	return trendData
}

// 私有方法：生成利润分析数据
func (s *financeService) generateProfitAnalysis(revenueData []models.RevenueData, expenseData []models.ExpenseData, analysisType string) []models.ProfitAnalysisData {
	// 简化实现，按月聚合数据
	monthlyData := make(map[string]*models.ProfitAnalysisData)

	// 聚合收入数据
	for _, data := range revenueData {
		month := data.Date[:7] // YYYY-MM
		if monthlyData[month] == nil {
			monthlyData[month] = &models.ProfitAnalysisData{
				Period: month,
			}
		}
		monthlyData[month].Revenue += data.Revenue
	}

	// 聚合支出数据
	for _, data := range expenseData {
		month := data.Date[:7] // YYYY-MM
		if monthlyData[month] == nil {
			monthlyData[month] = &models.ProfitAnalysisData{
				Period: month,
			}
		}
		monthlyData[month].Expense += data.Amount
	}

	// 计算利润和利润率
	var analysisData []models.ProfitAnalysisData
	for _, data := range monthlyData {
		data.Profit = data.Revenue - data.Expense
		if data.Revenue > 0 {
			data.ProfitMargin = (data.Profit / data.Revenue) * 100
		}
		analysisData = append(analysisData, *data)
	}

	return analysisData
}

// 私有方法：生成Excel报表
func (s *financeService) generateExcelReport(report *models.FinanceReportData, startDate, endDate time.Time) ([]byte, error) {
	// 简化实现，返回模拟的Excel数据
	// 实际应该使用Excel库生成真实的Excel文件
	excelContent := fmt.Sprintf("财务报表\n时间范围: %s 至 %s\n总收入: %.2f\n总支出: %.2f\n净利润: %.2f",
		startDate.Format("2006-01-02"),
		endDate.Format("2006-01-02"),
		report.Summary.TotalRevenue,
		report.Summary.TotalExpenses,
		report.Summary.NetProfit)

	return []byte(excelContent), nil
}
