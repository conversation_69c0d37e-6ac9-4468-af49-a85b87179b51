package services

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/repositories"
	"time"
)

// PackageService 套餐服务接口
type PackageService interface {
	// 套餐管理
	CreatePackage(req *models.PackageCreateRequest) (*models.Package, error)
	GetPackage(id int) (*models.Package, error)
	UpdatePackage(id int, req *models.PackageUpdateRequest) (*models.Package, error)
	DeletePackage(id int) error
	GetPackageList(page, pageSize int, filters map[string]interface{}) ([]*models.Package, int64, error)
	GetActivePackages(packageType string) ([]*models.Package, error)
	UpdatePackageStatus(id int, isActive bool) error
	GetPackageStats() (*models.PackageStatsResponse, error)
	
	// 套餐购买
	PurchasePackage(userID, packageID int, req *models.PackagePurchaseRequest) (*models.UserPackage, error)
	RechargePackage(userID int, packageID int, hours int, req *models.PackagePurchaseRequest) (*models.UserPackage, error)
}

// UserPackageService 用户套餐服务接口
type UserPackageService interface {
	GetUserPackages(userID int, status string, page, pageSize int) ([]*models.UserPackage, int64, error)
	GetUserPackage(id int) (*models.UserPackage, error)
	GetUserActivePackages(userID int) ([]*models.UserPackage, error)
	GetUserPackageStats(userID int) (*models.UserPackageStatsResponse, error)
	UsePackage(id int, req *models.UsePackageRequest) (*models.Order, error)
	GetUsageLogs(userPackageID int, page, pageSize int) ([]*models.PackageUsageLog, int64, error)
}

// packageService 套餐服务实现
type packageService struct {
	packageRepo     repositories.PackageRepository
	userPackageRepo repositories.UserPackageRepository
	userRepo        repositories.UserRepository
	orderRepo       repositories.OrderRepository
	roomRepo        repositories.RoomRepository
}

// NewPackageService 创建套餐服务
func NewPackageService(
	packageRepo repositories.PackageRepository,
	userPackageRepo repositories.UserPackageRepository,
	userRepo repositories.UserRepository,
	orderRepo repositories.OrderRepository,
	roomRepo repositories.RoomRepository,
) PackageService {
	return &packageService{
		packageRepo:     packageRepo,
		userPackageRepo: userPackageRepo,
		userRepo:        userRepo,
		orderRepo:       orderRepo,
		roomRepo:        roomRepo,
	}
}

// CreatePackage 创建套餐
func (s *packageService) CreatePackage(req *models.PackageCreateRequest) (*models.Package, error) {
	// 验证套餐数据
	if err := s.validatePackageRequest(req); err != nil {
		return nil, err
	}

	pkg := &models.Package{
		Name:             req.Name,
		Type:             req.Type,
		DurationHours:    req.DurationHours,
		OriginalPrice:    req.OriginalPrice,
		SalePrice:        req.SalePrice,
		Description:      req.Description,
		Features:         req.Features,
		ValidDays:        req.ValidDays,
		MinRechargeHours: req.MinRechargeHours,
		MaxRechargeHours: req.MaxRechargeHours,
		SortOrder:        req.SortOrder,
		IsActive:         true,
	}

	err := s.packageRepo.Create(pkg)
	if err != nil {
		return nil, fmt.Errorf("创建套餐失败: %v", err)
	}

	return pkg, nil
}

// GetPackage 获取套餐
func (s *packageService) GetPackage(id int) (*models.Package, error) {
	pkg, err := s.packageRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取套餐失败: %v", err)
	}
	if pkg == nil {
		return nil, fmt.Errorf("套餐不存在")
	}
	return pkg, nil
}

// UpdatePackage 更新套餐
func (s *packageService) UpdatePackage(id int, req *models.PackageUpdateRequest) (*models.Package, error) {
	// 获取现有套餐
	pkg, err := s.packageRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取套餐失败: %v", err)
	}
	if pkg == nil {
		return nil, fmt.Errorf("套餐不存在")
	}

	// 更新字段
	if req.Name != "" {
		pkg.Name = req.Name
	}
	if req.Type != "" {
		pkg.Type = req.Type
	}
	if req.DurationHours != nil {
		pkg.DurationHours = req.DurationHours
	}
	if req.OriginalPrice > 0 {
		pkg.OriginalPrice = req.OriginalPrice
	}
	if req.SalePrice > 0 {
		pkg.SalePrice = req.SalePrice
	}
	if req.Description != "" {
		pkg.Description = req.Description
	}
	if req.Features != nil {
		pkg.Features = req.Features
	}
	if req.ValidDays > 0 {
		pkg.ValidDays = req.ValidDays
	}
	if req.MinRechargeHours != nil {
		pkg.MinRechargeHours = req.MinRechargeHours
	}
	if req.MaxRechargeHours != nil {
		pkg.MaxRechargeHours = req.MaxRechargeHours
	}
	if req.SortOrder > 0 {
		pkg.SortOrder = req.SortOrder
	}
	if req.IsActive != nil {
		pkg.IsActive = *req.IsActive
	}

	err = s.packageRepo.Update(pkg)
	if err != nil {
		return nil, fmt.Errorf("更新套餐失败: %v", err)
	}

	return pkg, nil
}

// DeletePackage 删除套餐
func (s *packageService) DeletePackage(id int) error {
	// 检查是否有用户购买了此套餐
	// TODO: 实现检查逻辑

	err := s.packageRepo.Delete(id)
	if err != nil {
		return fmt.Errorf("删除套餐失败: %v", err)
	}
	return nil
}

// GetPackageList 获取套餐列表
func (s *packageService) GetPackageList(page, pageSize int, filters map[string]interface{}) ([]*models.Package, int64, error) {
	return s.packageRepo.GetList(page, pageSize, filters)
}

// GetActivePackages 获取有效套餐
func (s *packageService) GetActivePackages(packageType string) ([]*models.Package, error) {
	packages, err := s.packageRepo.GetActivePackages(packageType)
	if err != nil {
		return nil, fmt.Errorf("获取有效套餐失败: %v", err)
	}

	// 添加推荐标签等逻辑
	for _, pkg := range packages {
		// 可以根据销量、评分等添加推荐逻辑
		if pkg.SalesCount > 50 {
			// 设置为热门套餐的逻辑可以在响应层处理
		}
	}

	return packages, nil
}

// UpdatePackageStatus 更新套餐状态
func (s *packageService) UpdatePackageStatus(id int, isActive bool) error {
	return s.packageRepo.UpdateStatus(id, isActive)
}

// GetPackageStats 获取套餐统计
func (s *packageService) GetPackageStats() (*models.PackageStatsResponse, error) {
	return s.packageRepo.GetStats()
}

// PurchasePackage 购买套餐
func (s *packageService) PurchasePackage(userID, packageID int, req *models.PackagePurchaseRequest) (*models.UserPackage, error) {
	// 获取套餐信息
	pkg, err := s.packageRepo.GetByID(packageID)
	if err != nil {
		return nil, fmt.Errorf("获取套餐信息失败: %v", err)
	}
	if pkg == nil {
		return nil, fmt.Errorf("套餐不存在")
	}
	if !pkg.IsActive {
		return nil, fmt.Errorf("套餐已下架")
	}

	// 验证用户
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 计算套餐时长和价格
	var totalHours float64
	var price float64

	if pkg.Type == "fixed_duration" {
		if pkg.DurationHours == nil {
			return nil, fmt.Errorf("固定时长套餐缺少时长信息")
		}
		totalHours = float64(*pkg.DurationHours)
		price = pkg.SalePrice
	} else if pkg.Type == "flexible_recharge" {
		if req.Hours == nil {
			return nil, fmt.Errorf("灵活续费套餐需要指定时长")
		}
		if *req.Hours < *pkg.MinRechargeHours || *req.Hours > *pkg.MaxRechargeHours {
			return nil, fmt.Errorf("续费时长超出范围")
		}
		totalHours = float64(*req.Hours)
		price = pkg.SalePrice * totalHours
	}

	// 创建用户套餐
	userPackage := &models.UserPackage{
		UserID:         userID,
		PackageID:      packageID,
		TotalHours:     totalHours,
		UsedHours:      0,
		RemainingHours: totalHours,
		PurchasePrice:  price,
		Status:         "active",
		ExpiresAt:      time.Now().AddDate(0, 0, pkg.ValidDays),
		ActivatedAt:    &time.Time{},
	}
	*userPackage.ActivatedAt = time.Now()

	err = s.userPackageRepo.Create(userPackage)
	if err != nil {
		return nil, fmt.Errorf("创建用户套餐失败: %v", err)
	}

	// TODO: 集成支付系统
	// 这里应该创建支付订单，等待支付完成后再激活套餐

	return userPackage, nil
}

// RechargePackage 续费套餐
func (s *packageService) RechargePackage(userID int, packageID int, hours int, req *models.PackagePurchaseRequest) (*models.UserPackage, error) {
	// 获取灵活续费套餐
	pkg, err := s.packageRepo.GetByID(packageID)
	if err != nil {
		return nil, fmt.Errorf("获取套餐信息失败: %v", err)
	}
	if pkg == nil {
		return nil, fmt.Errorf("套餐不存在")
	}
	if pkg.Type != "flexible_recharge" {
		return nil, fmt.Errorf("只有灵活续费套餐支持续费")
	}

	// 验证续费时长
	if hours < *pkg.MinRechargeHours || hours > *pkg.MaxRechargeHours {
		return nil, fmt.Errorf("续费时长超出范围")
	}

	// 创建续费请求
	req.Hours = &hours
	return s.PurchasePackage(userID, packageID, req)
}

// validatePackageRequest 验证套餐请求
func (s *packageService) validatePackageRequest(req *models.PackageCreateRequest) error {
	if req.Type == "fixed_duration" {
		if req.DurationHours == nil || *req.DurationHours <= 0 {
			return fmt.Errorf("固定时长套餐必须指定有效的时长")
		}
	} else if req.Type == "flexible_recharge" {
		if req.MinRechargeHours == nil || req.MaxRechargeHours == nil {
			return fmt.Errorf("灵活续费套餐必须指定续费时长范围")
		}
		if *req.MinRechargeHours >= *req.MaxRechargeHours {
			return fmt.Errorf("最小续费时长必须小于最大续费时长")
		}
	}

	if req.SalePrice > req.OriginalPrice {
		return fmt.Errorf("售价不能高于原价")
	}

	return nil
}

// userPackageService 用户套餐服务实现
type userPackageService struct {
	userPackageRepo     repositories.UserPackageRepository
	packageUsageLogRepo repositories.PackageUsageLogRepository
	userRepo            repositories.UserRepository
	roomRepo            repositories.RoomRepository
	orderRepo           repositories.OrderRepository
}

// NewUserPackageService 创建用户套餐服务
func NewUserPackageService(
	userPackageRepo repositories.UserPackageRepository,
	packageUsageLogRepo repositories.PackageUsageLogRepository,
	userRepo repositories.UserRepository,
	roomRepo repositories.RoomRepository,
	orderRepo repositories.OrderRepository,
) UserPackageService {
	return &userPackageService{
		userPackageRepo:     userPackageRepo,
		packageUsageLogRepo: packageUsageLogRepo,
		userRepo:            userRepo,
		roomRepo:            roomRepo,
		orderRepo:           orderRepo,
	}
}

// GetUserPackages 获取用户套餐列表
func (s *userPackageService) GetUserPackages(userID int, status string, page, pageSize int) ([]*models.UserPackage, int64, error) {
	return s.userPackageRepo.GetUserPackages(userID, status, page, pageSize)
}

// GetUserPackage 获取用户套餐详情
func (s *userPackageService) GetUserPackage(id int) (*models.UserPackage, error) {
	userPackage, err := s.userPackageRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取用户套餐失败: %v", err)
	}
	if userPackage == nil {
		return nil, fmt.Errorf("用户套餐不存在")
	}
	return userPackage, nil
}

// GetUserActivePackages 获取用户有效套餐
func (s *userPackageService) GetUserActivePackages(userID int) ([]*models.UserPackage, error) {
	return s.userPackageRepo.GetUserActivePackages(userID)
}

// GetUserPackageStats 获取用户套餐统计
func (s *userPackageService) GetUserPackageStats(userID int) (*models.UserPackageStatsResponse, error) {
	return s.userPackageRepo.GetUserStats(userID)
}

// UsePackage 使用套餐开房
func (s *userPackageService) UsePackage(id int, req *models.UsePackageRequest) (*models.Order, error) {
	// 获取用户套餐
	userPackage, err := s.userPackageRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取用户套餐失败: %v", err)
	}
	if userPackage == nil {
		return nil, fmt.Errorf("用户套餐不存在")
	}

	// 检查套餐是否可用
	if !userPackage.CanUse() {
		return nil, fmt.Errorf("套餐不可用")
	}

	// 检查房间是否可用
	room, err := s.roomRepo.GetByID(req.RoomID)
	if err != nil {
		return nil, fmt.Errorf("获取房间信息失败: %v", err)
	}
	if room == nil {
		return nil, fmt.Errorf("房间不存在")
	}
	if room.Status != "available" {
		return nil, fmt.Errorf("房间不可用")
	}

	// 创建订单
	order := &models.Order{
		UserID:            &userPackage.UserID,
		RoomID:            &req.RoomID,
		PackageType:       "package",
		UserPackageID:     &userPackage.ID,
		PackageHoursUsed:  req.EstimatedHours,
		PaymentMethod:     "package",
		Status:            "active",
		StartTime:         time.Now(),
	}

	err = s.orderRepo.Create(order)
	if err != nil {
		return nil, fmt.Errorf("创建订单失败: %v", err)
	}

	// 使用套餐时长
	err = s.userPackageRepo.UsePackage(id, req.EstimatedHours)
	if err != nil {
		return nil, fmt.Errorf("使用套餐失败: %v", err)
	}

	// 记录使用日志
	usageLog := &models.PackageUsageLog{
		UserPackageID: id,
		OrderID:       order.ID,
		HoursUsed:     req.EstimatedHours,
		HoursBefore:   userPackage.RemainingHours,
		HoursAfter:    userPackage.RemainingHours - req.EstimatedHours,
		RoomID:        req.RoomID,
		StartedAt:     time.Now(),
	}

	err = s.packageUsageLogRepo.Create(usageLog)
	if err != nil {
		// 记录日志失败不影响主流程
		fmt.Printf("记录套餐使用日志失败: %v\n", err)
	}

	// 更新房间状态
	err = s.roomRepo.UpdateStatus(req.RoomID, "occupied")
	if err != nil {
		return nil, fmt.Errorf("更新房间状态失败: %v", err)
	}

	return order, nil
}

// GetUsageLogs 获取套餐使用记录
func (s *userPackageService) GetUsageLogs(userPackageID int, page, pageSize int) ([]*models.PackageUsageLog, int64, error) {
	return s.packageUsageLogRepo.GetByUserPackageID(userPackageID, page, pageSize)
}
