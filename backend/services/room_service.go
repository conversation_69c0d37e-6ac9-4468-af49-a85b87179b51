package services

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/repositories"
)

// RoomService 房间服务接口
type RoomService interface {
	CreateRoom(req *models.RoomCreateRequest) (*models.RoomResponse, error)
	GetRoom(id int) (*models.RoomResponse, error)
	GetRoomByNumber(roomNumber string) (*models.RoomResponse, error)
	UpdateRoom(id int, req *models.RoomUpdateRequest) (*models.RoomResponse, error)
	DeleteRoom(id int) error
	GetRoomList(page, pageSize int, filters map[string]interface{}) ([]*models.RoomResponse, int64, error)
	GetAvailableRooms() ([]*models.RoomResponse, error)
	UpdateRoomStatus(id int, status string) error
	GetRoomWithDevices(id int) (*models.RoomResponse, error)
	GetRoomStatistics() (map[string]int, error)
}

// roomService 房间服务实现
type roomService struct {
	roomRepo       repositories.RoomRepository
	deviceRepo     repositories.DeviceRepository
	pricingRepo    repositories.PricingRuleRepository
	orderRepo      repositories.OrderRepository
}

// NewRoomService 创建房间服务
func NewRoomService(roomRepo repositories.RoomRepository, deviceRepo repositories.DeviceRepository, 
	pricingRepo repositories.PricingRuleRepository, orderRepo repositories.OrderRepository) RoomService {
	return &roomService{
		roomRepo:    roomRepo,
		deviceRepo:  deviceRepo,
		pricingRepo: pricingRepo,
		orderRepo:   orderRepo,
	}
}

// CreateRoom 创建房间
func (s *roomService) CreateRoom(req *models.RoomCreateRequest) (*models.RoomResponse, error) {
	// 检查房间号是否已存在
	existingRoom, err := s.roomRepo.GetByRoomNumber(req.RoomNumber)
	if err != nil {
		return nil, fmt.Errorf("检查房间号失败: %v", err)
	}
	if existingRoom != nil {
		return nil, fmt.Errorf("房间号已存在")
	}

	// 检查计费规则是否存在
	pricingRule, err := s.pricingRepo.GetByID(req.PricingRuleID)
	if err != nil {
		return nil, fmt.Errorf("检查计费规则失败: %v", err)
	}
	if pricingRule == nil {
		return nil, fmt.Errorf("计费规则不存在")
	}

	// 创建房间
	room := &models.Room{
		RoomNumber:    req.RoomNumber,
		Name:          req.Name,
		Description:   req.Description,
		Status:        models.RoomStatusAvailable,
		PricingRuleID: req.PricingRuleID,
	}

	err = s.roomRepo.Create(room)
	if err != nil {
		return nil, fmt.Errorf("创建房间失败: %v", err)
	}

	return s.buildRoomResponse(room, pricingRule, nil)
}

// GetRoom 获取房间信息
func (s *roomService) GetRoom(id int) (*models.RoomResponse, error) {
	room, err := s.roomRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取房间信息失败: %v", err)
	}
	if room == nil {
		return nil, fmt.Errorf("房间不存在")
	}

	// 获取计费规则
	pricingRule, err := s.pricingRepo.GetByID(room.PricingRuleID)
	if err != nil {
		return nil, fmt.Errorf("获取计费规则失败: %v", err)
	}

	return s.buildRoomResponse(room, pricingRule, nil)
}

// GetRoomByNumber 根据房间号获取房间信息
func (s *roomService) GetRoomByNumber(roomNumber string) (*models.RoomResponse, error) {
	room, err := s.roomRepo.GetByRoomNumber(roomNumber)
	if err != nil {
		return nil, fmt.Errorf("获取房间信息失败: %v", err)
	}
	if room == nil {
		return nil, fmt.Errorf("房间不存在")
	}

	// 获取计费规则
	pricingRule, err := s.pricingRepo.GetByID(room.PricingRuleID)
	if err != nil {
		return nil, fmt.Errorf("获取计费规则失败: %v", err)
	}

	return s.buildRoomResponse(room, pricingRule, nil)
}

// UpdateRoom 更新房间信息
func (s *roomService) UpdateRoom(id int, req *models.RoomUpdateRequest) (*models.RoomResponse, error) {
	room, err := s.roomRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取房间信息失败: %v", err)
	}
	if room == nil {
		return nil, fmt.Errorf("房间不存在")
	}

	// 更新房间信息
	if req.Name != "" {
		room.Name = req.Name
	}
	if req.Description != "" {
		room.Description = req.Description
	}
	if req.Status != "" {
		room.Status = req.Status
	}
	if req.PricingRuleID != 0 {
		// 检查计费规则是否存在
		pricingRule, err := s.pricingRepo.GetByID(req.PricingRuleID)
		if err != nil {
			return nil, fmt.Errorf("检查计费规则失败: %v", err)
		}
		if pricingRule == nil {
			return nil, fmt.Errorf("计费规则不存在")
		}
		room.PricingRuleID = req.PricingRuleID
	}

	err = s.roomRepo.Update(room)
	if err != nil {
		return nil, fmt.Errorf("更新房间失败: %v", err)
	}

	// 获取计费规则
	pricingRule, err := s.pricingRepo.GetByID(room.PricingRuleID)
	if err != nil {
		return nil, fmt.Errorf("获取计费规则失败: %v", err)
	}

	return s.buildRoomResponse(room, pricingRule, nil)
}

// DeleteRoom 删除房间
func (s *roomService) DeleteRoom(id int) error {
	// 检查房间是否存在
	room, err := s.roomRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("获取房间信息失败: %v", err)
	}
	if room == nil {
		return fmt.Errorf("房间不存在")
	}

	// 检查房间是否有活跃订单
	activeOrder, err := s.orderRepo.GetActiveOrderByRoom(id)
	if err != nil {
		return fmt.Errorf("检查房间订单失败: %v", err)
	}
	if activeOrder != nil {
		return fmt.Errorf("房间有活跃订单，无法删除。请先完成或取消房间内的订单")
	}

	// 检查房间是否有关联的设备、预约或外卖订单
	// 这里我们提供更详细的错误信息，让用户知道具体的阻止原因
	if err := s.checkRoomDependencies(id); err != nil {
		return err
	}

	// 删除房间
	err = s.roomRepo.Delete(id)
	if err != nil {
		// 如果是外键约束错误，提供更友好的错误信息
		if err.Error() == "删除房间失败: FOREIGN KEY constraint failed" {
			return fmt.Errorf("无法删除房间：房间仍有关联数据。请先删除相关的设备、预约或订单记录")
		}
		return fmt.Errorf("删除房间失败: %v", err)
	}

	return nil
}

// checkRoomDependencies 检查房间的依赖关系
func (s *roomService) checkRoomDependencies(roomID int) error {
	// 注意：历史订单记录（已完成、已取消的订单）不应该阻止房间删除
	// 只有活跃订单才会阻止删除，这个检查已经在上面的 GetActiveOrderByRoom 中完成了

	// 这里可以添加其他依赖检查（设备、预约等）
	// 但不检查历史订单记录，因为历史数据不应该阻止房间删除

	return nil
}

// GetRoomList 获取房间列表
func (s *roomService) GetRoomList(page, pageSize int, filters map[string]interface{}) ([]*models.RoomResponse, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()

	// 构建过滤条件
	if roomNumber, ok := filters["room_number"]; ok && roomNumber != "" {
		filterParams.AddCondition("room_number LIKE ?", "%"+roomNumber.(string)+"%")
	}
	if name, ok := filters["name"]; ok && name != "" {
		filterParams.AddCondition("name LIKE ?", "%"+name.(string)+"%")
	}
	if status, ok := filters["status"]; ok && status != "" {
		filterParams.AddCondition("status = ?", status)
	}
	if pricingRuleID, ok := filters["pricing_rule_id"]; ok && pricingRuleID != 0 {
		filterParams.AddCondition("pricing_rule_id = ?", pricingRuleID)
	}

	rooms, total, err := s.roomRepo.List(pagination, filterParams)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*models.RoomResponse, len(rooms))
	for i, room := range rooms {
		// 获取计费规则
		pricingRule, _ := s.pricingRepo.GetByID(room.PricingRuleID)
		response, _ := s.buildRoomResponse(room, pricingRule, nil)
		responses[i] = response
	}

	return responses, total, nil
}

// GetAvailableRooms 获取可用房间列表
func (s *roomService) GetAvailableRooms() ([]*models.RoomResponse, error) {
	rooms, err := s.roomRepo.GetAvailableRooms()
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	responses := make([]*models.RoomResponse, len(rooms))
	for i, room := range rooms {
		// 获取计费规则
		pricingRule, _ := s.pricingRepo.GetByID(room.PricingRuleID)
		response, _ := s.buildRoomResponse(room, pricingRule, nil)
		responses[i] = response
	}

	return responses, nil
}

// UpdateRoomStatus 更新房间状态
func (s *roomService) UpdateRoomStatus(id int, status string) error {
	// 验证状态值
	if status != models.RoomStatusAvailable && status != models.RoomStatusOccupied && status != models.RoomStatusMaintenance {
		return fmt.Errorf("无效的房间状态")
	}

	return s.roomRepo.UpdateStatus(id, status)
}

// GetRoomWithDevices 获取房间及其设备信息
func (s *roomService) GetRoomWithDevices(id int) (*models.RoomResponse, error) {
	room, devices, err := s.roomRepo.GetRoomWithDevices(id)
	if err != nil {
		return nil, err
	}
	if room == nil {
		return nil, fmt.Errorf("房间不存在")
	}

	// 获取计费规则
	pricingRule, err := s.pricingRepo.GetByID(room.PricingRuleID)
	if err != nil {
		return nil, fmt.Errorf("获取计费规则失败: %v", err)
	}

	return s.buildRoomResponse(room, pricingRule, devices)
}

// GetRoomStatistics 获取房间统计信息
func (s *roomService) GetRoomStatistics() (map[string]int, error) {
	// 获取房间状态统计
	filters := repositories.NewFilterParams()
	
	// 总房间数
	allRooms, total, err := s.roomRepo.List(repositories.NewPaginationParams(1, 1000), filters)
	if err != nil {
		return nil, fmt.Errorf("获取房间总数失败: %v", err)
	}

	statistics := map[string]int{
		"total":       int(total),
		"available":   0,
		"occupied":    0,
		"maintenance": 0,
	}

	// 统计各状态房间数量
	for _, room := range allRooms {
		switch room.Status {
		case models.RoomStatusAvailable:
			statistics["available"]++
		case models.RoomStatusOccupied:
			statistics["occupied"]++
		case models.RoomStatusMaintenance:
			statistics["maintenance"]++
		}
	}

	return statistics, nil
}

// buildRoomResponse 构建房间响应数据
func (s *roomService) buildRoomResponse(room *models.Room, pricingRule *models.PricingRule, devices []models.Device) (*models.RoomResponse, error) {
	response := room.ToResponse()
	response.PricingRule = pricingRule
	
	if devices != nil {
		response.Devices = devices
	}

	return response, nil
}
