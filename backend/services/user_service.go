package services

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/repositories"
	"mahjong-system/utils"
	"time"
)

// stringPtr 将string转换为*string的辅助函数
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// UserService 用户服务接口
type UserService interface {
	Register(req *models.UserRegisterRequest) (*models.User, error)
	Login(req *models.UserLoginRequest) (*models.User, error)
	GetProfile(userID int) (*models.UserResponse, error)
	UpdateProfile(userID int, req *models.UserUpdateRequest) (*models.UserResponse, error)
	Recharge(userID int, req *models.UserRechargeRequest) error
	GetBalanceRecords(userID int, page, pageSize int) ([]*models.BalanceRecord, int64, error)
	GetUserList(page, pageSize int, filters map[string]interface{}) ([]*models.UserResponse, int64, error)
	GetUserStats() (*models.UserStatistics, error)
	DeductBalance(userID int, amount float64, description string) error
	RefundBalance(userID int, amount float64, description string) error
}

// userService 用户服务实现
type userService struct {
	userRepo repositories.UserRepository
}

// NewUserService 创建用户服务
func NewUserService(userRepo repositories.UserRepository) UserService {
	return &userService{
		userRepo: userRepo,
	}
}

// Register 用户注册
func (s *userService) Register(req *models.UserRegisterRequest) (*models.User, error) {
	// 检查用户是否已存在
	existingUser, err := s.userRepo.GetByOpenID(req.OpenID)
	if err != nil {
		return nil, fmt.Errorf("检查用户是否存在失败: %v", err)
	}
	if existingUser != nil {
		return existingUser, nil // 用户已存在，直接返回
	}

	// 创建新用户
	user := &models.User{
		OpenID:    req.OpenID,
		Nickname:  stringPtr(req.Nickname),
		AvatarURL: stringPtr(req.AvatarURL),
		Phone:     stringPtr(req.Phone),
		Balance:   0.0,
	}

	err = s.userRepo.Create(user)
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	return user, nil
}

// Login 用户登录
func (s *userService) Login(req *models.UserLoginRequest) (*models.User, error) {
	user, err := s.userRepo.GetByOpenID(req.OpenID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	return user, nil
}

// GetProfile 获取用户资料
func (s *userService) GetProfile(userID int) (*models.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	return user.ToResponse(), nil
}

// UpdateProfile 更新用户资料
func (s *userService) UpdateProfile(userID int, req *models.UserUpdateRequest) (*models.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 更新用户信息
	if req.Nickname != "" {
		user.Nickname = stringPtr(req.Nickname)
	}
	if req.AvatarURL != "" {
		user.AvatarURL = stringPtr(req.AvatarURL)
	}
	if req.Phone != "" {
		user.Phone = stringPtr(req.Phone)
	}

	err = s.userRepo.Update(user)
	if err != nil {
		return nil, fmt.Errorf("更新用户信息失败: %v", err)
	}

	return user.ToResponse(), nil
}

// Recharge 用户充值
func (s *userService) Recharge(userID int, req *models.UserRechargeRequest) error {
	// 获取用户当前信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 更新余额
	err = s.userRepo.UpdateBalance(userID, req.Amount)
	if err != nil {
		return fmt.Errorf("更新用户余额失败: %v", err)
	}

	// 记录余额变动
	record := &models.BalanceRecord{
		UserID:      userID,
		Type:        "recharge",
		Amount:      req.Amount,
		Balance:     user.Balance + req.Amount,
		Description: fmt.Sprintf("用户充值 %.2f 元", req.Amount),
	}

	err = s.userRepo.CreateBalanceRecord(record)
	if err != nil {
		return fmt.Errorf("创建余额记录失败: %v", err)
	}

	return nil
}

// GetBalanceRecords 获取余额变动记录
func (s *userService) GetBalanceRecords(userID int, page, pageSize int) ([]*models.BalanceRecord, int64, error) {
	pagination := repositories.NewPaginationParams(page, pageSize)
	return s.userRepo.GetBalanceRecords(userID, pagination)
}

// GetUserList 获取用户列表（优化版本，带缓存）
func (s *userService) GetUserList(page, pageSize int, filters map[string]interface{}) ([]*models.UserResponse, int64, error) {
	// 生成缓存键
	cacheKey := utils.GlobalCache.GenerateKey(utils.CacheKeyUsers, "list", page, pageSize, filters)

	// 尝试从缓存获取
	var cachedResult struct {
		Users []*models.UserResponse `json:"users"`
		Total int64                  `json:"total"`
	}

	if found, err := utils.GlobalCache.GetJSON(cacheKey, &cachedResult); err == nil && found {
		return cachedResult.Users, cachedResult.Total, nil
	}

	pagination := repositories.NewPaginationParams(page, pageSize)
	filterParams := repositories.NewFilterParams()

	// 构建过滤条件
	if nickname, ok := filters["nickname"]; ok && nickname != "" {
		filterParams.AddCondition("u.nickname LIKE ?", "%"+nickname.(string)+"%")
	}
	if phone, ok := filters["phone"]; ok && phone != "" {
		filterParams.AddCondition("u.phone LIKE ?", "%"+phone.(string)+"%")
	}
	if startDate, ok := filters["start_date"]; ok && startDate != "" {
		filterParams.AddCondition("DATE(u.created_at) >= ?", startDate)
	}
	if endDate, ok := filters["end_date"]; ok && endDate != "" {
		filterParams.AddCondition("DATE(u.created_at) <= ?", endDate)
	}

	// 使用优化的查询方法，避免N+1查询问题
	usersWithStats, total, err := s.userRepo.GetUsersWithOrderStats(pagination, filterParams)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*models.UserResponse, len(usersWithStats))
	for i, userWithStats := range usersWithStats {
		response := userWithStats.ToResponseWithStats()
		// 暂时设置LastLoginAt为nil，后续可以添加登录记录表
		response.LastLoginAt = nil
		responses[i] = response
	}

	// 缓存结果（如果没有搜索条件，缓存时间长一些）
	cacheDuration := utils.CacheShort
	if len(filters) == 0 {
		cacheDuration = utils.CacheMedium
	}

	cacheData := struct {
		Users []*models.UserResponse `json:"users"`
		Total int64                  `json:"total"`
	}{
		Users: responses,
		Total: total,
	}

	utils.GlobalCache.SetJSON(cacheKey, cacheData, cacheDuration)

	return responses, total, nil
}

// GetUserStats 获取用户统计信息（带缓存）
func (s *userService) GetUserStats() (*models.UserStatistics, error) {
	// 生成缓存键
	cacheKey := utils.GlobalCache.GenerateKey(utils.CacheKeyStats, "users", time.Now().Format("2006-01-02"))

	// 尝试从缓存获取
	var cachedStats models.UserStatistics
	if found, err := utils.GlobalCache.GetJSON(cacheKey, &cachedStats); err == nil && found {
		return &cachedStats, nil
	}

	// 获取总用户数
	var totalUsers int64
	err := s.userRepo.GetDB().Get(&totalUsers, "SELECT COUNT(*) FROM users")
	if err != nil {
		return nil, fmt.Errorf("获取总用户数失败: %v", err)
	}

	// 获取今日新增用户数
	today := time.Now().Format("2006-01-02")
	var newUsersToday int64
	err = s.userRepo.GetDB().Get(&newUsersToday, "SELECT COUNT(*) FROM users WHERE DATE(created_at) = ?", today)
	if err != nil {
		return nil, fmt.Errorf("获取今日新增用户数失败: %v", err)
	}

	// 获取活跃用户数（最近7天有订单的用户）
	var activeUsers int64
	err = s.userRepo.GetDB().Get(&activeUsers, `
		SELECT COUNT(DISTINCT user_id)
		FROM orders
		WHERE created_at >= datetime('now', '-7 days')
	`)
	if err != nil {
		return nil, fmt.Errorf("获取活跃用户数失败: %v", err)
	}

	// 构建用户增长图表数据（最近7天）
	userGrowthChart := make([]models.UserGrowthData, 0)
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		var count int64
		err := s.userRepo.GetDB().Get(&count, "SELECT COUNT(*) FROM users WHERE DATE(created_at) = ?", date)
		if err != nil {
			continue
		}
		userGrowthChart = append(userGrowthChart, models.UserGrowthData{
			Date:  date,
			Count: int(count),
		})
	}

	// 构建用户活动图表数据（最近7天的订单数）
	userActivityChart := make([]models.UserActivityData, 0)
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		var count int64
		err := s.userRepo.GetDB().Get(&count, "SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?", date)
		if err != nil {
			continue
		}
		userActivityChart = append(userActivityChart, models.UserActivityData{
			Date:   date,
			Orders: int(count),
		})
	}

	stats := &models.UserStatistics{
		TotalUsers:        int(totalUsers),
		NewUsersToday:     int(newUsersToday),
		ActiveUsers:       int(activeUsers),
		UserGrowthChart:   userGrowthChart,
		UserActivityChart: userActivityChart,
	}

	// 缓存统计结果（缓存10分钟）
	utils.GlobalCache.SetJSON(cacheKey, stats, 10*time.Minute)

	return stats, nil
}

// DeductBalance 扣除用户余额
func (s *userService) DeductBalance(userID int, amount float64, description string) error {
	// 获取用户当前信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 检查余额是否足够
	if user.Balance < amount {
		return fmt.Errorf("用户余额不足")
	}

	// 扣除余额
	err = s.userRepo.UpdateBalance(userID, -amount)
	if err != nil {
		return fmt.Errorf("扣除用户余额失败: %v", err)
	}

	// 记录余额变动
	record := &models.BalanceRecord{
		UserID:      userID,
		Type:        "consume",
		Amount:      -amount,
		Balance:     user.Balance - amount,
		Description: description,
	}

	err = s.userRepo.CreateBalanceRecord(record)
	if err != nil {
		return fmt.Errorf("创建余额记录失败: %v", err)
	}

	return nil
}

// RefundBalance 退款给用户
func (s *userService) RefundBalance(userID int, amount float64, description string) error {
	// 获取用户当前信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 退款
	err = s.userRepo.UpdateBalance(userID, amount)
	if err != nil {
		return fmt.Errorf("退款失败: %v", err)
	}

	// 记录余额变动
	record := &models.BalanceRecord{
		UserID:      userID,
		Type:        "refund",
		Amount:      amount,
		Balance:     user.Balance + amount,
		Description: description,
	}

	err = s.userRepo.CreateBalanceRecord(record)
	if err != nil {
		return fmt.Errorf("创建余额记录失败: %v", err)
	}

	return nil
}
