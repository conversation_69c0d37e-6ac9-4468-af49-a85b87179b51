package services

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/repositories"
)

// PricingRuleService 计费规则服务接口
type PricingRuleService interface {
	CreatePricingRule(req *models.PricingRuleCreateRequest) (*models.PricingRule, error)
	GetPricingRule(id int) (*models.PricingRule, error)
	UpdatePricingRule(id int, req *models.PricingRuleUpdateRequest) (*models.PricingRule, error)
	DeletePricingRule(id int) error
	GetPricingRuleList(page, pageSize int) ([]*models.PricingRule, int64, error)
}

// pricingRuleService 计费规则服务实现
type pricingRuleService struct {
	pricingRuleRepo repositories.PricingRuleRepository
}

// NewPricingRuleService 创建计费规则服务
func NewPricingRuleService(pricingRuleRepo repositories.PricingRuleRepository) PricingRuleService {
	return &pricingRuleService{
		pricingRuleRepo: pricingRuleRepo,
	}
}

// CreatePricingRule 创建计费规则
func (s *pricingRuleService) CreatePricingRule(req *models.PricingRuleCreateRequest) (*models.PricingRule, error) {
	// 创建计费规则
	rule := &models.PricingRule{
		Name:         req.Name,
		PricePerHour: req.PricePerHour,
		IsWeekend:    req.IsWeekend,
		IsHoliday:    req.IsHoliday,
	}

	// 处理可选字段
	if req.OvernightPrice > 0 {
		rule.OvernightPrice = &req.OvernightPrice
	}
	if req.StartTime != "" {
		rule.StartTime = &req.StartTime
	}
	if req.EndTime != "" {
		rule.EndTime = &req.EndTime
	}

	err := s.pricingRuleRepo.Create(rule)
	if err != nil {
		return nil, fmt.Errorf("创建计费规则失败: %v", err)
	}

	return rule, nil
}

// GetPricingRule 获取计费规则信息
func (s *pricingRuleService) GetPricingRule(id int) (*models.PricingRule, error) {
	rule, err := s.pricingRuleRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取计费规则信息失败: %v", err)
	}
	if rule == nil {
		return nil, fmt.Errorf("计费规则不存在")
	}

	return rule, nil
}

// UpdatePricingRule 更新计费规则信息
func (s *pricingRuleService) UpdatePricingRule(id int, req *models.PricingRuleUpdateRequest) (*models.PricingRule, error) {
	rule, err := s.pricingRuleRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取计费规则信息失败: %v", err)
	}
	if rule == nil {
		return nil, fmt.Errorf("计费规则不存在")
	}

	// 更新计费规则信息
	if req.Name != "" {
		rule.Name = req.Name
	}
	if req.PricePerHour > 0 {
		rule.PricePerHour = req.PricePerHour
	}
	if req.OvernightPrice > 0 {
		rule.OvernightPrice = &req.OvernightPrice
	}
	if req.StartTime != "" {
		rule.StartTime = &req.StartTime
	}
	if req.EndTime != "" {
		rule.EndTime = &req.EndTime
	}
	rule.IsWeekend = req.IsWeekend
	rule.IsHoliday = req.IsHoliday

	err = s.pricingRuleRepo.Update(rule)
	if err != nil {
		return nil, fmt.Errorf("更新计费规则失败: %v", err)
	}

	return rule, nil
}

// DeletePricingRule 删除计费规则
func (s *pricingRuleService) DeletePricingRule(id int) error {
	rule, err := s.pricingRuleRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("获取计费规则信息失败: %v", err)
	}
	if rule == nil {
		return fmt.Errorf("计费规则不存在")
	}

	err = s.pricingRuleRepo.Delete(id)
	if err != nil {
		return fmt.Errorf("删除计费规则失败: %v", err)
	}

	return nil
}

// GetPricingRuleList 获取计费规则列表
func (s *pricingRuleService) GetPricingRuleList(page, pageSize int) ([]*models.PricingRule, int64, error) {
	pagination := &repositories.PaginationParams{
		Page:     page,
		PageSize: pageSize,
		Offset:   (page - 1) * pageSize,
	}

	rules, total, err := s.pricingRuleRepo.List(pagination, nil)
	if err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}
