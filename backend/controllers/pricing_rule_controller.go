package controllers

import (
	"mahjong-system/models"
	"mahjong-system/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PricingRuleController 计费规则控制器
type PricingRuleController struct {
	pricingRuleService services.PricingRuleService
}

// NewPricingRuleController 创建计费规则控制器
func NewPricingRuleController(pricingRuleService services.PricingRuleService) *PricingRuleController {
	return &PricingRuleController{
		pricingRuleService: pricingRuleService,
	}
}

// GetPricingRuleList 获取计费规则列表
func (c *PricingRuleController) GetPricingRuleList(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	rules, total, err := c.pricingRuleService.GetPricingRuleList(page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := &models.PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
		Data:       rules,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetPricingRule 获取计费规则详情
func (c *PricingRuleController) GetPricingRule(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "计费规则ID格式错误"))
		return
	}

	rule, err := c.pricingRuleService.GetPricingRule(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(rule))
}

// CreatePricingRule 创建计费规则（管理端）
func (c *PricingRuleController) CreatePricingRule(ctx *gin.Context) {
	var req models.PricingRuleCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	rule, err := c.pricingRuleService.CreatePricingRule(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(rule))
}

// UpdatePricingRule 更新计费规则（管理端）
func (c *PricingRuleController) UpdatePricingRule(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "计费规则ID格式错误"))
		return
	}

	var req models.PricingRuleUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	rule, err := c.pricingRuleService.UpdatePricingRule(id, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(rule))
}

// DeletePricingRule 删除计费规则（管理端）
func (c *PricingRuleController) DeletePricingRule(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "计费规则ID格式错误"))
		return
	}

	err = c.pricingRuleService.DeletePricingRule(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "删除成功"}))
}
