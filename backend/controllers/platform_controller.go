package controllers

import (
	"net/http"
	"strconv"

	"mahjong-system/models"
	"mahjong-system/services"

	"github.com/gin-gonic/gin"
)

// PlatformController 平台订单控制器
type PlatformController struct {
	platformOrderService services.PlatformOrderService
}

// NewPlatformController 创建平台订单控制器
func NewPlatformController(platformOrderService services.PlatformOrderService) *PlatformController {
	return &PlatformController{
		platformOrderService: platformOrderService,
	}
}

// GetPlatformOrders 获取平台订单列表
func (c *PlatformController) GetPlatformOrders(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	// 获取筛选参数
	filters := make(map[string]interface{})
	if platformType := ctx.Query("platformType"); platformType != "" {
		filters["platform_type"] = platformType
	}
	if orderStatus := ctx.Query("orderStatus"); orderStatus != "" {
		filters["order_status"] = orderStatus
	}
	if verificationStatus := ctx.Query("verificationStatus"); verificationStatus != "" {
		filters["verification_status"] = verificationStatus
	}
	if roomNumber := ctx.Query("roomNumber"); roomNumber != "" {
		filters["room_number"] = roomNumber
	}
	if platformOrderId := ctx.Query("platformOrderId"); platformOrderId != "" {
		filters["platform_order_id"] = platformOrderId
	}
	if startDate := ctx.Query("start_date"); startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate := ctx.Query("end_date"); endDate != "" {
		filters["end_date"] = endDate
	}

	orders, total, err := c.platformOrderService.GetPlatformOrderList(page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 获取统计数据
	stats, err := c.platformOrderService.GetPlatformOrderStats(filters)
	if err != nil {
		// 如果获取统计数据失败，使用默认值
		stats = &models.PlatformOrderStats{
			Meituan:  models.PlatformStats{Count: 0, Amount: 0},
			Eleme:    models.PlatformStats{Count: 0, Amount: 0},
			Pending:  models.PlatformStats{Count: 0, Amount: 0},
			Verified: models.PlatformStats{Count: 0, Amount: 0},
		}
	}

	response := gin.H{
		"page":        page,
		"page_size":   pageSize,
		"total":       total,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		"data":        orders,
		"stats":       stats,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetPlatformOrder 获取平台订单详情
func (c *PlatformController) GetPlatformOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	order, err := c.platformOrderService.GetPlatformOrder(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(order))
}

// VerifyOrder 核销订单
func (c *PlatformController) VerifyOrder(ctx *gin.Context) {
	var req models.PlatformOrderVerifyRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err := c.platformOrderService.VerifyOrder(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "订单核销成功"}))
}

// BatchVerifyOrders 批量核销订单
func (c *PlatformController) BatchVerifyOrders(ctx *gin.Context) {
	var req models.BatchVerifyRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err := c.platformOrderService.BatchVerifyOrders(req.OrderIds)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "批量核销成功",
		"count":   len(req.OrderIds),
	}))
}

// GetPendingOrders 获取待核销订单
func (c *PlatformController) GetPendingOrders(ctx *gin.Context) {
	orders, err := c.platformOrderService.GetPendingOrders()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(orders))
}

// RefundOrder 退款订单
func (c *PlatformController) RefundOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	err = c.platformOrderService.RefundOrder(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "订单退款成功"}))
}

// DeletePlatformOrder 删除平台订单
func (c *PlatformController) DeletePlatformOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	err = c.platformOrderService.DeletePlatformOrder(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "订单删除成功"}))
}

// BatchDeleteOrders 批量删除订单
func (c *PlatformController) BatchDeleteOrders(ctx *gin.Context) {
	var req models.BatchDeleteRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err := c.platformOrderService.BatchDeleteOrders(req.OrderIds)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "批量删除成功",
		"count":   len(req.OrderIds),
	}))
}

// ExportOrders 导出订单数据
func (c *PlatformController) ExportOrders(ctx *gin.Context) {
	// 获取筛选参数
	filters := make(map[string]interface{})
	if platformType := ctx.Query("platformType"); platformType != "" {
		filters["platform_type"] = platformType
	}
	if startDate := ctx.Query("start_date"); startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate := ctx.Query("end_date"); endDate != "" {
		filters["end_date"] = endDate
	}

	fileData, filename, err := c.platformOrderService.ExportOrders(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", "attachment; filename="+filename)
	ctx.Header("Content-Length", strconv.Itoa(len(fileData)))

	// 返回文件数据
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileData)
}

// GetVerificationStats 获取核销统计数据
func (c *PlatformController) GetVerificationStats(ctx *gin.Context) {
	stats, err := c.platformOrderService.GetVerificationStats()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(stats))
}

// GetVerificationHistory 获取核销历史
func (c *PlatformController) GetVerificationHistory(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	filter := ctx.DefaultQuery("filter", "today")

	history, total, err := c.platformOrderService.GetVerificationHistory(page, pageSize, filter)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := gin.H{
		"page":        page,
		"page_size":   pageSize,
		"total":       total,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		"data":        history,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetAnalytics 获取平台分析数据
func (c *PlatformController) GetAnalytics(ctx *gin.Context) {
	// 获取日期范围参数
	startDate := ctx.Query("startDate")
	endDate := ctx.Query("endDate")

	analytics, err := c.platformOrderService.GetAnalytics(startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(analytics))
}

// GetRoomAnalysis 获取房间关联分析
func (c *PlatformController) GetRoomAnalysis(ctx *gin.Context) {
	// 获取日期范围参数
	startDate := ctx.Query("startDate")
	endDate := ctx.Query("endDate")

	analysis, err := c.platformOrderService.GetRoomAnalysis(startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(analysis))
}
