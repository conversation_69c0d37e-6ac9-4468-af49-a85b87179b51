package controllers

import (
	"net/http"
	"strconv"
	"time"

	"mahjong-system/models"
	"mahjong-system/services"

	"github.com/gin-gonic/gin"
)

// FinanceController 财务控制器
type FinanceController struct {
	financeService services.FinanceService
}

// NewFinanceController 创建财务控制器
func NewFinanceController(financeService services.FinanceService) *FinanceController {
	return &FinanceController{
		financeService: financeService,
	}
}

// GetFinanceReport 获取财务报表数据
func (c *FinanceController) GetFinanceReport(ctx *gin.Context) {
	// 获取日期范围参数
	startDateStr := ctx.Query("startDate")
	endDateStr := ctx.Query("endDate")

	// 设置默认日期范围（最近30天）
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30)

	if startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsed
		}
	}

	if endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsed
		}
	}

	// 获取财务报表数据
	report, err := c.financeService.GetFinanceReport(startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(report))
}

// GetFinanceCharts 获取图表数据
func (c *FinanceController) GetFinanceCharts(ctx *gin.Context) {
	// 获取参数
	startDateStr := ctx.Query("startDate")
	endDateStr := ctx.Query("endDate")
	revenueType := ctx.DefaultQuery("revenueType", "daily")
	profitType := ctx.DefaultQuery("profitType", "monthly")

	// 设置默认日期范围
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30)

	if startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsed
		}
	}

	if endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsed
		}
	}

	// 获取图表数据
	charts, err := c.financeService.GetFinanceCharts(startDate, endDate, revenueType, profitType)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(charts))
}

// AddExpense 添加支出记录
func (c *FinanceController) AddExpense(ctx *gin.Context) {
	var req models.ExpenseCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	expense, err := c.financeService.AddExpense(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(expense))
}

// GetExpenseList 获取支出记录列表
func (c *FinanceController) GetExpenseList(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	// 获取筛选参数
	filters := make(map[string]interface{})
	if expenseType := ctx.Query("type"); expenseType != "" {
		filters["type"] = expenseType
	}
	if startDate := ctx.Query("start_date"); startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate := ctx.Query("end_date"); endDate != "" {
		filters["end_date"] = endDate
	}

	expenses, total, err := c.financeService.GetExpenseList(page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := gin.H{
		"page":        page,
		"page_size":   pageSize,
		"total":       total,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		"data":        expenses,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// ExportFinanceReport 导出财务报表
func (c *FinanceController) ExportFinanceReport(ctx *gin.Context) {
	// 获取日期范围参数
	startDateStr := ctx.Query("startDate")
	endDateStr := ctx.Query("endDate")

	// 设置默认日期范围
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30)

	if startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsed
		}
	}

	if endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsed
		}
	}

	// 生成Excel文件
	fileData, filename, err := c.financeService.ExportFinanceReport(startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", "attachment; filename="+filename)
	ctx.Header("Content-Length", strconv.Itoa(len(fileData)))

	// 返回文件数据
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileData)
}

// GetFinanceSummary 获取财务概要数据
func (c *FinanceController) GetFinanceSummary(ctx *gin.Context) {
	// 获取日期范围参数
	startDateStr := ctx.Query("startDate")
	endDateStr := ctx.Query("endDate")

	// 设置默认日期范围
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30)

	if startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsed
		}
	}

	if endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsed
		}
	}

	// 获取财务概要数据
	summary, err := c.financeService.GetFinanceSummary(startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(summary))
}
