package controllers

import (
	"mahjong-system/models"
	"mahjong-system/services"
	"mahjong-system/utils"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// OrderController 订单控制器
type OrderController struct {
	orderService services.OrderService
}

// NewOrderController 创建订单控制器
func NewOrderController(orderService services.OrderService) *OrderController {
	return &OrderController{
		orderService: orderService,
	}
}

// CreateOrder 创建订单
func (c *OrderController) CreateOrder(ctx *gin.Context) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	var req models.OrderCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	// 额外的业务逻辑验证
	if err := utils.ValidateID(req.RoomID, "房间"); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, err.Error()))
		return
	}

	if err := utils.ValidateAmount(req.TotalAmount, 0, 5000); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, err.Error()))
		return
	}

	order, err := c.orderService.CreateOrder(userID, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(order))
}

// GetOrder 获取订单详情
func (c *OrderController) GetOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	order, err := c.orderService.GetOrder(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(order))
}

// GetOrderList 获取订单列表
func (c *OrderController) GetOrderList(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	// 构建过滤条件
	filters := make(map[string]interface{})
	if orderNumber := ctx.Query("order_number"); orderNumber != "" {
		filters["order_number"] = orderNumber
	}
	if userID, _ := strconv.Atoi(ctx.Query("user_id")); userID != 0 {
		filters["user_id"] = userID
	}
	if roomID, _ := strconv.Atoi(ctx.Query("room_id")); roomID != 0 {
		filters["room_id"] = roomID
	}
	if status := ctx.Query("status"); status != "" {
		filters["status"] = status
	}
	if startDate := ctx.Query("start_date"); startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate := ctx.Query("end_date"); endDate != "" {
		filters["end_date"] = endDate
	}

	orders, total, err := c.orderService.GetOrderList(page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 获取统计数据
	stats, err := c.orderService.GetOrderStats()
	if err != nil {
		// 如果获取统计数据失败，使用默认值
		stats = &models.OrderStats{
			Total:        0,
			TotalAmount:  0,
			TodayOrders:  0,
			TodayAmount:  0,
		}
	}

	response := &models.OrderListResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
		Data:       orders,
		Stats:      stats,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetUserOrders 获取用户订单列表
func (c *OrderController) GetUserOrders(ctx *gin.Context) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	orders, total, err := c.orderService.GetUserOrders(userID, page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := &models.PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
		Data:       orders,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// PayOrder 支付订单
func (c *OrderController) PayOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	var req models.OrderPaymentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err = c.orderService.PayOrder(id, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "支付成功"}))
}

// ExtendOrder 订单续费
func (c *OrderController) ExtendOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	var req models.OrderExtendRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err = c.orderService.ExtendOrder(id, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "续费成功"}))
}

// CompleteOrder 完成订单
func (c *OrderController) CompleteOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	err = c.orderService.CompleteOrder(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "订单完成"}))
}

// CancelOrder 取消订单
func (c *OrderController) CancelOrder(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "订单ID格式错误"))
		return
	}

	err = c.orderService.CancelOrder(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "订单已取消"}))
}

// GetActiveOrderByRoom 获取房间的活跃订单
func (c *OrderController) GetActiveOrderByRoom(ctx *gin.Context) {
	roomIDStr := ctx.Param("room_id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	order, err := c.orderService.GetActiveOrderByRoom(roomID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(order))
}

// GetTodayIncome 获取今日收入
func (c *OrderController) GetTodayIncome(ctx *gin.Context) {
	income, err := c.orderService.GetTodayIncome()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"today_income": income}))
}

// GetIncomeReport 获取收入报表
func (c *OrderController) GetIncomeReport(ctx *gin.Context) {
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "开始日期和结束日期不能为空"))
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "开始日期格式错误"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "结束日期格式错误"))
		return
	}

	report, err := c.orderService.GetIncomeReport(startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(report))
}

// CalculateOrderAmount 计算订单金额
func (c *OrderController) CalculateOrderAmount(ctx *gin.Context) {
	roomIDStr := ctx.Query("room_id")
	hoursStr := ctx.Query("hours")

	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	hours, err := strconv.Atoi(hoursStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "小时数格式错误"))
		return
	}

	amount, err := c.orderService.CalculateOrderAmount(roomID, hours)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"amount": amount}))
}
