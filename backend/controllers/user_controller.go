package controllers

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/services"
	"mahjong-system/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// UserController 用户控制器
type UserController struct {
	userService services.UserService
}

// NewUserController 创建用户控制器
func NewUserController(userService services.UserService) *UserController {
	return &UserController{
		userService: userService,
	}
}

// Register 用户注册
func (c *UserController) Register(ctx *gin.Context) {
	var req models.UserRegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	user, err := c.userService.Register(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(user.ToResponse()))
}

// Login 用户登录
func (c *UserController) Login(ctx *gin.Context) {
	var req models.UserLoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	user, err := c.userService.Login(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(user.ToResponse()))
}

// GetProfile 获取用户资料
func (c *UserController) GetProfile(ctx *gin.Context) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	profile, err := c.userService.GetProfile(userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(profile))
}

// UpdateProfile 更新用户资料
func (c *UserController) UpdateProfile(ctx *gin.Context) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	var req models.UserUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	profile, err := c.userService.UpdateProfile(userID, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(profile))
}

// Recharge 用户充值
func (c *UserController) Recharge(ctx *gin.Context) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	var req models.UserRechargeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	// 额外的业务逻辑验证
	if err := utils.ValidateAmount(req.Amount, 0, 10000); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, err.Error()))
		return
	}

	if err := utils.ValidatePaymentMethod(req.PaymentMethod); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, err.Error()))
		return
	}

	err = c.userService.Recharge(userID, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "充值成功"}))
}

// GetBalanceRecords 获取余额变动记录
func (c *UserController) GetBalanceRecords(ctx *gin.Context) {
	userID, err := getUserIDFromContext(ctx)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	records, total, err := c.userService.GetBalanceRecords(userID, page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := &models.PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
		Data:       records,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetUserList 获取用户列表（管理端）
func (c *UserController) GetUserList(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	// 参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// 构建过滤条件
	filters := make(map[string]interface{})
	if nickname := ctx.Query("nickname"); nickname != "" {
		filters["nickname"] = nickname
	}
	if phone := ctx.Query("phone"); phone != "" {
		filters["phone"] = phone
	}
	if startDate := ctx.Query("start_date"); startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate := ctx.Query("end_date"); endDate != "" {
		filters["end_date"] = endDate
	}

	users, total, err := c.userService.GetUserList(page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 计算总页数，避免除零错误
	totalPages := 0
	if pageSize > 0 {
		totalPages = int((total + int64(pageSize) - 1) / int64(pageSize))
	}

	// 获取统计数据
	stats, err := c.userService.GetUserStats()
	if err != nil {
		// 如果获取统计失败，使用默认值
		stats = &models.UserStatistics{
			TotalUsers:    int(total),
			NewUsersToday: 0,
			ActiveUsers:   0,
		}
	}

	response := map[string]interface{}{
		"page":        page,
		"page_size":   pageSize,
		"total":       total,
		"total_pages": totalPages,
		"data":        users,
		"stats": map[string]interface{}{
			"total":          stats.TotalUsers,
			"todayNew":       stats.NewUsersToday,
			"activeUsers":    stats.ActiveUsers,
			"avgConsumption": 0, // 暂时设为0，后续可以计算
		},
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetUserStats 获取用户统计信息（管理端）
func (c *UserController) GetUserStats(ctx *gin.Context) {
	stats, err := c.userService.GetUserStats()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(stats))
}

// getUserIDFromContext 从上下文中获取用户ID
func getUserIDFromContext(ctx *gin.Context) (int, error) {
	// 这里应该从JWT token或session中获取用户ID
	// 暂时使用查询参数模拟
	userIDStr := ctx.Query("user_id")
	if userIDStr == "" {
		userIDStr = ctx.GetHeader("X-User-ID")
	}
	if userIDStr == "" {
		return 0, fmt.Errorf("用户ID不存在")
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		return 0, fmt.Errorf("用户ID格式错误")
	}

	return userID, nil
}
