package controllers

import (
	"mahjong-system/models"
	"mahjong-system/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// RoomController 房间控制器
type RoomController struct {
	roomService services.RoomService
}

// NewRoomController 创建房间控制器
func NewRoomController(roomService services.RoomService) *RoomController {
	return &RoomController{
		roomService: roomService,
	}
}

// GetRoomList 获取房间列表
func (c *RoomController) GetRoomList(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	// 构建过滤条件
	filters := make(map[string]interface{})
	if roomNumber := ctx.Query("room_number"); roomNumber != "" {
		filters["room_number"] = roomNumber
	}
	if name := ctx.Query("name"); name != "" {
		filters["name"] = name
	}
	if status := ctx.Query("status"); status != "" {
		filters["status"] = status
	}
	if pricingRuleID, _ := strconv.Atoi(ctx.Query("pricing_rule_id")); pricingRuleID != 0 {
		filters["pricing_rule_id"] = pricingRuleID
	}

	rooms, total, err := c.roomService.GetRoomList(page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := &models.PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
		Data:       rooms,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetRoom 获取房间详情
func (c *RoomController) GetRoom(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	room, err := c.roomService.GetRoom(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(room))
}

// GetRoomByNumber 根据房间号获取房间
func (c *RoomController) GetRoomByNumber(ctx *gin.Context) {
	roomNumber := ctx.Param("number")
	if roomNumber == "" {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间号不能为空"))
		return
	}

	room, err := c.roomService.GetRoomByNumber(roomNumber)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(room))
}

// GetAvailableRooms 获取可用房间列表
func (c *RoomController) GetAvailableRooms(ctx *gin.Context) {
	rooms, err := c.roomService.GetAvailableRooms()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(rooms))
}

// CreateRoom 创建房间（管理端）
func (c *RoomController) CreateRoom(ctx *gin.Context) {
	var req models.RoomCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	room, err := c.roomService.CreateRoom(&req)
	if err != nil {
		// 检查是否是业务逻辑错误（如重复数据）
		if err.Error() == "房间号已存在" {
			ctx.JSON(http.StatusConflict, models.Error(models.CodeBusinessError, err.Error()))
			return
		}
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(room))
}

// UpdateRoom 更新房间（管理端）
func (c *RoomController) UpdateRoom(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	var req models.RoomUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	room, err := c.roomService.UpdateRoom(id, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(room))
}

// DeleteRoom 删除房间（管理端）
func (c *RoomController) DeleteRoom(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	err = c.roomService.DeleteRoom(id)
	if err != nil {
		// 根据错误类型返回不同的状态码
		errorMsg := err.Error()

		if errorMsg == "房间不存在" {
			ctx.JSON(http.StatusNotFound, models.Error(models.CodeBusinessError, errorMsg))
			return
		}

		if errorMsg == "房间有活跃订单，无法删除。请先完成或取消房间内的订单" ||
		   errorMsg == "无法删除房间：房间仍有关联数据。请先删除相关的设备、预约或订单记录" {
			ctx.JSON(http.StatusConflict, models.Error(models.CodeBusinessError, errorMsg))
			return
		}

		// 其他错误返回500
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, errorMsg))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "删除成功"}))
}

// UpdateRoomStatus 更新房间状态（管理端）
func (c *RoomController) UpdateRoomStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err = c.roomService.UpdateRoomStatus(id, req.Status)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "状态更新成功"}))
}

// GetRoomWithDevices 获取房间及设备信息（管理端）
func (c *RoomController) GetRoomWithDevices(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	room, err := c.roomService.GetRoomWithDevices(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(room))
}

// GetRoomStatistics 获取房间统计信息（管理端）
func (c *RoomController) GetRoomStatistics(ctx *gin.Context) {
	statistics, err := c.roomService.GetRoomStatistics()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 转换为前端图表组件需要的格式
	chartData := map[string]interface{}{
		"total":       statistics["total"],
		"available":   statistics["available"],
		"occupied":    statistics["occupied"],
		"maintenance": statistics["maintenance"],
	}

	ctx.JSON(http.StatusOK, models.Success(chartData))
}
