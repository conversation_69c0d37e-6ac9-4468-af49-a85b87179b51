package controllers

import (
	"mahjong-system/models"
	"mahjong-system/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PackageController 套餐控制器
type PackageController struct {
	packageService     services.PackageService
	userPackageService services.UserPackageService
}

// NewPackageController 创建套餐控制器
func NewPackageController(
	packageService services.PackageService,
	userPackageService services.UserPackageService,
) *PackageController {
	return &PackageController{
		packageService:     packageService,
		userPackageService: userPackageService,
	}
}

// ===== 管理端套餐管理 =====

// GetPackageList 获取套餐列表（管理端）
func (c *PackageController) GetPackageList(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	filters := make(map[string]interface{})
	if keyword := ctx.Query("keyword"); keyword != "" {
		filters["keyword"] = keyword
	}
	if packageType := ctx.Query("type"); packageType != "" {
		filters["type"] = packageType
	}
	if isActive := ctx.Query("is_active"); isActive != "" {
		if isActive == "true" {
			filters["is_active"] = true
		} else if isActive == "false" {
			filters["is_active"] = false
		}
	}

	packages, total, err := c.packageService.GetPackageList(page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"list":      packages,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}))
}

// GetPackage 获取套餐详情（管理端）
func (c *PackageController) GetPackage(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	pkg, err := c.packageService.GetPackage(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(pkg))
}

// CreatePackage 创建套餐（管理端）
func (c *PackageController) CreatePackage(ctx *gin.Context) {
	var req models.PackageCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	pkg, err := c.packageService.CreatePackage(&req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(pkg))
}

// UpdatePackage 更新套餐（管理端）
func (c *PackageController) UpdatePackage(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	var req models.PackageUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	pkg, err := c.packageService.UpdatePackage(id, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(pkg))
}

// DeletePackage 删除套餐（管理端）
func (c *PackageController) DeletePackage(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	err = c.packageService.DeletePackage(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "套餐删除成功"}))
}

// UpdatePackageStatus 更新套餐状态（管理端）
func (c *PackageController) UpdatePackageStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	var req struct {
		IsActive bool `json:"is_active" binding:"required"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err = c.packageService.UpdatePackageStatus(id, req.IsActive)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{"message": "状态更新成功"}))
}

// GetPackageStats 获取套餐统计（管理端）
func (c *PackageController) GetPackageStats(ctx *gin.Context) {
	stats, err := c.packageService.GetPackageStats()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(stats))
}

// ===== 用户端套餐购买 =====

// GetActivePackages 获取有效套餐（用户端）
func (c *PackageController) GetActivePackages(ctx *gin.Context) {
	packageType := ctx.Query("type")

	packages, err := c.packageService.GetActivePackages(packageType)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 添加推荐标签
	for _, pkg := range packages {
		// 可以根据业务逻辑添加推荐标签
		if pkg.SalesCount > 50 {
			// 热门套餐逻辑
		}
	}

	ctx.JSON(http.StatusOK, models.Success(packages))
}

// GetPackageDetail 获取套餐详情（用户端）
func (c *PackageController) GetPackageDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	pkg, err := c.packageService.GetPackage(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	if !pkg.IsActive {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeBusinessError, "套餐已下架"))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(pkg))
}

// PurchasePackage 购买套餐（用户端）
func (c *PackageController) PurchasePackage(ctx *gin.Context) {
	idStr := ctx.Param("id")
	packageID, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	// 获取用户ID（从JWT中获取）
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	var req models.PackagePurchaseRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	userPackage, err := c.packageService.PurchasePackage(userID.(int), packageID, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// TODO: 返回支付信息
	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"user_package_id": userPackage.ID,
		"total_amount":    userPackage.PurchasePrice,
		"message":         "套餐购买成功",
		// "payment_info": paymentInfo, // 支付信息
	}))
}

// RechargePackage 续费套餐（用户端）
func (c *PackageController) RechargePackage(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	var req struct {
		PackageID int `json:"package_id" binding:"required"`
		Hours     int `json:"hours" binding:"required,min=1,max=24"`
		models.PackagePurchaseRequest
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	userPackage, err := c.packageService.RechargePackage(userID.(int), req.PackageID, req.Hours, &req.PackagePurchaseRequest)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"user_package_id": userPackage.ID,
		"total_amount":    userPackage.PurchasePrice,
		"message":         "续费成功",
	}))
}

// ===== 用户套餐管理 =====

// GetUserPackages 获取用户套餐列表
func (c *PackageController) GetUserPackages(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	status := ctx.DefaultQuery("status", "")

	userPackages, total, err := c.userPackageService.GetUserPackages(userID.(int), status, page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"list":      userPackages,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}))
}

// GetUserPackage 获取用户套餐详情
func (c *PackageController) GetUserPackage(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	userPackage, err := c.userPackageService.GetUserPackage(id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(userPackage))
}

// GetUserPackageStats 获取用户套餐统计
func (c *PackageController) GetUserPackageStats(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, models.Error(models.CodeUnauthorized, "用户未登录"))
		return
	}

	stats, err := c.userPackageService.GetUserPackageStats(userID.(int))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(stats))
}

// UsePackage 使用套餐开房
func (c *PackageController) UsePackage(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	var req models.UsePackageRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	order, err := c.userPackageService.UsePackage(id, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"order_id": order.ID,
		"message":  "开房成功",
	}))
}

// GetUsageLogs 获取套餐使用记录
func (c *PackageController) GetUsageLogs(ctx *gin.Context) {
	idStr := ctx.Param("id")
	userPackageID, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "套餐ID格式错误"))
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	logs, total, err := c.userPackageService.GetUsageLogs(userPackageID, page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"list":      logs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}))
}
