package controllers

import (
	"fmt"
	"mahjong-system/models"
	"mahjong-system/mqtt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// MQTTController MQTT控制器
type MQTTController struct {
	mqttManager *mqtt.Manager
}

// NewMQTTController 创建MQTT控制器
func NewMQTTController(mqttManager *mqtt.Manager) *MQTTController {
	return &MQTTController{
		mqttManager: mqttManager,
	}
}

// HandleHeartbeat 处理设备心跳
func (c *MQTTController) HandleHeartbeat(ctx *gin.Context) {
	var req struct {
		MacAddress string                 `json:"mac_address" binding:"required"`
		Timestamp  string                 `json:"timestamp"`
		Battery    int                    `json:"battery"`
		Signal     int                    `json:"signal"`
		Version    string                 `json:"version"`
		Data       map[string]interface{} `json:"data"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	// 这里可以记录心跳信息到数据库
	// 实际的心跳处理在MQTT客户端中完成

	response := &models.MQTTHeartbeatResponse{
		Message:    "心跳接收成功",
		MacAddress: req.MacAddress,
		Timestamp:  time.Now().Format("2006-01-02 15:04:05"),
		Status:     "received",
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// HandleStatusReport 处理设备状态上报
func (c *MQTTController) HandleStatusReport(ctx *gin.Context) {
	var req struct {
		MacAddress string                 `json:"mac_address" binding:"required"`
		Status     string                 `json:"status" binding:"required"`
		Timestamp  string                 `json:"timestamp"`
		Data       map[string]interface{} `json:"data"`
		Error      string                 `json:"error"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	// 这里可以记录状态信息到数据库
	// 实际的状态处理在MQTT客户端中完成

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "状态上报成功",
		"mac_address": req.MacAddress,
		"status": req.Status,
	}))
}

// HandleControlResponse 处理设备控制响应
func (c *MQTTController) HandleControlResponse(ctx *gin.Context) {
	var req struct {
		MacAddress string                 `json:"mac_address" binding:"required"`
		RequestID  string                 `json:"request_id" binding:"required"`
		Command    string                 `json:"command" binding:"required"`
		Success    bool                   `json:"success"`
		Message    string                 `json:"message"`
		Data       map[string]interface{} `json:"data"`
		Timestamp  string                 `json:"timestamp"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	// 记录控制响应结果
	if req.Success {
		// 控制成功的处理逻辑
	} else {
		// 控制失败的处理逻辑
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "控制响应接收成功",
		"request_id": req.RequestID,
		"success": req.Success,
	}))
}

// GetConnectionStatus 获取MQTT连接状态
func (c *MQTTController) GetConnectionStatus(ctx *gin.Context) {
	status := c.mqttManager.GetConnectionStatus()

	// 使用统一的响应格式
	response := &models.MQTTConnectionStatus{
		Status:        status["mqtt_connected"].(bool),
		MQTTConnected: status["mqtt_connected"].(bool),
		Broker:        status["broker"].(string),
		ClientID:      status["client_id"].(string),
		Topics:        status["topics"].(map[string]string),
		ConnectionInfo: map[string]interface{}{
			"connected_at": time.Now().Format("2006-01-02 15:04:05"),
			"uptime":       "运行中",
		},
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetRoomDeviceStatus 获取房间设备状态
func (c *MQTTController) GetRoomDeviceStatus(ctx *gin.Context) {
	roomIDStr := ctx.Param("room_id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	status, err := c.mqttManager.GetRoomDeviceStatus(roomID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(status))
}

// ControlRoomLock 控制房间门锁
func (c *MQTTController) ControlRoomLock(ctx *gin.Context) {
	roomIDStr := ctx.Param("room_id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	var req struct {
		Action string `json:"action" binding:"required"` // open, close
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	deviceController := c.mqttManager.GetDeviceController()

	switch req.Action {
	case "open":
		err = deviceController.OpenRoomLock(roomID)
	case "close":
		err = deviceController.CloseRoomLock(roomID)
	default:
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "无效的操作类型"))
		return
	}

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := &models.MQTTControlResponse{
		Message:   "门锁控制命令已发送",
		Action:    req.Action,
		RoomID:    roomID,
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// ControlRoomPower 控制房间电源
func (c *MQTTController) ControlRoomPower(ctx *gin.Context) {
	roomIDStr := ctx.Param("room_id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	var req struct {
		Action string `json:"action" binding:"required"` // on, off
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	deviceController := c.mqttManager.GetDeviceController()

	switch req.Action {
	case "on":
		err = deviceController.PowerOnRoomDevices(roomID)
	case "off":
		err = deviceController.PowerOffRoomDevices(roomID)
	default:
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "无效的操作类型"))
		return
	}

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := &models.MQTTControlResponse{
		Message:   "电源控制命令已发送",
		Action:    req.Action,
		RoomID:    roomID,
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}



// GetDeviceList 获取设备列表
func (c *MQTTController) GetDeviceList(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	// 构建过滤条件
	filters := make(map[string]interface{})
	if deviceType := ctx.Query("type"); deviceType != "" {
		filters["type"] = deviceType
	}
	if roomID, _ := strconv.Atoi(ctx.Query("room_id")); roomID != 0 {
		filters["room_id"] = roomID
	}
	if status := ctx.Query("status"); status != "" {
		filters["status"] = status
	}

	devices, total, err := c.mqttManager.GetServiceManager().Device.GetDeviceList(page, pageSize, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	response := gin.H{
		"page":        page,
		"page_size":   pageSize,
		"total":       total,
		"total_pages": int((total + int64(pageSize) - 1) / int64(pageSize)),
		"data":        devices,
	}

	ctx.JSON(http.StatusOK, models.Success(response))
}

// GetDevice 获取单个设备信息
func (c *MQTTController) GetDevice(ctx *gin.Context) {
	deviceIDStr := ctx.Param("id")
	deviceID, err := strconv.Atoi(deviceIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "设备ID格式错误"))
		return
	}

	// TODO: 实现单个设备信息获取
	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"id": deviceID,
		"message": "设备信息获取功能待实现",
	}))
}

// GetRoomDevices 获取房间设备列表
func (c *MQTTController) GetRoomDevices(ctx *gin.Context) {
	roomIDStr := ctx.Param("id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	status, err := c.mqttManager.GetRoomDeviceStatus(roomID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(status))
}

// ControlRoomDevice 控制房间设备
func (c *MQTTController) ControlRoomDevice(ctx *gin.Context) {
	roomIDStr := ctx.Param("id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	deviceType := ctx.Param("device_type")

	var req struct {
		Action string `json:"action" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	deviceController := c.mqttManager.GetDeviceController()
	var controlErr error

	switch deviceType {
	case "lock":
		switch req.Action {
		case "unlock", "open":
			controlErr = deviceController.OpenRoomLock(roomID)
		case "lock", "close":
			controlErr = deviceController.CloseRoomLock(roomID)
		default:
			ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "无效的门锁操作"))
			return
		}
	case "power":
		switch req.Action {
		case "on":
			controlErr = deviceController.PowerOnRoomDevices(roomID)
		case "off":
			controlErr = deviceController.PowerOffRoomDevices(roomID)
		default:
			ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "无效的电源操作"))
			return
		}
	default:
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "不支持的设备类型"))
		return
	}

	if controlErr != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, controlErr.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": fmt.Sprintf("%s设备控制命令已发送", deviceType),
		"action": req.Action,
	}))
}

// SendCustomMessage 发送自定义消息
func (c *MQTTController) SendCustomMessage(ctx *gin.Context) {
	roomIDStr := ctx.Param("room_id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	var req struct {
		Message string `json:"message" binding:"required"`
		Type    string `json:"type"`    // warning, reminder, info
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	err = c.mqttManager.SendCustomMessage(roomID, req.Message)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "自定义消息已发送",
		"content": req.Message,
	}))
}

// GetDeviceListGrouped 获取按类型分组的设备列表
func (c *MQTTController) GetDeviceListGrouped(ctx *gin.Context) {
	deviceList, err := c.mqttManager.GetServiceManager().Device.GetDeviceListGrouped()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(deviceList))
}

// SendAudioMessage 发送音频消息到房间
func (c *MQTTController) SendAudioMessage(ctx *gin.Context) {
	roomIDStr := ctx.Param("room_id")
	roomID, err := strconv.Atoi(roomIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "房间ID格式错误"))
		return
	}

	var req struct {
		Message string `json:"message" binding:"required"`
		Type    string `json:"type"`
		Volume  int    `json:"volume"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	// 设置默认值
	if req.Volume == 0 {
		req.Volume = 80 // 默认音量80%
	}
	if req.Type == "" {
		req.Type = "notice" // 默认为通知类型
	}

	deviceController := c.mqttManager.GetDeviceController()
	err = deviceController.SendAudioMessage(roomID, req.Message, req.Type, req.Volume)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "音频消息已发送",
		"room_id": roomID,
		"content": req.Message,
	}))
}

// BatchPowerOff 批量关闭所有房间电源
func (c *MQTTController) BatchPowerOff(ctx *gin.Context) {
	deviceController := c.mqttManager.GetDeviceController()
	err := deviceController.BatchPowerOffAllRooms()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "批量关闭电源命令已发送",
	}))
}

// BatchLockRooms 批量锁定所有房间
func (c *MQTTController) BatchLockRooms(ctx *gin.Context) {
	deviceController := c.mqttManager.GetDeviceController()
	err := deviceController.BatchLockAllRooms()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "批量锁定门锁命令已发送",
	}))
}

// BroadcastAudio 广播音频消息到所有房间
func (c *MQTTController) BroadcastAudio(ctx *gin.Context) {
	var req struct {
		Message string `json:"message" binding:"required"`
		Type    string `json:"type"`
		Volume  int    `json:"volume"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "参数验证失败: "+err.Error()))
		return
	}

	// 设置默认值
	if req.Volume == 0 {
		req.Volume = 80
	}
	if req.Type == "" {
		req.Type = "notice"
	}

	deviceController := c.mqttManager.GetDeviceController()
	err := deviceController.BroadcastAudioMessage(req.Message, req.Type, req.Volume)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(gin.H{
		"message": "广播音频消息已发送",
		"content": req.Message,
	}))
}
