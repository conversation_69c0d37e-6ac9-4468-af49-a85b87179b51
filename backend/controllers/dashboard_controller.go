package controllers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"mahjong-system/models"
	"mahjong-system/services"
)

// DashboardController Dashboard控制器
type DashboardController struct {
	dashboardService     services.DashboardService
	orderService         services.OrderService
	roomService          services.RoomService
	userService          services.UserService
	deviceService        services.DeviceService
	platformOrderService services.PlatformOrderService
}

// NewDashboardController 创建Dashboard控制器
func NewDashboardController(
	dashboardService services.DashboardService,
	orderService services.OrderService,
	roomService services.RoomService,
	userService services.UserService,
	deviceService services.DeviceService,
	platformOrderService services.PlatformOrderService,
) *DashboardController {
	return &DashboardController{
		dashboardService:     dashboardService,
		orderService:         orderService,
		roomService:          roomService,
		userService:          userService,
		deviceService:        deviceService,
		platformOrderService: platformOrderService,
	}
}

// GetDashboardData 获取仪表盘数据
func (c *DashboardController) GetDashboardData(ctx *gin.Context) {
	// 获取今日收入（订单收入）
	orderIncome, err := c.orderService.GetTodayIncome()
	if err != nil {
		orderIncome = 0
	}

	// 获取今日平台核销收入
	today := time.Now().Format("2006-01-02")
	platformIncome := c.getTodayPlatformIncome(today)

	// 计算总收入
	todayIncome := orderIncome + platformIncome

	// 获取活跃订单数（正在进行中的订单）
	activeOrders, err := c.orderService.GetActiveOrderCount()
	if err != nil {
		activeOrders = 0
	}

	// 获取房间统计
	roomStats, err := c.roomService.GetRoomStatistics()
	if err != nil {
		roomStats = map[string]int{
			"total":       0,
			"available":   0,
			"occupied":    0,
			"maintenance": 0,
		}
	}

	// 获取设备统计
	deviceStats, err := c.deviceService.GetDeviceStatistics()
	if err != nil {
		deviceStats = map[string]int{
			"total":   0,
			"online":  0,
			"offline": 0,
		}
	}

	// 获取房间状态图表数据
	roomStatusChart := []models.RoomStatusData{
		{Status: "available", Count: roomStats["available"]},
		{Status: "occupied", Count: roomStats["occupied"]},
		{Status: "maintenance", Count: roomStats["maintenance"]},
	}

	// 获取最近7天的收入数据
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -7)
	incomeReport, err := c.orderService.GetIncomeReport(startDate, endDate)
	var incomeChart []models.IncomeData
	if err == nil && incomeReport != nil {
		incomeChart = incomeReport.DailyIncome
	}

	// 获取最近7天的订单趋势数据
	orderTrendChart, err := c.orderService.GetOrderTrendData(startDate, endDate)
	if err != nil {
		orderTrendChart = []models.OrderTrendData{}
	}

	// 构建符合前端期望的数据格式
	dashboardData := map[string]interface{}{
		"todayIncome":     todayIncome,
		"totalRooms":      roomStats["total"],
		"occupiedRooms":   roomStats["occupied"],
		"availableRooms":  roomStats["available"],
		"activeOrders":    activeOrders,
		"onlineDevices":   deviceStats["online"],
		"offlineDevices":  deviceStats["offline"],
		"roomStatusChart": roomStatusChart,
		"incomeChart":     incomeChart,
		"orderTrendChart": orderTrendChart,
	}

	ctx.JSON(http.StatusOK, models.Success(dashboardData))
}

// getTodayPlatformIncome 获取今日平台核销收入
func (c *DashboardController) getTodayPlatformIncome(today string) float64 {
	// 获取今日已核销的平台订单
	filters := map[string]interface{}{
		"verification_status": "verified",
		"start_date":         today,
		"end_date":           today,
	}

	orders, _, err := c.platformOrderService.GetPlatformOrderList(1, 1000, filters)
	if err != nil {
		return 0
	}

	var totalIncome float64
	for _, order := range orders {
		totalIncome += order.PaidAmount
	}

	return totalIncome
}

// GetRoomStatistics 获取房间统计数据
func (c *DashboardController) GetRoomStatistics(ctx *gin.Context) {
	statistics, err := c.roomService.GetRoomStatistics()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 转换为前端需要的格式
	roomStats := []map[string]interface{}{
		{"name": "空闲", "value": statistics["available"], "color": "#67C23A"},
		{"name": "使用中", "value": statistics["occupied"], "color": "#E6A23C"},
		{"name": "维护中", "value": statistics["maintenance"], "color": "#F56C6C"},
		{"name": "预约中", "value": 0, "color": "#409EFF"}, // 暂时设为0，后续可以添加预约功能
	}

	ctx.JSON(http.StatusOK, models.Success(roomStats))
}

// GetIncomeReport 获取收入报表
func (c *DashboardController) GetIncomeReport(ctx *gin.Context) {
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "开始日期和结束日期不能为空"))
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "开始日期格式错误"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, models.Error(models.CodeValidationError, "结束日期格式错误"))
		return
	}

	report, err := c.orderService.GetIncomeReport(startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	// 计算统计数据
	today := time.Now().Format("2006-01-02")
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	var todayIncome, yesterdayIncome float64
	for _, data := range report.DailyIncome {
		if data.Date == today {
			todayIncome = data.Income
		}
		if data.Date == yesterday {
			yesterdayIncome = data.Income
		}
	}

	// 计算周收入和月收入
	thisWeekIncome := report.TotalIncome
	thisMonthIncome := report.TotalIncome * 4 // 简化计算

	incomeStats := map[string]interface{}{
		"today":     todayIncome,
		"yesterday": yesterdayIncome,
		"thisWeek":  thisWeekIncome,
		"thisMonth": thisMonthIncome,
		"chartData": report.DailyIncome,
	}

	ctx.JSON(http.StatusOK, models.Success(incomeStats))
}

// GetUserStats 获取用户统计数据
func (c *DashboardController) GetUserStats(ctx *gin.Context) {
	stats, err := c.userService.GetUserStats()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, models.Error(models.CodeBusinessError, err.Error()))
		return
	}

	ctx.JSON(http.StatusOK, models.Success(stats))
}

// GetSystemOverview 获取系统概览数据
func (c *DashboardController) GetSystemOverview(ctx *gin.Context) {
	// 获取各种统计数据
	todayIncome, _ := c.orderService.GetTodayIncome()
	todayOrders, _ := c.orderService.GetTodayOrderCount()
	roomStats, _ := c.roomService.GetRoomStatistics()
	userStats, _ := c.userService.GetUserStats()
	deviceStats, _ := c.deviceService.GetDeviceStatistics()

	overview := map[string]interface{}{
		"income": map[string]interface{}{
			"today":  todayIncome,
			"trend":  "+12.5%", // 模拟趋势数据
		},
		"orders": map[string]interface{}{
			"today": todayOrders,
			"trend": "+8.3%", // 模拟趋势数据
		},
		"rooms": map[string]interface{}{
			"total":      roomStats["total"],
			"occupied":   roomStats["occupied"],
			"available":  roomStats["available"],
			"utilization": func() float64 {
				if roomStats["total"] > 0 {
					return float64(roomStats["occupied"]) / float64(roomStats["total"]) * 100
				}
				return 0
			}(),
		},
		"users": map[string]interface{}{
			"total":    userStats.TotalUsers,
			"newToday": userStats.NewUsersToday,
			"active":   userStats.ActiveUsers,
		},
		"devices": map[string]interface{}{
			"online":  deviceStats["online"],
			"offline": deviceStats["offline"],
			"total":   deviceStats["online"] + deviceStats["offline"],
		},
	}

	ctx.JSON(http.StatusOK, models.Success(overview))
}
