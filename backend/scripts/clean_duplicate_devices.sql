-- 清理重复的设备数据
-- 删除旧的测试设备数据（ID 1-18），保留正确的设备数据（ID 19-37）

-- 首先查看当前设备数据
SELECT 
    id, 
    type, 
    type_name, 
    room_id, 
    mac_address,
    CASE 
        WHEN mac_address LIKE '00:11:22:33:44:%' THEN '旧测试数据'
        WHEN mac_address LIKE 'AA:BB:CC:DD:EE:%' THEN '正确数据'
        ELSE '其他'
    END as data_category
FROM devices 
ORDER BY id;

-- 删除旧的测试设备数据（MAC地址以00:11:22:33:44开头的设备）
DELETE FROM devices 
WHERE mac_address LIKE '00:11:22:33:44:%';

-- 验证删除结果
SELECT COUNT(*) as remaining_devices FROM devices;

-- 查看剩余的设备
SELECT 
    id, 
    type, 
    type_name, 
    room_id, 
    mac_address
FROM devices 
ORDER BY id;
