-- 套餐管理相关表创建脚本
-- 执行时间：2025-01-27

-- 1. 套餐表 (packages)
CREATE TABLE IF NOT EXISTS packages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('fixed_duration', 'flexible_recharge')),
    duration_hours INTEGER DEFAULT NULL,
    original_price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2) NOT NULL,
    discount_rate DECIMAL(5,2) DEFAULT 0,
    description TEXT,
    features TEXT, -- JSON格式存储
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    valid_days INTEGER DEFAULT 365,
    min_recharge_hours INTEGER DEFAULT 1,
    max_recharge_hours INTEGER DEFAULT 24,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 用户套餐表 (user_packages)
CREATE TABLE IF NOT EXISTS user_packages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    package_id INTEGER NOT NULL,
    order_id INTEGER DEFAULT NULL,
    platform_order_id INTEGER DEFAULT NULL,
    total_hours DECIMAL(8,2) NOT NULL,
    used_hours DECIMAL(8,2) DEFAULT 0,
    remaining_hours DECIMAL(8,2) NOT NULL,
    purchase_price DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'used_up', 'refunded')),
    expires_at DATETIME NOT NULL,
    activated_at DATETIME DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE RESTRICT,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
);

-- 3. 外卖平台订单表 (platform_orders)
CREATE TABLE IF NOT EXISTS platform_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform_type VARCHAR(20) NOT NULL CHECK (platform_type IN ('meituan', 'eleme', 'douyin')),
    platform_order_id VARCHAR(100) NOT NULL,
    verification_code VARCHAR(50) NOT NULL,
    package_id INTEGER NOT NULL,
    user_id INTEGER DEFAULT NULL,
    original_price DECIMAL(10,2) NOT NULL,
    platform_commission DECIMAL(10,2) DEFAULT 0,
    actual_income DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'verified', 'expired', 'refunded')),
    verified_at DATETIME DEFAULT NULL,
    expires_at DATETIME NOT NULL,
    platform_data TEXT, -- JSON格式存储
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE RESTRICT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 4. 套餐使用记录表 (package_usage_logs)
CREATE TABLE IF NOT EXISTS package_usage_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_package_id INTEGER NOT NULL,
    order_id INTEGER NOT NULL,
    hours_used DECIMAL(8,2) NOT NULL,
    hours_before DECIMAL(8,2) NOT NULL,
    hours_after DECIMAL(8,2) NOT NULL,
    room_id INTEGER NOT NULL,
    started_at DATETIME NOT NULL,
    ended_at DATETIME DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_package_id) REFERENCES user_packages(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE RESTRICT
);

-- 5. 更新订单表，添加套餐相关字段
ALTER TABLE orders ADD COLUMN package_type VARCHAR(20) DEFAULT 'hourly' CHECK (package_type IN ('hourly', 'package'));
ALTER TABLE orders ADD COLUMN user_package_id INTEGER DEFAULT NULL;
ALTER TABLE orders ADD COLUMN package_hours_used DECIMAL(8,2) DEFAULT 0;
ALTER TABLE orders ADD COLUMN payment_method VARCHAR(20) DEFAULT 'wechat' CHECK (payment_method IN ('wechat', 'alipay', 'package', 'platform_voucher'));

-- 6. 创建索引
CREATE INDEX IF NOT EXISTS idx_packages_type_active ON packages(type, is_active);
CREATE INDEX IF NOT EXISTS idx_packages_sort_order ON packages(sort_order DESC);

CREATE INDEX IF NOT EXISTS idx_user_packages_user_status ON user_packages(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_packages_expires_at ON user_packages(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_packages_status_remaining ON user_packages(status, remaining_hours);

CREATE INDEX IF NOT EXISTS idx_platform_orders_platform_status ON platform_orders(platform_type, status);
CREATE INDEX IF NOT EXISTS idx_platform_orders_verification_code ON platform_orders(verification_code);
CREATE INDEX IF NOT EXISTS idx_platform_orders_expires_at ON platform_orders(expires_at);

CREATE INDEX IF NOT EXISTS idx_package_usage_logs_user_package ON package_usage_logs(user_package_id);
CREATE INDEX IF NOT EXISTS idx_package_usage_logs_started_at ON package_usage_logs(started_at DESC);

-- 7. 创建唯一约束
CREATE UNIQUE INDEX IF NOT EXISTS uk_platform_orders_platform_order ON platform_orders(platform_type, platform_order_id);
CREATE UNIQUE INDEX IF NOT EXISTS uk_platform_orders_verification_code ON platform_orders(verification_code);

-- 8. 插入示例套餐数据
INSERT OR IGNORE INTO packages (id, name, type, duration_hours, original_price, sale_price, discount_rate, description, features, sort_order, valid_days) VALUES
(1, '4小时畅玩套餐', 'fixed_duration', 4, 120.00, 100.00, 16.67, '适合短时间娱乐，性价比高', '["免费茶水", "专属客服"]', 1, 30),
(2, '6小时超值套餐', 'fixed_duration', 6, 180.00, 140.00, 22.22, '中等时长，最受欢迎', '["免费茶水", "免费小食", "专属客服"]', 2, 30),
(3, '8小时豪华套餐', 'fixed_duration', 8, 240.00, 180.00, 25.00, '长时间娱乐首选，超值优惠', '["免费茶水", "免费小食", "免费水果", "专属客服", "优先预约"]', 3, 30),
(4, '12小时通宵套餐', 'fixed_duration', 12, 360.00, 250.00, 30.56, '通宵达旦，畅玩不停', '["免费茶水", "免费小食", "免费水果", "免费夜宵", "专属客服", "优先预约"]', 4, 30),
(5, '灵活充值套餐', 'flexible_recharge', NULL, 30.00, 25.00, 16.67, '按需充值，灵活使用', '["自选时长", "长期有效"]', 5, 365);

-- 9. 插入示例外卖平台订单
INSERT OR IGNORE INTO platform_orders (id, platform_type, platform_order_id, verification_code, package_id, original_price, platform_commission, actual_income, expires_at) VALUES
(1, 'meituan', 'MT202501270001', 'MT8888', 1, 100.00, 15.00, 85.00, datetime('now', '+7 days')),
(2, 'eleme', 'ELM202501270001', 'ELM6666', 2, 140.00, 21.00, 119.00, datetime('now', '+7 days')),
(3, 'douyin', 'DY202501270001', 'DY9999', 3, 180.00, 27.00, 153.00, datetime('now', '+7 days'));

-- 10. 更新触发器，自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_packages_updated_at 
    AFTER UPDATE ON packages
    FOR EACH ROW
    BEGIN
        UPDATE packages SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_user_packages_updated_at 
    AFTER UPDATE ON user_packages
    FOR EACH ROW
    BEGIN
        UPDATE user_packages SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_platform_orders_updated_at 
    AFTER UPDATE ON platform_orders
    FOR EACH ROW
    BEGIN
        UPDATE platform_orders SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
