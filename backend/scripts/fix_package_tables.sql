-- 修复套餐管理相关表
-- 执行时间：2025-01-27

-- 1. 创建套餐表 (packages) - 如果不存在
CREATE TABLE IF NOT EXISTS packages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('fixed_duration', 'flexible_recharge')),
    duration_hours INTEGER DEFAULT NULL,
    original_price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2) NOT NULL,
    discount_rate DECIMAL(5,2) DEFAULT 0,
    description TEXT,
    features TEXT, -- JSON格式存储
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    valid_days INTEGER DEFAULT 365,
    min_recharge_hours INTEGER DEFAULT 1,
    max_recharge_hours INTEGER DEFAULT 24,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建用户套餐表 (user_packages) - 如果不存在
CREATE TABLE IF NOT EXISTS user_packages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    package_id INTEGER NOT NULL,
    order_id INTEGER DEFAULT NULL,
    platform_order_id INTEGER DEFAULT NULL,
    total_hours DECIMAL(8,2) NOT NULL,
    used_hours DECIMAL(8,2) DEFAULT 0,
    remaining_hours DECIMAL(8,2) NOT NULL,
    purchase_price DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'used_up', 'refunded')),
    expires_at DATETIME NOT NULL,
    activated_at DATETIME DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建套餐使用记录表 (package_usage_logs) - 如果不存在
CREATE TABLE IF NOT EXISTS package_usage_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_package_id INTEGER NOT NULL,
    order_id INTEGER NOT NULL,
    hours_used DECIMAL(8,2) NOT NULL,
    hours_before DECIMAL(8,2) NOT NULL,
    hours_after DECIMAL(8,2) NOT NULL,
    room_id INTEGER NOT NULL,
    started_at DATETIME NOT NULL,
    ended_at DATETIME DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 4. 检查并插入示例套餐数据
INSERT OR IGNORE INTO packages (id, name, type, duration_hours, original_price, sale_price, discount_rate, description, features, sort_order, valid_days) VALUES
(1, '4小时畅玩套餐', 'fixed_duration', 4, 120.00, 100.00, 16.67, '适合短时间娱乐，性价比高', '["免费茶水", "专属客服"]', 1, 30),
(2, '6小时超值套餐', 'fixed_duration', 6, 180.00, 140.00, 22.22, '中等时长，最受欢迎', '["免费茶水", "免费小食", "专属客服"]', 2, 30),
(3, '8小时豪华套餐', 'fixed_duration', 8, 240.00, 180.00, 25.00, '长时间娱乐首选，超值优惠', '["免费茶水", "免费小食", "免费水果", "专属客服", "优先预约"]', 3, 30),
(4, '12小时通宵套餐', 'fixed_duration', 12, 360.00, 250.00, 30.56, '通宵达旦，畅玩不停', '["免费茶水", "免费小食", "免费水果", "免费夜宵", "专属客服", "优先预约"]', 4, 30),
(5, '灵活充值套餐', 'flexible_recharge', NULL, 30.00, 25.00, 16.67, '按需充值，灵活使用', '["自选时长", "长期有效"]', 5, 365);

-- 5. 验证数据
SELECT 'packages表记录数:' as info, COUNT(*) as count FROM packages;
SELECT 'user_packages表记录数:' as info, COUNT(*) as count FROM user_packages;
SELECT 'package_usage_logs表记录数:' as info, COUNT(*) as count FROM package_usage_logs;
