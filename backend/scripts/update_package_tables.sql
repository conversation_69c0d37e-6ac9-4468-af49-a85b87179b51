-- 更新套餐管理相关表结构
-- 执行时间：2025-01-27

-- 1. 检查并插入示例套餐数据
INSERT OR IGNORE INTO packages (id, name, type, duration_hours, original_price, sale_price, discount_rate, description, features, sort_order, valid_days) VALUES
(1, '4小时畅玩套餐', 'fixed_duration', 4, 120.00, 100.00, 16.67, '适合短时间娱乐，性价比高', '["免费茶水", "专属客服"]', 1, 30),
(2, '6小时超值套餐', 'fixed_duration', 6, 180.00, 140.00, 22.22, '中等时长，最受欢迎', '["免费茶水", "免费小食", "专属客服"]', 2, 30),
(3, '8小时豪华套餐', 'fixed_duration', 8, 240.00, 180.00, 25.00, '长时间娱乐首选，超值优惠', '["免费茶水", "免费小食", "免费水果", "专属客服", "优先预约"]', 3, 30),
(4, '12小时通宵套餐', 'fixed_duration', 12, 360.00, 250.00, 30.56, '通宵达旦，畅玩不停', '["免费茶水", "免费小食", "免费水果", "免费夜宵", "专属客服", "优先预约"]', 4, 30),
(5, '灵活充值套餐', 'flexible_recharge', NULL, 30.00, 25.00, 16.67, '按需充值，灵活使用', '["自选时长", "长期有效"]', 5, 365);

-- 2. 为现有的platform_orders表添加套餐相关字段
-- 先检查字段是否存在，如果不存在则添加
PRAGMA table_info(platform_orders);

-- 添加套餐相关字段到platform_orders表
ALTER TABLE platform_orders ADD COLUMN package_id INTEGER DEFAULT NULL;
ALTER TABLE platform_orders ADD COLUMN original_price DECIMAL(10,2) DEFAULT 0;
ALTER TABLE platform_orders ADD COLUMN platform_commission DECIMAL(10,2) DEFAULT 0;
ALTER TABLE platform_orders ADD COLUMN actual_income DECIMAL(10,2) DEFAULT 0;
ALTER TABLE platform_orders ADD COLUMN expires_at DATETIME DEFAULT NULL;
ALTER TABLE platform_orders ADD COLUMN platform_data TEXT DEFAULT NULL;

-- 3. 更新现有platform_orders数据，设置默认值
UPDATE platform_orders SET 
    original_price = paid_amount,
    platform_commission = paid_amount * 0.15,
    actual_income = paid_amount * 0.85,
    expires_at = datetime('now', '+7 days')
WHERE original_price = 0 OR original_price IS NULL;

-- 4. 插入示例外卖平台订单（套餐相关）
INSERT OR IGNORE INTO platform_orders (
    platform_type, platform_order_id, verification_code, package_id, 
    original_price, platform_commission, actual_income, 
    paid_amount, order_status, verification_status, expires_at
) VALUES
('meituan', 'MT202501270001', 'MT8888', 1, 100.00, 15.00, 85.00, 100.00, 'paid', 'pending', datetime('now', '+7 days')),
('eleme', 'ELM202501270001', 'ELM6666', 2, 140.00, 21.00, 119.00, 140.00, 'paid', 'pending', datetime('now', '+7 days')),
('douyin', 'DY202501270001', 'DY9999', 3, 180.00, 27.00, 153.00, 180.00, 'paid', 'pending', datetime('now', '+7 days'));

-- 5. 插入一些示例用户套餐数据（如果有用户数据的话）
INSERT OR IGNORE INTO user_packages (
    user_id, package_id, total_hours, remaining_hours, purchase_price, 
    expires_at, activated_at
) 
SELECT 
    1 as user_id, 
    1 as package_id, 
    4.0 as total_hours, 
    2.5 as remaining_hours, 
    100.00 as purchase_price,
    datetime('now', '+30 days') as expires_at,
    datetime('now') as activated_at
WHERE EXISTS (SELECT 1 FROM users WHERE id = 1)
AND NOT EXISTS (SELECT 1 FROM user_packages WHERE user_id = 1 AND package_id = 1);

-- 6. 创建外键约束（如果SQLite支持的话）
-- 注意：SQLite的ALTER TABLE不支持添加外键，所以我们跳过这一步

-- 7. 验证数据
SELECT 'packages表记录数:' as info, COUNT(*) as count FROM packages;
SELECT 'user_packages表记录数:' as info, COUNT(*) as count FROM user_packages;
SELECT 'platform_orders表记录数:' as info, COUNT(*) as count FROM platform_orders;
SELECT 'package_usage_logs表记录数:' as info, COUNT(*) as count FROM package_usage_logs;
