package main

import (
	"fmt"
	"log"
	"strings"

	"github.com/jmoiron/sqlx"
	_ "github.com/mattn/go-sqlite3"
)

// OrderIntegrityCheck 订单完整性检查结构
type OrderIntegrityCheck struct {
	ID                 int     `db:"id"`
	PlatformOrderID    string  `db:"platform_order_id"`
	PlatformType       string  `db:"platform_type"`
	OriginalAmount     float64 `db:"original_amount"`
	OriginalPrice      *float64 `db:"original_price"`
	PaidAmount         float64 `db:"paid_amount"`
	PlatformCommission *float64 `db:"platform_commission"`
	ActualIncome       *float64 `db:"actual_income"`
	PackageID          *int    `db:"package_id"`
	ExpiresAt          *string `db:"expires_at"`
	PlatformData       *string `db:"platform_data"`
}

func main() {
	// 连接数据库
	db, err := sqlx.Connect("sqlite3", "/home/<USER>/database/mahjong.db")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	fmt.Println("🔍 开始数据完整性验证...")

	// 获取所有订单数据
	var orders []OrderIntegrityCheck
	query := `
		SELECT id, platform_order_id, platform_type, original_amount, original_price,
		       paid_amount, platform_commission, actual_income, package_id, expires_at, platform_data
		FROM platform_orders
		ORDER BY id
	`
	
	err = db.Select(&orders, query)
	if err != nil {
		log.Fatal("查询订单数据失败:", err)
	}

	fmt.Printf("📊 总订单数: %d\n", len(orders))

	// 验证计数器
	var (
		totalOrders        = len(orders)
		validOrders        = 0
		invalidOrders      = 0
		missingFields      = 0
		incorrectCalculations = 0
	)

	fmt.Println("\n📋 详细验证结果:")
	fmt.Println(strings.Repeat("=", 80))

	for i, order := range orders {
		fmt.Printf("\n🔸 订单 %d: %s (%s)\n", i+1, order.PlatformOrderID, order.PlatformType)
		
		isValid := true
		issues := []string{}

		// 1. 检查必填字段
		if order.OriginalPrice == nil {
			issues = append(issues, "❌ original_price 为空")
			missingFields++
			isValid = false
		}

		if order.PlatformCommission == nil {
			issues = append(issues, "❌ platform_commission 为空")
			missingFields++
			isValid = false
		}

		if order.ActualIncome == nil {
			issues = append(issues, "❌ actual_income 为空")
			missingFields++
			isValid = false
		}

		if order.ExpiresAt == nil {
			issues = append(issues, "❌ expires_at 为空")
			missingFields++
			isValid = false
		}

		if order.PlatformData == nil {
			issues = append(issues, "❌ platform_data 为空")
			missingFields++
			isValid = false
		}

		// 2. 检查数据一致性
		if order.OriginalPrice != nil && *order.OriginalPrice != order.OriginalAmount {
			issues = append(issues, fmt.Sprintf("❌ original_price (%.2f) != original_amount (%.2f)", 
				*order.OriginalPrice, order.OriginalAmount))
			incorrectCalculations++
			isValid = false
		}

		// 3. 检查佣金计算
		if order.PlatformCommission != nil && order.ActualIncome != nil {
			expectedTotal := *order.PlatformCommission + *order.ActualIncome
			if abs(expectedTotal - order.PaidAmount) > 0.01 { // 允许0.01的浮点误差
				issues = append(issues, fmt.Sprintf("❌ 佣金+实际收入 (%.2f) != 支付金额 (%.2f)", 
					expectedTotal, order.PaidAmount))
				incorrectCalculations++
				isValid = false
			}
		}

		// 4. 检查佣金率合理性
		if order.PlatformCommission != nil && order.PaidAmount > 0 {
			commissionRate := *order.PlatformCommission / order.PaidAmount
			var expectedRate float64
			if order.PlatformType == "meituan" {
				expectedRate = 0.20
			} else if order.PlatformType == "eleme" {
				expectedRate = 0.18
			}

			if abs(commissionRate - expectedRate) > 0.01 {
				issues = append(issues, fmt.Sprintf("❌ 佣金率 (%.1f%%) 不符合预期 (%.1f%%)", 
					commissionRate*100, expectedRate*100))
				incorrectCalculations++
				isValid = false
			}
		}

		// 输出结果
		if isValid {
			fmt.Printf("  ✅ 数据完整且正确\n")
			fmt.Printf("     💰 原价: %.2f, 实付: %.2f, 佣金: %.2f, 实收: %.2f\n", 
				*order.OriginalPrice, order.PaidAmount, *order.PlatformCommission, *order.ActualIncome)
			validOrders++
		} else {
			fmt.Printf("  ❌ 发现问题:\n")
			for _, issue := range issues {
				fmt.Printf("     %s\n", issue)
			}
			invalidOrders++
		}
	}

	// 总结报告
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📊 验证总结报告:")
	fmt.Printf("  📦 总订单数: %d\n", totalOrders)
	fmt.Printf("  ✅ 有效订单: %d (%.1f%%)\n", validOrders, float64(validOrders)/float64(totalOrders)*100)
	fmt.Printf("  ❌ 无效订单: %d (%.1f%%)\n", invalidOrders, float64(invalidOrders)/float64(totalOrders)*100)
	fmt.Printf("  🔍 缺失字段: %d\n", missingFields)
	fmt.Printf("  🧮 计算错误: %d\n", incorrectCalculations)

	// 业务统计
	fmt.Println("\n💼 业务统计:")
	var businessStats struct {
		TotalIncome     float64 `db:"total_income"`
		TotalCommission float64 `db:"total_commission"`
		MeituanCount    int     `db:"meituan_count"`
		ElemeCount      int     `db:"eleme_count"`
		PendingCount    int     `db:"pending_count"`
		VerifiedCount   int     `db:"verified_count"`
	}

	err = db.Get(&businessStats, `
		SELECT 
			COALESCE(SUM(actual_income), 0) as total_income,
			COALESCE(SUM(platform_commission), 0) as total_commission,
			COALESCE(SUM(CASE WHEN platform_type = 'meituan' THEN 1 ELSE 0 END), 0) as meituan_count,
			COALESCE(SUM(CASE WHEN platform_type = 'eleme' THEN 1 ELSE 0 END), 0) as eleme_count,
			COALESCE(SUM(CASE WHEN verification_status = 'pending' THEN 1 ELSE 0 END), 0) as pending_count,
			COALESCE(SUM(CASE WHEN verification_status = 'verified' THEN 1 ELSE 0 END), 0) as verified_count
		FROM platform_orders
	`)

	if err != nil {
		fmt.Printf("❌ 业务统计查询失败: %v\n", err)
	} else {
		fmt.Printf("  🍔 美团订单: %d\n", businessStats.MeituanCount)
		fmt.Printf("  🛵 饿了么订单: %d\n", businessStats.ElemeCount)
		fmt.Printf("  ⏳ 待核销: %d\n", businessStats.PendingCount)
		fmt.Printf("  ✅ 已核销: %d\n", businessStats.VerifiedCount)
		fmt.Printf("  💰 总实际收入: %.2f 元\n", businessStats.TotalIncome)
		fmt.Printf("  💸 总平台佣金: %.2f 元\n", businessStats.TotalCommission)
		
		if businessStats.TotalIncome + businessStats.TotalCommission > 0 {
			commissionRate := businessStats.TotalCommission / (businessStats.TotalIncome + businessStats.TotalCommission) * 100
			fmt.Printf("  📊 平均佣金率: %.1f%%\n", commissionRate)
		}
	}

	// 最终结论
	fmt.Println("\n🎯 验证结论:")
	if invalidOrders == 0 {
		fmt.Println("  🎉 所有订单数据完整且正确！")
		fmt.Println("  ✅ 数据迁移成功完成")
	} else {
		fmt.Printf("  ⚠️  发现 %d 个订单存在问题，需要修复\n", invalidOrders)
		fmt.Println("  🔧 建议重新运行迁移脚本")
	}
}

// abs 计算浮点数绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
