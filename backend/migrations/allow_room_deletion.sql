-- 允许删除有历史订单的房间的数据库迁移
-- 这个迁移将修改订单表，允许 room_id 为 NULL，这样删除房间时不会被外键约束阻止

-- 由于 SQLite 不支持直接修改列约束，我们需要重建表

-- 1. 创建新的订单表（允许 room_id 为 NULL）
CREATE TABLE orders_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number TEXT DEFAULT '',
    user_id INTEGER,
    room_id INTEGER, -- 允许为 NULL
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0.00,
    status TEXT DEFAULT 'pending',
    package_type VARCHAR(20) DEFAULT 'hourly' CHECK (package_type IN ('hourly', 'package')),
    user_package_id INTEGER DEFAULT NULL,
    package_hours_used DECIMAL(8,2) DEFAULT 0,
    payment_method VARCHAR(20) DEFAULT 'wechat' CHECK (payment_method IN ('wechat', 'alipay', 'package', 'platform_voucher')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE SET NULL
);

-- 2. 复制数据到新表
INSERT INTO orders_new (
    id, order_number, user_id, room_id, start_time, end_time,
    total_amount, paid_amount, status, package_type, user_package_id,
    package_hours_used, payment_method, created_at, updated_at
) SELECT
    id, order_number, user_id, room_id, start_time, end_time,
    total_amount, paid_amount, status, package_type, user_package_id,
    package_hours_used, payment_method, created_at, updated_at
FROM orders;

-- 3. 删除旧表
DROP TABLE orders;

-- 4. 重命名新表
ALTER TABLE orders_new RENAME TO orders;

-- 5. 重建索引
CREATE INDEX idx_orders_active ON orders(status, start_time) WHERE status IN ('paid', 'in_use');
CREATE INDEX idx_orders_income ON orders(status, total_amount, created_at) WHERE status IN ('paid', 'completed');
CREATE INDEX idx_orders_user_stats ON orders(user_id, status, total_amount);
CREATE INDEX idx_orders_room_usage ON orders(room_id, start_time, end_time, status);
CREATE INDEX idx_orders_pagination ON orders(id DESC, created_at DESC);
CREATE INDEX idx_orders_user_join ON orders(user_id, id, created_at);
CREATE INDEX idx_orders_room_join ON orders(room_id, id, created_at);
CREATE INDEX idx_orders_date_stats ON orders(DATE(created_at), status, total_amount);
CREATE INDEX idx_orders_month_stats ON orders(strftime('%Y-%m', created_at), status, total_amount);
CREATE INDEX idx_orders_performance ON orders(created_at, user_id, room_id, status);
CREATE INDEX idx_orders_active_status ON orders(status, start_time);
CREATE INDEX idx_orders_amount_sort ON orders(total_amount DESC, created_at DESC);
CREATE INDEX idx_orders_room_frequency ON orders(room_id, created_at DESC);
CREATE INDEX idx_orders_admin_list ON orders(created_at DESC, id, user_id, room_id, status);
CREATE INDEX idx_orders_income_stats ON orders(status, total_amount, created_at);
CREATE INDEX idx_orders_user_consumption ON orders(user_id, total_amount, created_at);
CREATE INDEX idx_orders_room_income ON orders(room_id, total_amount, created_at);
CREATE INDEX idx_orders_device_usage ON orders(room_id, start_time, end_time);
CREATE INDEX idx_orders_basic_info ON orders(id, user_id, room_id, status, total_amount);
