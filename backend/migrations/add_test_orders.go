package main

import (
	"fmt"
	"log"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sqlx.Connect("sqlite3", "/home/<USER>/database/mahjong.db")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	fmt.Println("🚀 开始添加测试订单数据...")

	// 检查当前订单数量
	var currentCount int
	err = db.Get(&currentCount, "SELECT COUNT(*) FROM platform_orders")
	if err != nil {
		log.Fatal("查询当前订单数量失败:", err)
	}

	fmt.Printf("📊 当前订单数量: %d\n", currentCount)

	// 如果订单数量少于8个，添加更多测试数据
	if currentCount < 8 {
		testOrders := []map[string]interface{}{
			{
				"platform_type":       "eleme",
				"platform_order_id":   "EL202507270001",
				"room_id":             2,
				"user_id":             3,
				"customer_name":       "周八",
				"customer_phone":      "13800138006",
				"original_amount":     75.0,
				"discount_amount":     0.0,
				"paid_amount":         75.0,
				"order_status":        "paid",
				"verification_status": "pending",
				"verification_code":   "VF006",
				"order_items":         `{"items":[{"name":"粥","quantity":2,"price":12.00},{"name":"包子","quantity":6,"price":3.00},{"name":"豆浆","quantity":3,"price":5.00}]}`,
				"created_at":          "2025-07-27 09:15:00",
				"updated_at":          "2025-07-27 16:59:49",
			},
			{
				"platform_type":       "meituan",
				"platform_order_id":   "MT202507270002",
				"room_id":             5,
				"user_id":             1,
				"customer_name":       "吴九",
				"customer_phone":      "13800138007",
				"original_amount":     88.0,
				"discount_amount":     8.0,
				"paid_amount":         80.0,
				"order_status":        "paid",
				"verification_status": "verified",
				"verification_code":   "VF007",
				"order_items":         `{"items":[{"name":"面条","quantity":2,"price":18.00},{"name":"小菜","quantity":4,"price":6.00},{"name":"汤","quantity":2,"price":8.00}]}`,
				"created_at":          "2025-07-27 11:40:00",
				"updated_at":          "2025-07-27 16:59:49",
			},
			{
				"platform_type":       "eleme",
				"platform_order_id":   "EL202507270003",
				"room_id":             3,
				"user_id":             2,
				"customer_name":       "郑十",
				"customer_phone":      "13800138008",
				"original_amount":     110.0,
				"discount_amount":     10.0,
				"paid_amount":         100.0,
				"order_status":        "paid",
				"verification_status": "pending",
				"verification_code":   "VF008",
				"order_items":         `{"items":[{"name":"烧烤","quantity":1,"price":80.00},{"name":"啤酒","quantity":4,"price":6.00},{"name":"小食","quantity":2,"price":8.00}]}`,
				"created_at":          "2025-07-27 20:10:00",
				"updated_at":          "2025-07-27 16:59:49",
			},
		}

		// 开始事务
		tx, err := db.Beginx()
		if err != nil {
			log.Fatal("开始事务失败:", err)
		}
		defer tx.Rollback()

		addedCount := 0
		for _, order := range testOrders {
			// 检查订单是否已存在
			var exists int
			err := tx.Get(&exists, "SELECT COUNT(*) FROM platform_orders WHERE platform_order_id = ?", order["platform_order_id"])
			if err != nil {
				fmt.Printf("❌ 检查订单 %s 是否存在失败: %v\n", order["platform_order_id"], err)
				continue
			}

			if exists > 0 {
				fmt.Printf("⚠️  订单 %s 已存在，跳过\n", order["platform_order_id"])
				continue
			}

			// 插入订单
			insertQuery := `
				INSERT INTO platform_orders (
					platform_type, platform_order_id, room_id, user_id, customer_name, customer_phone,
					original_amount, discount_amount, paid_amount, order_status, verification_status,
					verification_code, order_items, created_at, updated_at,
					package_id, original_price, platform_commission, actual_income, expires_at, platform_data
				) VALUES (
					:platform_type, :platform_order_id, :room_id, :user_id, :customer_name, :customer_phone,
					:original_amount, :discount_amount, :paid_amount, :order_status, :verification_status,
					:verification_code, :order_items, :created_at, :updated_at,
					NULL, :original_amount, :platform_commission, :actual_income, :expires_at, :platform_data
				)
			`

			// 计算佣金和实际收入
			paidAmount := order["paid_amount"].(float64)
			var commissionRate float64
			if order["platform_type"].(string) == "meituan" {
				commissionRate = 0.20
			} else {
				commissionRate = 0.18
			}
			commission := paidAmount * commissionRate
			actualIncome := paidAmount - commission

			// 设置过期时间
			createdAt, _ := time.Parse("2006-01-02 15:04:05", order["created_at"].(string))
			expiresAt := createdAt.AddDate(0, 0, 7).Format("2006-01-02 15:04:05")

			// 创建平台数据
			platformData := fmt.Sprintf(`{
				"platform": "%s",
				"order_id": "%s",
				"migrated": false,
				"created_time": "%s",
				"commission_rate": %.2f,
				"original_amount": %.2f,
				"discount_amount": %.2f,
				"paid_amount": %.2f
			}`, order["platform_type"], order["platform_order_id"], time.Now().Format("2006-01-02 15:04:05"),
				commissionRate, order["original_amount"], order["discount_amount"], paidAmount)

			// 添加计算字段
			order["platform_commission"] = commission
			order["actual_income"] = actualIncome
			order["expires_at"] = expiresAt
			order["platform_data"] = platformData

			_, err = tx.NamedExec(insertQuery, order)
			if err != nil {
				fmt.Printf("❌ 插入订单 %s 失败: %v\n", order["platform_order_id"], err)
				continue
			}

			fmt.Printf("✅ 添加订单 %s 成功\n", order["platform_order_id"])
			addedCount++
		}

		// 提交事务
		if addedCount > 0 {
			err = tx.Commit()
			if err != nil {
				log.Fatal("提交事务失败:", err)
			}
			fmt.Printf("🎉 成功添加 %d 个测试订单\n", addedCount)
		} else {
			fmt.Println("📝 没有新订单需要添加")
		}
	} else {
		fmt.Println("📝 订单数量已足够，无需添加")
	}

	// 最终统计
	err = db.Get(&currentCount, "SELECT COUNT(*) FROM platform_orders")
	if err != nil {
		log.Fatal("查询最终订单数量失败:", err)
	}

	fmt.Printf("📊 最终订单数量: %d\n", currentCount)
	fmt.Println("✅ 测试数据准备完成！")
}
