package main

import (
	"fmt"
	"log"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/mattn/go-sqlite3"
)

// PlatformOrderMigration 外卖平台订单数据迁移
type PlatformOrderMigration struct {
	ID                 int     `db:"id"`
	PlatformType       string  `db:"platform_type"`
	PlatformOrderID    string  `db:"platform_order_id"`
	OriginalAmount     float64 `db:"original_amount"`
	DiscountAmount     float64 `db:"discount_amount"`
	PaidAmount         float64 `db:"paid_amount"`
	OrderStatus        string  `db:"order_status"`
	VerificationStatus string  `db:"verification_status"`
	CreatedAt          string  `db:"created_at"`
}

func main() {
	// 连接数据库
	db, err := sqlx.Connect("sqlite3", "/home/<USER>/database/mahjong.db")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	fmt.Println("🚀 开始外卖平台订单数据迁移...")

	// 获取所有需要迁移的订单
	var orders []PlatformOrderMigration
	query := `
		SELECT id, platform_type, platform_order_id, original_amount, 
		       discount_amount, paid_amount, order_status, verification_status, created_at
		FROM platform_orders 
		WHERE package_id IS NULL OR original_price IS NULL OR platform_commission IS NULL
	`
	
	err = db.Select(&orders, query)
	if err != nil {
		log.Fatal("查询订单数据失败:", err)
	}

	fmt.Printf("📊 找到 %d 个需要迁移的订单\n", len(orders))

	if len(orders) == 0 {
		fmt.Println("✅ 所有订单数据已经完整，无需迁移")
		return
	}

	// 开始事务
	tx, err := db.Beginx()
	if err != nil {
		log.Fatal("开始事务失败:", err)
	}
	defer tx.Rollback()

	successCount := 0
	errorCount := 0

	for i, order := range orders {
		fmt.Printf("📝 处理订单 %d/%d: %s\n", i+1, len(orders), order.PlatformOrderID)

		// 计算迁移数据
		migrationData := calculateMigrationData(order)

		// 更新订单数据
		updateQuery := `
			UPDATE platform_orders 
			SET package_id = ?, 
			    original_price = ?, 
			    platform_commission = ?, 
			    actual_income = ?, 
			    expires_at = ?, 
			    platform_data = ?
			WHERE id = ?
		`

		_, err := tx.Exec(updateQuery,
			migrationData.PackageID,
			migrationData.OriginalPrice,
			migrationData.PlatformCommission,
			migrationData.ActualIncome,
			migrationData.ExpiresAt,
			migrationData.PlatformData,
			order.ID,
		)

		if err != nil {
			fmt.Printf("❌ 更新订单 %s 失败: %v\n", order.PlatformOrderID, err)
			errorCount++
			continue
		}

		successCount++
		fmt.Printf("✅ 订单 %s 迁移成功\n", order.PlatformOrderID)
	}

	// 提交事务
	if errorCount == 0 {
		err = tx.Commit()
		if err != nil {
			log.Fatal("提交事务失败:", err)
		}
		fmt.Printf("🎉 数据迁移完成！成功迁移 %d 个订单\n", successCount)
	} else {
		fmt.Printf("⚠️  迁移过程中出现 %d 个错误，事务已回滚\n", errorCount)
		return
	}

	// 验证迁移结果
	fmt.Println("🔍 验证迁移结果...")
	verifyMigration(db)
}

// MigrationData 迁移数据结构
type MigrationData struct {
	PackageID          *int
	OriginalPrice      float64
	PlatformCommission float64
	ActualIncome       float64
	ExpiresAt          *string
	PlatformData       string
}

// calculateMigrationData 计算迁移数据
func calculateMigrationData(order PlatformOrderMigration) MigrationData {
	data := MigrationData{}

	// 1. package_id: 对于外卖订单，通常不关联套餐，设为 NULL
	data.PackageID = nil

	// 2. original_price: 使用 original_amount 作为原价
	data.OriginalPrice = order.OriginalAmount

	// 3. platform_commission: 根据平台类型计算佣金
	// 美团通常收取 18-22% 佣金，饿了么通常收取 15-20% 佣金
	var commissionRate float64
	switch order.PlatformType {
	case "meituan":
		commissionRate = 0.20 // 20%
	case "eleme":
		commissionRate = 0.18 // 18%
	default:
		commissionRate = 0.19 // 默认 19%
	}
	data.PlatformCommission = order.PaidAmount * commissionRate

	// 4. actual_income: 实际收入 = 支付金额 - 平台佣金
	data.ActualIncome = order.PaidAmount - data.PlatformCommission

	// 5. expires_at: 对于已完成的订单，设置过期时间为创建时间 + 30天
	if order.VerificationStatus == "verified" || order.OrderStatus == "completed" {
		// 解析创建时间
		createdAt, err := time.Parse("2006-01-02T15:04:05Z", order.CreatedAt)
		if err != nil {
			// 如果解析失败，使用当前时间
			createdAt = time.Now()
		}
		expiresAt := createdAt.AddDate(0, 0, 30) // 30天后过期
		expiresAtStr := expiresAt.Format("2006-01-02 15:04:05")
		data.ExpiresAt = &expiresAtStr
	} else {
		// 对于未完成的订单，设置过期时间为创建时间 + 7天
		createdAt, err := time.Parse("2006-01-02T15:04:05Z", order.CreatedAt)
		if err != nil {
			createdAt = time.Now()
		}
		expiresAt := createdAt.AddDate(0, 0, 7) // 7天后过期
		expiresAtStr := expiresAt.Format("2006-01-02 15:04:05")
		data.ExpiresAt = &expiresAtStr
	}

	// 6. platform_data: 创建基础的平台数据JSON
	data.PlatformData = fmt.Sprintf(`{
		"platform": "%s",
		"order_id": "%s",
		"migrated": true,
		"migration_time": "%s",
		"commission_rate": %.2f,
		"original_amount": %.2f,
		"discount_amount": %.2f,
		"paid_amount": %.2f
	}`, order.PlatformType, order.PlatformOrderID, time.Now().Format("2006-01-02 15:04:05"),
		commissionRate, order.OriginalAmount, order.DiscountAmount, order.PaidAmount)

	return data
}

// verifyMigration 验证迁移结果
func verifyMigration(db *sqlx.DB) {
	// 检查是否还有未迁移的数据
	var unmigratedCount int
	err := db.Get(&unmigratedCount, `
		SELECT COUNT(*) FROM platform_orders 
		WHERE package_id IS NULL AND original_price IS NULL
	`)
	if err != nil {
		fmt.Printf("❌ 验证失败: %v\n", err)
		return
	}

	if unmigratedCount > 0 {
		fmt.Printf("⚠️  还有 %d 个订单未完成迁移\n", unmigratedCount)
		return
	}

	// 统计迁移结果
	var stats struct {
		TotalOrders    int     `db:"total_orders"`
		TotalIncome    float64 `db:"total_income"`
		TotalCommission float64 `db:"total_commission"`
		MeituanOrders  int     `db:"meituan_orders"`
		ElemeOrders    int     `db:"eleme_orders"`
	}

	err = db.Get(&stats, `
		SELECT 
			COUNT(*) as total_orders,
			COALESCE(SUM(actual_income), 0) as total_income,
			COALESCE(SUM(platform_commission), 0) as total_commission,
			COALESCE(SUM(CASE WHEN platform_type = 'meituan' THEN 1 ELSE 0 END), 0) as meituan_orders,
			COALESCE(SUM(CASE WHEN platform_type = 'eleme' THEN 1 ELSE 0 END), 0) as eleme_orders
		FROM platform_orders
	`)

	if err != nil {
		fmt.Printf("❌ 统计失败: %v\n", err)
		return
	}

	fmt.Println("📊 迁移结果统计:")
	fmt.Printf("  📦 总订单数: %d\n", stats.TotalOrders)
	fmt.Printf("  🍔 美团订单: %d\n", stats.MeituanOrders)
	fmt.Printf("  🛵 饿了么订单: %d\n", stats.ElemeOrders)
	fmt.Printf("  💰 总实际收入: %.2f 元\n", stats.TotalIncome)
	fmt.Printf("  💸 总平台佣金: %.2f 元\n", stats.TotalCommission)
	fmt.Printf("  📈 平均佣金率: %.1f%%\n", (stats.TotalCommission/(stats.TotalIncome+stats.TotalCommission))*100)

	fmt.Println("✅ 数据迁移验证完成！")
}
