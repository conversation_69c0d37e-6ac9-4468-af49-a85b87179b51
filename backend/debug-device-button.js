const { chromium } = require('playwright');

(async () => {
  console.log('🚀 启动浏览器进行详细调试...');
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-dev-shm-usage']
  });
  
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const text = msg.text();
    if (text.includes('设备按钮被点击') || text.includes('准备跳转') || text.includes('路由跳转失败')) {
      console.log('🎯 重要消息:', text);
    } else if (!text.includes('ElementPlusError') && !text.includes('[vite]')) {
      console.log('🖥️ 浏览器控制台:', text);
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log('❌ 页面错误:', error.message);
  });
  
  console.log('📄 访问房间列表页面...');
  await page.goto('http://localhost:3000/rooms/list');
  
  // 等待页面完全加载
  await page.waitForLoadState('networkidle');
  
  console.log('🔍 查找设备按钮...');

  // 尝试不同的选择器
  const deviceButtons1 = await page.locator('text=设备').count();
  const deviceButtons2 = await page.locator('button:has-text("设备")').count();
  const deviceButtons3 = await page.locator('el-button:has-text("设备")').count();

  console.log(`text=设备: ${deviceButtons1} 个`);
  console.log(`button:has-text("设备"): ${deviceButtons2} 个`);
  console.log(`el-button:has-text("设备"): ${deviceButtons3} 个`);

  // 使用更精确的选择器
  const deviceButtons = await page.locator('button:has-text("设备")');
  const count = await deviceButtons.count();
  console.log(`使用精确选择器找到 ${count} 个设备按钮`);
  
  if (count > 0) {
    // 获取第一个按钮的详细信息
    const firstButton = deviceButtons.first();
    const buttonText = await firstButton.textContent();
    const isVisible = await firstButton.isVisible();
    const isEnabled = await firstButton.isEnabled();
    
    console.log('📋 按钮信息:');
    console.log('  - 文本:', buttonText);
    console.log('  - 可见:', isVisible);
    console.log('  - 可用:', isEnabled);

    // 检查按钮的HTML结构
    const buttonHTML = await firstButton.innerHTML();
    console.log('🔍 按钮HTML:', buttonHTML);

    // 检查按钮的父元素
    const parentHTML = await firstButton.locator('..').innerHTML();
    console.log('🔍 父元素HTML:', parentHTML.substring(0, 200) + '...');
    
    console.log('👆 点击第一个设备按钮...');
    await firstButton.click();

    // 等待可能的页面变化
    console.log('⏳ 等待页面响应...');
    await page.waitForTimeout(2000);

    // 检查是否有成功消息出现
    const successMessage = await page.locator('.el-message--success').count();
    console.log('📢 成功消息数量:', successMessage);

    // 再等待一下路由跳转
    console.log('⏳ 等待路由跳转...');
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    console.log('📍 点击后URL:', currentUrl);
    
    // 检查是否有路由变化
    if (currentUrl.includes('/devices/list')) {
      console.log('✅ 成功跳转到设备管理页面！');
    } else {
      console.log('❌ 未能跳转，检查控制台消息...');
    }
  }
  
  console.log('🔒 关闭浏览器...');
  await browser.close();
  console.log('✅ 调试完成!');
})().catch(error => {
  console.error('❌ 调试失败:', error);
  process.exit(1);
});
