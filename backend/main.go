package main

import (
	"log"
	"mahjong-system/config"
	"mahjong-system/logger"
	"mahjong-system/mqtt"
	"mahjong-system/repositories"
	"mahjong-system/routes"
	"mahjong-system/services"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/zap"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("./config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 初始化日志系统
	if err := logger.InitLogger(&cfg.Log); err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}
	defer logger.Sync()

	logger.Info("自助麻将室系统启动", zap.String("mode", cfg.Server.Mode))

	// 初始化数据库
	db, err := repositories.NewDatabase(cfg.Database.DSN)
	if err != nil {
		logger.Fatal("数据库初始化失败", zap.Error(err))
	}
	defer db.Close()

	// 创建仓储层
	repos := repositories.NewRepositories(db)

	// 创建服务层
	serviceManager := services.NewServiceManager(repos)

	// 初始化MQTT管理器
	mqttManager := mqtt.NewManager(cfg.GetMQTTConfig(), serviceManager)
	// 临时注释掉MQTT启动检查以测试API
	if err := mqttManager.Start(); err != nil {
		logger.Warn("MQTT管理器启动失败，继续运行以测试API", zap.Error(err))
	}
	defer mqttManager.Stop()

	// 设置路由
	r := routes.SetupRoutes(serviceManager, mqttManager)

	// 设置优雅关闭
	setupGracefulShutdown(mqttManager)

	// 启动HTTP服务器
	address := cfg.GetServerAddress()
	logger.Info("HTTP服务器启动", zap.String("address", address))
	log.Fatal(http.ListenAndServe(address, r))
}

// setupGracefulShutdown 设置优雅关闭
func setupGracefulShutdown(mqttManager *mqtt.Manager) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		logger.Info("接收到关闭信号，正在优雅关闭...")

		// 停止MQTT管理器
		mqttManager.Stop()

		// 同步日志
		logger.Sync()

		os.Exit(0)
	}()
}
