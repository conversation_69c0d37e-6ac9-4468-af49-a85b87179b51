package mqtt

import (
	"fmt"
	"log"
	"mahjong-system/services"
	"time"
)

// Manager MQTT管理器
type Manager struct {
	client           *MQTTClient
	deviceController *DeviceController
	serviceManager   *services.ServiceManager
	config           *MQTTConfig
	stopChan         chan bool
}

// NewManager 创建MQTT管理器
func NewManager(config *MQTTConfig, serviceManager *services.ServiceManager) *Manager {
	// 创建MQTT客户端
	client := NewMQTTClient(config, serviceManager)
	
	// 创建设备控制器
	deviceController := NewDeviceController(client, serviceManager)

	return &Manager{
		client:           client,
		deviceController: deviceController,
		serviceManager:   serviceManager,
		config:           config,
		stopChan:         make(chan bool),
	}
}

// Start 启动MQTT管理器
func (m *Manager) Start() error {
	log.Println("启动MQTT管理器...")

	// 连接MQTT服务器
	if err := m.client.Connect(); err != nil {
		return err
	}

	// 启动定时任务
	go m.startPeriodicTasks()

	log.Println("MQTT管理器启动成功")
	return nil
}

// Stop 停止MQTT管理器
func (m *Manager) Stop() {
	log.Println("停止MQTT管理器...")
	
	// 发送停止信号
	close(m.stopChan)
	
	// 断开MQTT连接
	m.client.Disconnect()
	
	log.Println("MQTT管理器已停止")
}

// GetDeviceController 获取设备控制器
func (m *Manager) GetDeviceController() *DeviceController {
	return m.deviceController
}

// GetServiceManager 获取服务管理器
func (m *Manager) GetServiceManager() *services.ServiceManager {
	return m.serviceManager
}

// IsConnected 检查MQTT连接状态
func (m *Manager) IsConnected() bool {
	return m.client.IsConnected()
}

// startPeriodicTasks 启动定时任务
func (m *Manager) startPeriodicTasks() {
	// 设备状态检查定时器（每分钟检查一次）
	deviceCheckTicker := time.NewTicker(1 * time.Minute)
	defer deviceCheckTicker.Stop()

	// 订单时间提醒定时器（每5分钟检查一次）
	orderReminderTicker := time.NewTicker(5 * time.Minute)
	defer orderReminderTicker.Stop()

	// 设备心跳超时检查定时器（每30秒检查一次）
	heartbeatCheckTicker := time.NewTicker(30 * time.Second)
	defer heartbeatCheckTicker.Stop()

	for {
		select {
		case <-m.stopChan:
			log.Println("定时任务已停止")
			return

		case <-deviceCheckTicker.C:
			m.checkDeviceStatus()

		case <-orderReminderTicker.C:
			m.checkOrderTimeReminders()

		case <-heartbeatCheckTicker.C:
			m.checkDeviceHeartbeat()
		}
	}
}

// checkDeviceStatus 检查设备状态
func (m *Manager) checkDeviceStatus() {
	log.Println("检查设备状态...")

	// 获取所有设备统计
	stats, err := m.serviceManager.Device.GetDeviceStatistics()
	if err != nil {
		log.Printf("获取设备统计失败: %v", err)
		return
	}

	log.Printf("设备状态统计 - 总数: %d, 在线: %d, 离线: %d", 
		stats["total"], stats["online"], stats["offline"])

	// 如果离线设备过多，发送告警
	if stats["total"] > 0 {
		offlineRate := float64(stats["offline"]) / float64(stats["total"])
		if offlineRate > 0.3 { // 超过30%的设备离线
			log.Printf("警告：设备离线率过高 %.1f%%", offlineRate*100)
			// 这里可以发送告警通知
		}
	}
}

// checkOrderTimeReminders 检查订单时间提醒
func (m *Manager) checkOrderTimeReminders() {
	log.Println("检查订单时间提醒...")

	// 获取所有进行中的订单
	filters := map[string]interface{}{
		"status": "paid", // 已支付状态的订单
	}

	orders, _, err := m.serviceManager.Order.GetOrderList(1, 100, filters)
	if err != nil {
		log.Printf("获取订单列表失败: %v", err)
		return
	}

	now := time.Now()
	for _, order := range orders {
		if order.Room == nil {
			continue
		}

		// 计算已使用时间
		usedMinutes := int(now.Sub(order.StartTime).Minutes())
		
		// 计算预计结束时间（基于已支付金额）
		// 这里简化处理，假设每小时20元
		paidHours := int(order.PaidAmount / 20)
		paidMinutes := paidHours * 60
		
		remainingMinutes := paidMinutes - usedMinutes

		// 发送提醒
		if remainingMinutes == 15 || remainingMinutes == 5 {
			// 剩余15分钟或5分钟时提醒
			err := m.deviceController.SendTimeReminder(order.Room.ID, remainingMinutes)
			if err != nil {
				log.Printf("发送时间提醒失败: %v", err)
			}
		} else if remainingMinutes <= 0 {
			// 时间已到，发送警告
			err := m.deviceController.SendWarningMessage(order.Room.ID, "您的使用时间已到，请及时续费或结束使用")
			if err != nil {
				log.Printf("发送超时警告失败: %v", err)
			}
		}
	}
}

// checkDeviceHeartbeat 检查设备心跳超时
func (m *Manager) checkDeviceHeartbeat() {
	// 获取离线设备列表
	offlineDevices, err := m.serviceManager.Device.GetOfflineDevices()
	if err != nil {
		log.Printf("获取离线设备失败: %v", err)
		return
	}

	if len(offlineDevices) > 0 {
		log.Printf("发现 %d 个离线设备", len(offlineDevices))
		
		// 更新离线设备状态
		for _, device := range offlineDevices {
			if device.Status != "offline" {
				err := m.serviceManager.Device.UpdateDeviceStatus(device.ID, "offline")
				if err != nil {
					log.Printf("更新设备离线状态失败: %v", err)
				} else {
					log.Printf("设备 %s 已标记为离线", device.MacAddress)
				}
			}
		}
	}
}

// HandleOrderStart 处理订单开始
func (m *Manager) HandleOrderStart(orderID int, roomID int) error {
	log.Printf("处理订单开始: 订单ID=%d, 房间ID=%d", orderID, roomID)
	
	return m.deviceController.StartRoomSession(roomID, orderID)
}

// HandleOrderEnd 处理订单结束
func (m *Manager) HandleOrderEnd(orderID int, roomID int) error {
	log.Printf("处理订单结束: 订单ID=%d, 房间ID=%d", orderID, roomID)
	
	return m.deviceController.EndRoomSession(roomID, orderID)
}

// HandleOrderExtend 处理订单续费
func (m *Manager) HandleOrderExtend(orderID int, roomID int, extendHours int) error {
	log.Printf("处理订单续费: 订单ID=%d, 房间ID=%d, 续费小时=%d", orderID, roomID, extendHours)

	// 发送续费成功提醒
	_ = fmt.Sprintf("续费成功，已延长 %d 小时使用时间", extendHours)
	return m.deviceController.SendTimeReminder(roomID, extendHours*60)
}

// GetRoomDeviceStatus 获取房间设备状态
func (m *Manager) GetRoomDeviceStatus(roomID int) (map[string]interface{}, error) {
	return m.deviceController.GetRoomDeviceStatus(roomID)
}

// SendCustomMessage 发送自定义消息到房间
func (m *Manager) SendCustomMessage(roomID int, message string) error {
	return m.deviceController.SendWarningMessage(roomID, message)
}

// GetConnectionStatus 获取连接状态信息
func (m *Manager) GetConnectionStatus() map[string]interface{} {
	return map[string]interface{}{
		"mqtt_connected": m.client.IsConnected(),
		"broker":         m.config.Broker,
		"client_id":      m.config.ClientID,
		"topics": map[string]string{
			"control":   m.config.Topics.Control,
			"status":    m.config.Topics.Status,
			"heartbeat": m.config.Topics.Heartbeat,
		},
	}
}
