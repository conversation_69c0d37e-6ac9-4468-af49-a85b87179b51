package mqtt

import (
	"encoding/json"
	"fmt"
	"log"
	"mahjong-system/models"
	"mahjong-system/services"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// MQTTClient MQTT客户端管理器
type MQTTClient struct {
	client         mqtt.Client
	serviceManager *services.ServiceManager
	config         *MQTTConfig
}

// MQTTConfig MQTT配置
type MQTTConfig struct {
	Broker   string `yaml:"broker"`
	ClientID string `yaml:"client_id"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	QoS      byte   `yaml:"qos"`
	Topics   struct {
		Control   string `yaml:"control"`
		Status    string `yaml:"status"`
		Heartbeat string `yaml:"heartbeat"`
	} `yaml:"topics"`
}

// NewMQTTClient 创建MQTT客户端
func NewMQTTClient(config *MQTTConfig, serviceManager *services.ServiceManager) *MQTTClient {
	client := &MQTTClient{
		serviceManager: serviceManager,
		config:         config,
	}

	// 设置MQTT客户端选项
	opts := mqtt.NewClientOptions()
	opts.AddBroker(config.Broker)
	opts.SetClientID(config.ClientID)
	opts.SetUsername(config.Username)
	opts.SetPassword(config.Password)
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetCleanSession(true)

	// 设置连接回调
	opts.SetOnConnectHandler(client.onConnect)
	opts.SetConnectionLostHandler(client.onConnectionLost)

	// 创建MQTT客户端
	client.client = mqtt.NewClient(opts)

	return client
}

// Connect 连接到MQTT服务器
func (c *MQTTClient) Connect() error {
	log.Println("正在连接MQTT服务器...")
	
	token := c.client.Connect()
	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("MQTT连接失败: %v", token.Error())
	}

	log.Println("MQTT连接成功")
	return nil
}

// Disconnect 断开MQTT连接
func (c *MQTTClient) Disconnect() {
	log.Println("断开MQTT连接")
	c.client.Disconnect(250)
}

// onConnect 连接成功回调
func (c *MQTTClient) onConnect(client mqtt.Client) {
	log.Println("MQTT连接建立成功")

	// 订阅设备状态主题
	c.subscribeToTopic(c.config.Topics.Status, c.handleStatusMessage)
	
	// 订阅设备心跳主题
	c.subscribeToTopic(c.config.Topics.Heartbeat, c.handleHeartbeatMessage)
}

// onConnectionLost 连接丢失回调
func (c *MQTTClient) onConnectionLost(client mqtt.Client, err error) {
	log.Printf("MQTT连接丢失: %v", err)
	
	// 尝试重连
	go c.reconnect()
}

// reconnect 重连逻辑
func (c *MQTTClient) reconnect() {
	for {
		log.Println("尝试重新连接MQTT服务器...")
		time.Sleep(5 * time.Second)
		
		if token := c.client.Connect(); token.Wait() && token.Error() == nil {
			log.Println("MQTT重连成功")
			break
		}
	}
}

// subscribeToTopic 订阅主题
func (c *MQTTClient) subscribeToTopic(topic string, handler mqtt.MessageHandler) {
	token := c.client.Subscribe(topic, c.config.QoS, handler)
	if token.Wait() && token.Error() != nil {
		log.Printf("订阅主题失败 %s: %v", topic, token.Error())
	} else {
		log.Printf("成功订阅主题: %s", topic)
	}
}

// handleStatusMessage 处理设备状态消息
func (c *MQTTClient) handleStatusMessage(client mqtt.Client, msg mqtt.Message) {
	log.Printf("收到设备状态消息: %s -> %s", msg.Topic(), string(msg.Payload()))

	var statusMsg models.MQTTMessage
	if err := json.Unmarshal(msg.Payload(), &statusMsg); err != nil {
		log.Printf("解析状态消息失败: %v", err)
		return
	}

	// 从主题中提取设备MAC地址
	// 主题格式: mahjong/device/{mac_address}/status
	macAddress := c.extractMacFromTopic(msg.Topic())
	if macAddress == "" {
		log.Printf("无法从主题中提取MAC地址: %s", msg.Topic())
		return
	}

	// 更新设备状态
	c.updateDeviceStatus(macAddress, &statusMsg)
}

// handleHeartbeatMessage 处理设备心跳消息
func (c *MQTTClient) handleHeartbeatMessage(client mqtt.Client, msg mqtt.Message) {
	log.Printf("收到设备心跳: %s -> %s", msg.Topic(), string(msg.Payload()))

	var heartbeatMsg models.MQTTMessage
	if err := json.Unmarshal(msg.Payload(), &heartbeatMsg); err != nil {
		log.Printf("解析心跳消息失败: %v", err)
		return
	}

	// 从主题中提取设备MAC地址
	macAddress := c.extractMacFromTopic(msg.Topic())
	if macAddress == "" {
		log.Printf("无法从主题中提取MAC地址: %s", msg.Topic())
		return
	}

	// 更新设备心跳时间
	c.updateDeviceHeartbeat(macAddress, &heartbeatMsg)
}

// extractMacFromTopic 从主题中提取MAC地址
func (c *MQTTClient) extractMacFromTopic(topic string) string {
	// 主题格式: mahjong/device/{mac_address}/status 或 mahjong/device/{mac_address}/heartbeat
	// 简单的字符串分割实现
	parts := []string{}
	current := ""
	for _, char := range topic {
		if char == '/' {
			if current != "" {
				parts = append(parts, current)
				current = ""
			}
		} else {
			current += string(char)
		}
	}
	if current != "" {
		parts = append(parts, current)
	}

	// 期望格式: ["mahjong", "device", "{mac_address}", "status/heartbeat"]
	if len(parts) >= 3 && parts[0] == "mahjong" && parts[1] == "device" {
		return parts[2]
	}

	return ""
}

// updateDeviceStatus 更新设备状态
func (c *MQTTClient) updateDeviceStatus(macAddress string, statusMsg *models.MQTTMessage) {
	// 根据MAC地址查找设备
	device, err := c.serviceManager.Device.GetDeviceByMacAddress(macAddress)
	if err != nil {
		log.Printf("查找设备失败 %s: %v", macAddress, err)
		return
	}
	if device == nil {
		log.Printf("设备不存在: %s", macAddress)
		return
	}

	// 更新设备状态
	if statusMsg.Status != "" {
		err = c.serviceManager.Device.UpdateDeviceStatus(device.ID, statusMsg.Status)
		if err != nil {
			log.Printf("更新设备状态失败: %v", err)
		} else {
			log.Printf("设备状态已更新: %s -> %s", macAddress, statusMsg.Status)
		}
	}
}

// updateDeviceHeartbeat 更新设备心跳
func (c *MQTTClient) updateDeviceHeartbeat(macAddress string, heartbeatMsg *models.MQTTMessage) {
	// 根据MAC地址查找设备
	device, err := c.serviceManager.Device.GetDeviceByMacAddress(macAddress)
	if err != nil {
		log.Printf("查找设备失败 %s: %v", macAddress, err)
		return
	}
	if device == nil {
		log.Printf("设备不存在: %s", macAddress)
		return
	}

	// 更新设备心跳时间
	err = c.serviceManager.Device.UpdateHeartbeat(device.ID)
	if err != nil {
		log.Printf("更新设备心跳失败: %v", err)
	} else {
		log.Printf("设备心跳已更新: %s", macAddress)
	}
}

// PublishControlMessage 发布设备控制消息
func (c *MQTTClient) PublishControlMessage(macAddress string, command string, params map[string]interface{}) error {
	// 构建控制消息
	controlMsg := models.MQTTMessage{
		Command:   command,
		Timestamp: time.Now(),
		RequestID: fmt.Sprintf("req_%d", time.Now().UnixNano()),
		Params:    params,
	}

	// 序列化消息
	payload, err := json.Marshal(controlMsg)
	if err != nil {
		return fmt.Errorf("序列化控制消息失败: %v", err)
	}

	// 构建主题
	topic := fmt.Sprintf("mahjong/device/%s/control", macAddress)

	// 发布消息
	token := c.client.Publish(topic, c.config.QoS, false, payload)
	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("发布控制消息失败: %v", token.Error())
	}

	log.Printf("控制消息已发送: %s -> %s", topic, string(payload))
	return nil
}

// IsConnected 检查连接状态
func (c *MQTTClient) IsConnected() bool {
	return c.client.IsConnected()
}
