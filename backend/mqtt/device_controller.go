package mqtt

import (
	"fmt"
	"log"
	"mahjong-system/models"
	"mahjong-system/services"
)

// DeviceController 设备控制器
type DeviceController struct {
	mqttClient     *MQTTClient
	serviceManager *services.ServiceManager
}

// NewDeviceController 创建设备控制器
func NewDeviceController(mqttClient *MQTTClient, serviceManager *services.ServiceManager) *DeviceController {
	return &DeviceController{
		mqttClient:     mqttClient,
		serviceManager: serviceManager,
	}
}

// OpenRoomLock 开启房间门锁
func (dc *DeviceController) OpenRoomLock(roomID int) error {
	// 查找房间门锁设备
	lockDevice, err := dc.getRoomDevice(roomID, models.DeviceTypeRoomLock)
	if err != nil {
		return fmt.Errorf("获取房间门锁失败: %v", err)
	}

	// 发送开锁命令
	params := map[string]interface{}{
		"action": "open",
		"duration": 5, // 开锁持续时间（秒）
	}

	err = dc.mqttClient.PublishControlMessage(lockDevice.MacAddress, models.CommandUnlock, params)
	if err != nil {
		return fmt.Errorf("发送开锁命令失败: %v", err)
	}

	log.Printf("房间 %d 门锁开启命令已发送", roomID)
	return nil
}

// CloseRoomLock 关闭房间门锁
func (dc *DeviceController) CloseRoomLock(roomID int) error {
	// 优先查找新的房间门锁类型
	lockDevice, err := dc.getRoomDevice(roomID, models.DeviceTypeRoomLock)
	if err != nil {
		return fmt.Errorf("获取房间门锁失败: %v", err)
	}

	// 发送关锁命令
	params := map[string]interface{}{
		"action": "close",
	}

	err = dc.mqttClient.PublishControlMessage(lockDevice.MacAddress, models.CommandLock, params)
	if err != nil {
		return fmt.Errorf("发送关锁命令失败: %v", err)
	}

	log.Printf("房间 %d 门锁关闭命令已发送", roomID)
	return nil
}

// PowerOnRoomDevices 开启房间设备电源
func (dc *DeviceController) PowerOnRoomDevices(roomID int) error {
	// 查找房间电源控制设备
	powerDevice, err := dc.getRoomDevice(roomID, models.DeviceTypePower)
	if err != nil {
		return fmt.Errorf("获取房间电源控制设备失败: %v", err)
	}

	// 发送开电命令
	params := map[string]interface{}{
		"action": "on",
		"outlets": []string{"all"}, // 开启所有插座
	}

	err = dc.mqttClient.PublishControlMessage(powerDevice.MacAddress, models.CommandPowerOn, params)
	if err != nil {
		return fmt.Errorf("发送开电命令失败: %v", err)
	}

	log.Printf("房间 %d 设备电源开启命令已发送", roomID)
	return nil
}

// PowerOffRoomDevices 关闭房间设备电源
func (dc *DeviceController) PowerOffRoomDevices(roomID int) error {
	// 优先查找新的电源控制设备类型
	powerDevice, err := dc.getRoomDevice(roomID, models.DeviceTypePower)
	if err != nil {
		return fmt.Errorf("获取房间电源控制设备失败: %v", err)
	}

	// 发送关电命令
	params := map[string]interface{}{
		"action": "off",
		"outlets": []string{"all"}, // 关闭所有插座
	}

	err = dc.mqttClient.PublishControlMessage(powerDevice.MacAddress, models.CommandPowerOff, params)
	if err != nil {
		return fmt.Errorf("发送关电命令失败: %v", err)
	}

	log.Printf("房间 %d 设备电源关闭命令已发送", roomID)
	return nil
}



// SendAudioMessage 发送音频消息到房间喇叭
func (dc *DeviceController) SendAudioMessage(roomID int, message string, messageType string, volume int) error {
	// 获取房间的音响设备
	speakerDevice, err := dc.getRoomDevice(roomID, models.DeviceTypeSpeaker)
	if err != nil {
		return fmt.Errorf("获取房间音响设备失败: %v", err)
	}

	// 构建音频消息参数
	params := map[string]interface{}{
		"message": message,
		"type":    messageType,
		"volume":  volume,
		"tts":     true, // 启用文字转语音
	}

	// 发送音频播放命令
	err = dc.mqttClient.PublishControlMessage(speakerDevice.MacAddress, models.CommandPlayAudio, params)
	if err != nil {
		return fmt.Errorf("发送音频消息失败: %v", err)
	}

	log.Printf("房间 %d 音频消息已发送: %s", roomID, message)
	return nil
}

// PlayRoomAudio 播放房间音频文件
func (dc *DeviceController) PlayRoomAudio(roomID int, audioFile string, volume int) error {
	// 获取房间的音响设备
	speakerDevice, err := dc.getRoomDevice(roomID, models.DeviceTypeSpeaker)
	if err != nil {
		return fmt.Errorf("获取房间音响设备失败: %v", err)
	}

	// 构建音频播放参数
	params := map[string]interface{}{
		"file":   audioFile,
		"volume": volume,
	}

	// 发送音频播放命令
	err = dc.mqttClient.PublishControlMessage(speakerDevice.MacAddress, models.CommandPlayAudio, params)
	if err != nil {
		return fmt.Errorf("播放音频文件失败: %v", err)
	}

	log.Printf("房间 %d 音频文件播放命令已发送: %s", roomID, audioFile)
	return nil
}

// StopRoomAudio 停止房间音频播放
func (dc *DeviceController) StopRoomAudio(roomID int) error {
	// 获取房间的音响设备
	speakerDevice, err := dc.getRoomDevice(roomID, models.DeviceTypeSpeaker)
	if err != nil {
		return fmt.Errorf("获取房间音响设备失败: %v", err)
	}

	// 发送停止播放命令
	err = dc.mqttClient.PublishControlMessage(speakerDevice.MacAddress, models.CommandStopAudio, nil)
	if err != nil {
		return fmt.Errorf("停止音频播放失败: %v", err)
	}

	log.Printf("房间 %d 音频播放停止命令已发送", roomID)
	return nil
}

// SetRoomAudioVolume 设置房间音响音量
func (dc *DeviceController) SetRoomAudioVolume(roomID int, volume int) error {
	// 获取房间的音响设备
	speakerDevice, err := dc.getRoomDevice(roomID, models.DeviceTypeSpeaker)
	if err != nil {
		return fmt.Errorf("获取房间音响设备失败: %v", err)
	}

	// 构建音量设置参数
	params := map[string]interface{}{
		"volume": volume,
	}

	// 发送音量设置命令
	err = dc.mqttClient.PublishControlMessage(speakerDevice.MacAddress, models.CommandSetVolume, params)
	if err != nil {
		return fmt.Errorf("设置音响音量失败: %v", err)
	}

	log.Printf("房间 %d 音响音量设置命令已发送: %d", roomID, volume)
	return nil
}

// BatchPowerOffAllRooms 批量关闭所有房间电源
func (dc *DeviceController) BatchPowerOffAllRooms() error {
	// 获取所有房间的电源设备
	powerDevices, err := dc.serviceManager.Device.GetDevicesByType(models.DeviceTypePower)
	if err != nil {
		return fmt.Errorf("获取电源设备失败: %v", err)
	}

	// 批量发送关闭电源命令
	params := map[string]interface{}{
		"action":  "off",
		"outlets": []string{"all"},
	}

	successCount := 0
	for _, device := range powerDevices {
		err = dc.mqttClient.PublishControlMessage(device.MacAddress, models.CommandPowerOff, params)
		if err != nil {
			log.Printf("关闭设备 %s 电源失败: %v", device.MacAddress, err)
		} else {
			successCount++
		}
	}

	log.Printf("批量关闭电源完成，成功: %d/%d", successCount, len(powerDevices))
	return nil
}

// BatchLockAllRooms 批量锁定所有房间门锁
func (dc *DeviceController) BatchLockAllRooms() error {
	// 获取所有房间门锁设备
	lockDevices, err := dc.serviceManager.Device.GetDevicesByType(models.DeviceTypeRoomLock)
	if err != nil {
		return fmt.Errorf("获取门锁设备失败: %v", err)
	}

	// 批量发送锁门命令
	params := map[string]interface{}{
		"action": "close",
	}

	successCount := 0
	for _, device := range lockDevices {
		err = dc.mqttClient.PublishControlMessage(device.MacAddress, models.CommandLock, params)
		if err != nil {
			log.Printf("锁定设备 %s 失败: %v", device.MacAddress, err)
		} else {
			successCount++
		}
	}

	log.Printf("批量锁定门锁完成，成功: %d/%d", successCount, len(lockDevices))
	return nil
}

// BroadcastAudioMessage 广播音频消息到所有房间
func (dc *DeviceController) BroadcastAudioMessage(message string, messageType string, volume int) error {
	// 获取所有音响设备
	speakerDevices, err := dc.serviceManager.Device.GetDevicesByType(models.DeviceTypeSpeaker)
	if err != nil {
		return fmt.Errorf("获取音响设备失败: %v", err)
	}

	// 构建音频消息参数
	params := map[string]interface{}{
		"message": message,
		"type":    messageType,
		"volume":  volume,
		"tts":     true,
	}

	successCount := 0
	for _, device := range speakerDevices {
		err = dc.mqttClient.PublishControlMessage(device.MacAddress, models.CommandPlayAudio, params)
		if err != nil {
			log.Printf("向设备 %s 发送音频消息失败: %v", device.MacAddress, err)
		} else {
			successCount++
		}
	}

	log.Printf("广播音频消息完成，成功: %d/%d，消息: %s", successCount, len(speakerDevices), message)
	return nil
}

// StartRoomSession 开始房间使用会话
func (dc *DeviceController) StartRoomSession(roomID int, orderID int) error {
	log.Printf("开始房间 %d 使用会话，订单ID: %d", roomID, orderID)

	// 1. 开启门锁
	if err := dc.OpenRoomLock(roomID); err != nil {
		return fmt.Errorf("开启门锁失败: %v", err)
	}

	// 2. 开启设备电源
	if err := dc.PowerOnRoomDevices(roomID); err != nil {
		return fmt.Errorf("开启设备电源失败: %v", err)
	}

	// 3. 房间使用会话已准备就绪
	log.Printf("房间 %d 设备已准备就绪", roomID)

	log.Printf("房间 %d 使用会话已开始", roomID)
	return nil
}

// EndRoomSession 结束房间使用会话
func (dc *DeviceController) EndRoomSession(roomID int, orderID int) error {
	log.Printf("结束房间 %d 使用会话，订单ID: %d", roomID, orderID)

	// 1. 关闭设备电源
	if err := dc.PowerOffRoomDevices(roomID); err != nil {
		log.Printf("关闭设备电源失败（非致命错误）: %v", err)
	}

	// 2. 关闭门锁
	if err := dc.CloseRoomLock(roomID); err != nil {
		return fmt.Errorf("关闭门锁失败: %v", err)
	}

	// 3. 房间使用会话清理完成
	log.Printf("房间 %d 设备已安全关闭", roomID)

	log.Printf("房间 %d 使用会话已结束", roomID)
	return nil
}

// SendWarningMessage 发送警告消息
func (dc *DeviceController) SendWarningMessage(roomID int, message string) error {
	// 获取房间的所有设备，发送警告消息
	devices, err := dc.serviceManager.Device.GetDevicesByRoomID(roomID)
	if err != nil {
		return fmt.Errorf("获取房间设备失败: %v", err)
	}

	params := map[string]interface{}{
		"type":    "warning",
		"message": message,
		"level":   "high",
	}

	// 向所有设备发送警告
	for _, device := range devices {
		err = dc.mqttClient.PublishControlMessage(device.MacAddress, "warning", params)
		if err != nil {
			log.Printf("向设备 %s 发送警告失败: %v", device.MacAddress, err)
		}
	}

	log.Printf("房间 %d 警告消息已发送: %s", roomID, message)
	return nil
}

// SendTimeReminder 发送时间提醒
func (dc *DeviceController) SendTimeReminder(roomID int, remainingMinutes int) error {
	// 获取房间的所有设备
	devices, err := dc.serviceManager.Device.GetDevicesByRoomID(roomID)
	if err != nil {
		return fmt.Errorf("获取房间设备失败: %v", err)
	}

	var message string
	if remainingMinutes <= 15 {
		message = fmt.Sprintf("您的使用时间还剩 %d 分钟，请及时续费或结束使用", remainingMinutes)
	} else {
		message = fmt.Sprintf("您已使用房间 %d 分钟", remainingMinutes)
	}

	params := map[string]interface{}{
		"type":              "reminder",
		"message":           message,
		"remaining_minutes": remainingMinutes,
		"level":             "normal",
	}

	// 向所有设备发送提醒
	for _, device := range devices {
		err = dc.mqttClient.PublishControlMessage(device.MacAddress, "reminder", params)
		if err != nil {
			log.Printf("向设备 %s 发送提醒失败: %v", device.MacAddress, err)
		}
	}

	log.Printf("房间 %d 时间提醒已发送: %s", roomID, message)
	return nil
}

// getRoomDevice 获取房间指定类型的设备
func (dc *DeviceController) getRoomDevice(roomID int, deviceType string) (*models.DeviceResponse, error) {
	// 获取房间的所有设备
	devices, err := dc.serviceManager.Device.GetDevicesByRoomID(roomID)
	if err != nil {
		return nil, err
	}

	// 查找指定类型的设备
	for _, device := range devices {
		if device.Type == deviceType {
			return device, nil
		}
	}

	return nil, fmt.Errorf("房间 %d 没有找到类型为 %s 的设备", roomID, deviceType)
}

// GetRoomDeviceStatus 获取房间设备状态
func (dc *DeviceController) GetRoomDeviceStatus(roomID int) (map[string]interface{}, error) {
	devices, err := dc.serviceManager.Device.GetDevicesByRoomID(roomID)
	if err != nil {
		return nil, err
	}

	status := make(map[string]interface{})
	for _, device := range devices {
		status[device.Type] = map[string]interface{}{
			"id":             device.ID,
			"mac_address":    device.MacAddress,
			"status":         device.Status,
			"is_online":      device.IsOnline,
			"last_heartbeat": device.LastHeartbeat,
		}
	}

	return status, nil
}
