const { chromium } = require('playwright');

(async () => {
  console.log('🚀 启动订单界面交互测试...');
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-dev-shm-usage']
  });
  
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const text = msg.text();
    if (!text.includes('ElementPlusError') && !text.includes('[vite]')) {
      console.log('🖥️ 浏览器控制台:', text);
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log('❌ 页面错误:', error.message);
  });
  
  try {
    console.log('📄 访问订单列表页面...');
    await page.goto('http://localhost:3000/orders/list');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    console.log('🔍 测试搜索功能...');
    
    // 测试房间筛选
    const roomSelect = page.locator('.el-select').first();
    if (await roomSelect.count() > 0) {
      console.log('点击房间选择框...');
      await roomSelect.click();
      await page.waitForTimeout(1000);
      
      // 选择第一个房间选项
      const firstOption = page.locator('.el-select-dropdown .el-option').first();
      if (await firstOption.count() > 0) {
        await firstOption.click();
        console.log('✅ 房间筛选功能正常');
        await page.waitForTimeout(2000);
      }
    }
    
    console.log('🔘 测试订单操作按钮...');
    
    // 检查订单详情按钮
    const detailButtons = page.locator('.el-table .el-button').filter({ hasText: '详情' });
    const detailCount = await detailButtons.count();
    console.log('详情按钮数量:', detailCount);
    
    if (detailCount > 0) {
      console.log('点击第一个详情按钮...');
      await detailButtons.first().click();
      await page.waitForTimeout(2000);
      
      // 检查是否打开了详情对话框或跳转到详情页面
      const dialog = await page.locator('.el-dialog').count();
      const currentUrl = page.url();
      
      if (dialog > 0) {
        console.log('✅ 详情对话框已打开');
        // 关闭对话框
        const closeBtn = page.locator('.el-dialog .el-dialog__close');
        if (await closeBtn.count() > 0) {
          await closeBtn.click();
        }
      } else if (currentUrl.includes('/orders/')) {
        console.log('✅ 已跳转到订单详情页面:', currentUrl);
        // 返回列表页面
        await page.goBack();
        await page.waitForTimeout(1000);
      } else {
        console.log('⚠️ 详情按钮点击后无明显变化');
      }
    }
    
    console.log('📄 测试分页功能...');
    
    // 检查分页组件
    const pagination = page.locator('.el-pagination');
    if (await pagination.count() > 0) {
      const totalText = await pagination.locator('.el-pagination__total').textContent();
      console.log('分页信息:', totalText?.trim());
      
      // 如果有多页，测试翻页
      const nextBtn = pagination.locator('.btn-next');
      if (await nextBtn.count() > 0 && !(await nextBtn.getAttribute('disabled'))) {
        console.log('测试下一页按钮...');
        await nextBtn.click();
        await page.waitForTimeout(1000);
        console.log('✅ 分页功能正常');
      }
    }
    
    console.log('🔄 测试刷新功能...');
    
    // 刷新页面
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // 检查页面是否正常加载
    const tableRows = await page.locator('.el-table tbody tr').count();
    console.log('刷新后订单行数:', tableRows);
    
    if (tableRows > 0) {
      console.log('✅ 页面刷新功能正常');
    }
    
    console.log('✅ 订单界面交互测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('🔒 关闭浏览器...');
  await browser.close();
  console.log('✅ 订单界面交互测试完成!');
})().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
