package middleware

import (
	"bytes"
	"crypto/md5"
	"fmt"
	"mahjong-system/utils"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// CacheConfig 缓存配置
type CacheConfig struct {
	Duration time.Duration
	KeyFunc  func(*gin.Context) string
	ShouldCache func(*gin.Context) bool
}

// DefaultCacheConfig 默认缓存配置
func DefaultCacheConfig() CacheConfig {
	return CacheConfig{
		Duration: utils.CacheShort,
		KeyFunc:  DefaultKeyFunc,
		ShouldCache: DefaultShouldCache,
	}
}

// DefaultKeyFunc 默认缓存键生成函数
func DefaultKeyFunc(c *gin.Context) string {
	// 生成基于请求路径、查询参数和用户ID的缓存键
	path := c.Request.URL.Path
	query := c.Request.URL.RawQuery
	userID := c.GetHeader("X-User-ID")
	
	// 创建唯一标识
	identifier := fmt.Sprintf("%s?%s&user=%s", path, query, userID)
	
	// 使用MD5生成短键
	hash := md5.Sum([]byte(identifier))
	return fmt.Sprintf("api:%x", hash)
}

// DefaultShouldCache 默认缓存条件
func DefaultShouldCache(c *gin.Context) bool {
	// 只缓存GET请求
	if c.Request.Method != "GET" {
		return false
	}
	
	// 不缓存包含敏感信息的请求
	path := c.Request.URL.Path
	if strings.Contains(path, "/admin/") && strings.Contains(path, "/users/") {
		return false
	}
	
	return true
}

// responseWriter 包装响应写入器以捕获响应内容
type responseWriter struct {
	gin.ResponseWriter
	body   *bytes.Buffer
	status int
}

func (w *responseWriter) Write(data []byte) (int, error) {
	w.body.Write(data)
	return w.ResponseWriter.Write(data)
}

func (w *responseWriter) WriteHeader(status int) {
	w.status = status
	w.ResponseWriter.WriteHeader(status)
}

// Cache 缓存中间件
func Cache(config ...CacheConfig) gin.HandlerFunc {
	cfg := DefaultCacheConfig()
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(c *gin.Context) {
		// 检查是否应该缓存
		if !cfg.ShouldCache(c) {
			c.Next()
			return
		}

		// 生成缓存键
		cacheKey := cfg.KeyFunc(c)

		// 尝试从缓存获取
		if cachedData, found := utils.GlobalCache.Get(cacheKey); found {
			if responseData, ok := cachedData.(map[string]interface{}); ok {
				// 设置响应头
				if headers, exists := responseData["headers"].(map[string]string); exists {
					for key, value := range headers {
						c.Header(key, value)
					}
				}
				
				// 设置状态码
				if status, exists := responseData["status"].(int); exists {
					c.Status(status)
				}
				
				// 返回缓存的响应体
				if body, exists := responseData["body"].(string); exists {
					c.String(http.StatusOK, body)
					c.Abort()
					return
				}
			}
		}

		// 包装响应写入器
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:          bytes.NewBuffer(nil),
			status:        http.StatusOK,
		}
		c.Writer = writer

		// 继续处理请求
		c.Next()

		// 检查响应是否成功且应该缓存
		if writer.status == http.StatusOK && writer.body.Len() > 0 {
			// 收集响应头
			headers := make(map[string]string)
			for key, values := range c.Writer.Header() {
				if len(values) > 0 {
					headers[key] = values[0]
				}
			}

			// 构建缓存数据
			cacheData := map[string]interface{}{
				"status":  writer.status,
				"headers": headers,
				"body":    writer.body.String(),
			}

			// 存储到缓存
			utils.GlobalCache.Set(cacheKey, cacheData, cfg.Duration)
		}
	}
}

// CacheWithTTL 带自定义TTL的缓存中间件
func CacheWithTTL(duration time.Duration) gin.HandlerFunc {
	config := DefaultCacheConfig()
	config.Duration = duration
	return Cache(config)
}

// CacheForUsers 用户相关API缓存
func CacheForUsers() gin.HandlerFunc {
	config := CacheConfig{
		Duration: utils.CacheMedium,
		KeyFunc: func(c *gin.Context) string {
			path := c.Request.URL.Path
			query := c.Request.URL.RawQuery
			userID := c.GetHeader("X-User-ID")
			
			// 用户相关的缓存键
			identifier := fmt.Sprintf("users:%s?%s&user=%s", path, query, userID)
			hash := md5.Sum([]byte(identifier))
			return fmt.Sprintf("api:users:%x", hash)
		},
		ShouldCache: func(c *gin.Context) bool {
			return c.Request.Method == "GET"
		},
	}
	return Cache(config)
}

// CacheForRooms 房间相关API缓存
func CacheForRooms() gin.HandlerFunc {
	config := CacheConfig{
		Duration: utils.CacheLong,
		KeyFunc: func(c *gin.Context) string {
			path := c.Request.URL.Path
			query := c.Request.URL.RawQuery
			
			identifier := fmt.Sprintf("rooms:%s?%s", path, query)
			hash := md5.Sum([]byte(identifier))
			return fmt.Sprintf("api:rooms:%x", hash)
		},
		ShouldCache: func(c *gin.Context) bool {
			return c.Request.Method == "GET"
		},
	}
	return Cache(config)
}

// CacheForStats 统计数据缓存
func CacheForStats() gin.HandlerFunc {
	config := CacheConfig{
		Duration: 10 * time.Minute, // 统计数据缓存10分钟
		KeyFunc: func(c *gin.Context) string {
			path := c.Request.URL.Path
			query := c.Request.URL.RawQuery
			
			// 按小时分组缓存统计数据
			hour := time.Now().Format("2006-01-02-15")
			identifier := fmt.Sprintf("stats:%s?%s&hour=%s", path, query, hour)
			hash := md5.Sum([]byte(identifier))
			return fmt.Sprintf("api:stats:%x", hash)
		},
		ShouldCache: func(c *gin.Context) bool {
			return c.Request.Method == "GET" && strings.Contains(c.Request.URL.Path, "stats")
		},
	}
	return Cache(config)
}

// InvalidateCache 缓存失效中间件
func InvalidateCache(patterns ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先处理请求
		c.Next()

		// 如果请求成功，清除相关缓存
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			for _, pattern := range patterns {
				utils.GlobalCache.DeleteByPattern(pattern)
			}
		}
	}
}

// InvalidateCacheOnWrite 写操作时清除缓存
func InvalidateCacheOnWrite() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		path := c.Request.URL.Path

		// 先处理请求
		c.Next()

		// 如果是写操作且成功，清除相关缓存
		if (method == "POST" || method == "PUT" || method == "DELETE") &&
			c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			
			// 根据路径确定要清除的缓存模式
			if strings.Contains(path, "/users") {
				utils.GlobalCache.DeleteByPattern("api:users:*")
				utils.GlobalCache.DeleteByPattern("api:stats:*")
			} else if strings.Contains(path, "/rooms") {
				utils.GlobalCache.DeleteByPattern("api:rooms:*")
				utils.GlobalCache.DeleteByPattern("api:stats:*")
			} else if strings.Contains(path, "/orders") {
				utils.GlobalCache.DeleteByPattern("api:orders:*")
				utils.GlobalCache.DeleteByPattern("api:stats:*")
			}
		}
	}
}

// CacheStatus 缓存状态中间件（用于调试）
func CacheStatus() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加缓存统计信息到响应头
		stats := utils.GlobalCache.GetStats()
		c.Header("X-Cache-Size", strconv.Itoa(stats["size"].(int)))
		c.Header("X-Cache-Keys", strconv.Itoa(len(stats["keys"].([]string))))
		
		c.Next()
	}
}
