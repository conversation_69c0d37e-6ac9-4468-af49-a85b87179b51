package routes

import (
	"mahjong-system/controllers"
	"mahjong-system/middleware"
	"mahjong-system/mqtt"
	"mahjong-system/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置路由
func SetupRoutes(serviceManager *services.ServiceManager, mqttManager *mqtt.Manager) *gin.Engine {
	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-User-ID")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 创建控制器
	userController := controllers.NewUserController(serviceManager.User)
	roomController := controllers.NewRoomController(serviceManager.Room)
	orderController := controllers.NewOrderController(serviceManager.Order)
	pricingRuleController := controllers.NewPricingRuleController(serviceManager.PricingRule)
	mqttController := controllers.NewMQTTController(mqttManager)
	financeController := controllers.NewFinanceController(serviceManager.Finance)
	platformController := controllers.NewPlatformController(serviceManager.PlatformOrder)
	systemController := controllers.NewSystemController()
	dashboardController := controllers.NewDashboardController(
		serviceManager.Dashboard,
		serviceManager.Order,
		serviceManager.Room,
		serviceManager.User,
		serviceManager.Device,
		serviceManager.PlatformOrder,
	)
	packageController := controllers.NewPackageController(
		serviceManager.Package,
		serviceManager.UserPackage,
	)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "自助麻将室系统后端服务运行正常",
		})
	})

	// API v1 路由组
	v1 := r.Group("/api/v1")
	{
		// 用户相关路由
		users := v1.Group("/users")
		{
			users.POST("/register", userController.Register)
			users.POST("/login", userController.Login)
			users.GET("/profile", userController.GetProfile)
			users.PUT("/profile", userController.UpdateProfile)
			users.POST("/recharge", userController.Recharge)
			users.GET("/balance-records", userController.GetBalanceRecords)
		}

		// 房间相关路由
		rooms := v1.Group("/rooms")
		{
			rooms.GET("", roomController.GetRoomList)
			rooms.GET("/available", roomController.GetAvailableRooms)
			rooms.GET("/:id", roomController.GetRoom)
			rooms.GET("/number/:number", roomController.GetRoomByNumber)
			rooms.GET("/:id/devices", roomController.GetRoomWithDevices)
		}

		// 订单相关路由
		orders := v1.Group("/orders")
		{
			orders.POST("", orderController.CreateOrder)
			orders.GET("", orderController.GetOrderList)
			orders.GET("/my", orderController.GetUserOrders)
			orders.GET("/:id", orderController.GetOrder)
			orders.POST("/:id/pay", orderController.PayOrder)
			orders.POST("/:id/extend", orderController.ExtendOrder)
			orders.POST("/:id/complete", orderController.CompleteOrder)
			orders.POST("/:id/cancel", orderController.CancelOrder)
			orders.GET("/room/:room_id/active", orderController.GetActiveOrderByRoom)
			orders.GET("/calculate-amount", orderController.CalculateOrderAmount)
		}

		// 预约相关路由
		v1.Group("/reservations")
		// TODO: 添加预约相关路由

		// 优惠活动相关路由
		v1.Group("/promotions")
		// TODO: 添加优惠活动相关路由

		// 套餐相关路由
		packages := v1.Group("/packages")
		{
			packages.GET("", packageController.GetActivePackages)
			packages.GET("/:id", packageController.GetPackageDetail)
			packages.POST("/:id/purchase", packageController.PurchasePackage)
			packages.POST("/recharge", packageController.RechargePackage)
		}

		// 用户套餐相关路由
		userPackages := v1.Group("/user/packages")
		{
			userPackages.GET("", packageController.GetUserPackages)
			userPackages.GET("/stats", packageController.GetUserPackageStats)
			userPackages.GET("/:id", packageController.GetUserPackage)
			userPackages.GET("/:id/logs", packageController.GetUsageLogs)
			userPackages.POST("/:id/use", packageController.UsePackage)
		}

		// 外卖平台订单相关路由
		v1.Group("/platform-orders")
		// TODO: 添加外卖平台订单相关路由

		// 系统配置相关路由
		system := v1.Group("/system")
		{
			system.GET("/config", systemController.GetSystemConfig)
			system.GET("/status", systemController.GetSystemStatus)
		}
	}

	// 管理端 API 路由组
	admin := r.Group("/api/v1/admin")
	{
		// 仪表盘
		admin.GET("/dashboard", dashboardController.GetDashboardData)

		// 用户管理
		adminUsers := admin.Group("/users")
		{
			adminUsers.GET("", middleware.CacheForUsers(), userController.GetUserList)
			adminUsers.GET("/stats", middleware.CacheForStats(), userController.GetUserStats)
		}

		// 房间管理
		adminRooms := admin.Group("/rooms")
		{
			adminRooms.GET("", middleware.CacheForRooms(), roomController.GetRoomList)
			adminRooms.GET("/:id", middleware.CacheForRooms(), roomController.GetRoom)
			adminRooms.POST("", middleware.InvalidateCacheOnWrite(), roomController.CreateRoom)
			adminRooms.PUT("/:id", middleware.InvalidateCacheOnWrite(), roomController.UpdateRoom)
			adminRooms.DELETE("/:id", middleware.InvalidateCacheOnWrite(), roomController.DeleteRoom)
			adminRooms.PUT("/:id/status", roomController.UpdateRoomStatus)
			adminRooms.GET("/statistics", roomController.GetRoomStatistics)
		}

		// 计费规则管理
		adminPricingRules := admin.Group("/pricing-rules")
		{
			adminPricingRules.GET("", pricingRuleController.GetPricingRuleList)
			adminPricingRules.GET("/:id", pricingRuleController.GetPricingRule)
			adminPricingRules.POST("", pricingRuleController.CreatePricingRule)
			adminPricingRules.PUT("/:id", pricingRuleController.UpdatePricingRule)
			adminPricingRules.DELETE("/:id", pricingRuleController.DeletePricingRule)
		}

		// 订单管理
		adminOrders := admin.Group("/orders")
		{
			adminOrders.GET("/today-income", orderController.GetTodayIncome)
			adminOrders.GET("/income-report", orderController.GetIncomeReport)
		}

		// 设备管理
		adminDevices := admin.Group("/devices")
		{
			adminDevices.GET("", mqttController.GetDeviceList)
			adminDevices.GET("/grouped", mqttController.GetDeviceListGrouped)
			adminDevices.GET("/:id", mqttController.GetDevice)

			// 批量设备操作
			adminDevices.POST("/batch/power-off", mqttController.BatchPowerOff)
			adminDevices.POST("/batch/lock", mqttController.BatchLockRooms)
			adminDevices.POST("/broadcast/audio", mqttController.BroadcastAudio)
		}

		// 房间设备管理
		adminRooms.GET("/:id/devices", mqttController.GetRoomDevices)
		adminRooms.POST("/:id/devices/:device_type/control", mqttController.ControlRoomDevice)

		// 预约管理
		admin.Group("/reservations")
		// TODO: 添加预约管理路由

		// 优惠活动管理
		admin.Group("/promotions")
		// TODO: 添加优惠活动管理路由

		// 套餐管理
		adminPackages := admin.Group("/packages")
		{
			adminPackages.GET("", packageController.GetPackageList)
			adminPackages.GET("/stats", packageController.GetPackageStats)
			adminPackages.GET("/:id", packageController.GetPackage)
			adminPackages.POST("", packageController.CreatePackage)
			adminPackages.PUT("/:id", packageController.UpdatePackage)
			adminPackages.DELETE("/:id", packageController.DeletePackage)
			adminPackages.PUT("/:id/status", packageController.UpdatePackageStatus)
		}

		// 外卖平台订单管理
		admin.Group("/platform-orders")
		// TODO: 添加外卖平台订单管理路由

		// 系统设置
		adminSystem := admin.Group("/system")
		{
			adminSystem.GET("/config", systemController.GetSystemConfig)
			adminSystem.PUT("/config", systemController.UpdateSystemConfig)
			adminSystem.GET("/status", systemController.GetSystemStatus)
			adminSystem.GET("/logs", systemController.GetSystemLogs)
			adminSystem.POST("/cache/clear", systemController.ClearSystemCache)
			adminSystem.POST("/backup", systemController.BackupData)
			adminSystem.POST("/restore", systemController.RestoreData)
		}
	}

	// MQTT 设备相关路由
	mqttRoutes := r.Group("/api/v1/mqtt")
	{
		// 设备心跳
		mqttRoutes.POST("/heartbeat", mqttController.HandleHeartbeat)

		// 设备状态上报
		mqttRoutes.POST("/status", mqttController.HandleStatusReport)

		// 设备控制响应
		mqttRoutes.POST("/control-response", mqttController.HandleControlResponse)

		// 获取MQTT连接状态
		mqttRoutes.GET("/connection-status", mqttController.GetConnectionStatus)

		// 获取房间设备状态
		mqttRoutes.GET("/room/:room_id/devices", mqttController.GetRoomDeviceStatus)

		// 控制房间门锁
		mqttRoutes.POST("/room/:room_id/lock", mqttController.ControlRoomLock)

		// 控制房间电源
		mqttRoutes.POST("/room/:room_id/power", mqttController.ControlRoomPower)



		// 发送自定义消息
		mqttRoutes.POST("/room/:room_id/message", mqttController.SendCustomMessage)

		// 发送音频消息
		mqttRoutes.POST("/room/:room_id/audio", mqttController.SendAudioMessage)
	}

	// 财务管理路由
	financeRoutes := admin.Group("/finance")
	{
		// 获取财务报表数据
		financeRoutes.GET("/report", financeController.GetFinanceReport)

		// 获取图表数据
		financeRoutes.GET("/charts", financeController.GetFinanceCharts)

		// 获取财务概要数据
		financeRoutes.GET("/summary", financeController.GetFinanceSummary)

		// 支出管理
		financeRoutes.POST("/expenses", financeController.AddExpense)
		financeRoutes.GET("/expenses", financeController.GetExpenseList)

		// 导出财务报表
		financeRoutes.GET("/export", financeController.ExportFinanceReport)
	}

	// 平台订单管理路由
	platformRoutes := admin.Group("/platform")
	{
		// 订单管理
		platformRoutes.GET("/orders", platformController.GetPlatformOrders)
		platformRoutes.GET("/orders/:id", platformController.GetPlatformOrder)
		platformRoutes.DELETE("/orders/:id", platformController.DeletePlatformOrder)
		platformRoutes.POST("/orders/batch-delete", platformController.BatchDeleteOrders)
		platformRoutes.GET("/orders/export", platformController.ExportOrders)

		// 订单核销
		platformRoutes.POST("/verify", platformController.VerifyOrder)
		platformRoutes.POST("/batch-verify", platformController.BatchVerifyOrders)
		platformRoutes.GET("/orders/pending", platformController.GetPendingOrders)

		// 订单退款
		platformRoutes.POST("/orders/:id/refund", platformController.RefundOrder)

		// 核销相关
		platformRoutes.GET("/verification/stats", platformController.GetVerificationStats)
		platformRoutes.GET("/verification/history", platformController.GetVerificationHistory)

		// 数据分析
		platformRoutes.GET("/analytics", platformController.GetAnalytics)
		platformRoutes.GET("/room-analysis", platformController.GetRoomAnalysis)
	}

	// 第三方回调路由
	callback := r.Group("/api/v1/callback")
	{
		// 微信支付回调
		callback.POST("/wechat-pay", func(c *gin.Context) {
			// TODO: 实现微信支付回调处理
			c.JSON(200, gin.H{"message": "微信支付回调处理成功"})
		})

		// 美团回调
		callback.POST("/meituan", func(c *gin.Context) {
			// TODO: 实现美团回调处理
			c.JSON(200, gin.H{"message": "美团回调处理成功"})
		})

		// 饿了么回调
		callback.POST("/eleme", func(c *gin.Context) {
			// TODO: 实现饿了么回调处理
			c.JSON(200, gin.H{"message": "饿了么回调处理成功"})
		})
	}

	return r
}
