const { chromium } = require('playwright');

(async () => {
  console.log('🚀 启动浏览器测试订单界面...');
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-dev-shm-usage']
  });
  
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const text = msg.text();
    if (!text.includes('ElementPlusError') && !text.includes('[vite]')) {
      console.log('🖥️ 浏览器控制台:', text);
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log('❌ 页面错误:', error.message);
  });
  
  try {
    console.log('📄 访问订单列表页面...');
    await page.goto('http://localhost:3000/orders/list');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    console.log('📋 获取页面标题...');
    const title = await page.title();
    console.log('页面标题:', title);
    
    console.log('🔍 检查页面内容...');
    
    // 检查是否有订单表格
    const tableExists = await page.locator('table, .el-table').count();
    console.log('找到表格数量:', tableExists);
    
    // 检查是否有搜索框
    const searchInputs = await page.locator('input[placeholder*="搜索"], input[placeholder*="查询"]').count();
    console.log('找到搜索框数量:', searchInputs);
    
    // 检查是否有按钮
    const buttons = await page.locator('button').count();
    console.log('找到按钮数量:', buttons);
    
    // 检查是否有分页
    const pagination = await page.locator('.el-pagination').count();
    console.log('找到分页组件数量:', pagination);
    
    // 检查是否有加载状态
    const loading = await page.locator('.el-loading-mask').count();
    console.log('加载状态数量:', loading);
    
    // 检查页面内容
    const pageContent = await page.textContent('body');
    if (pageContent.includes('订单')) {
      console.log('✅ 页面包含订单相关内容');
    } else {
      console.log('❌ 页面不包含订单相关内容');
    }
    
    // 检查是否有错误信息
    const errorMessages = await page.locator('.el-message--error, .error').count();
    if (errorMessages > 0) {
      console.log('⚠️ 发现错误消息:', errorMessages);
    }
    
    console.log('📍 当前URL:', page.url());
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('🔒 关闭浏览器...');
  await browser.close();
  console.log('✅ 订单界面测试完成!');
})().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
