package repositories

import (
	"database/sql"
	"fmt"
	"mahjong-system/models"
	"time"
)

// OrderRepository 订单仓储接口
type OrderRepository interface {
	Repository
	Create(order *models.Order) error
	GetByID(id int) (*models.Order, error)
	Update(order *models.Order) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.Order, int64, error)
	GetByUserID(userID int, pagination *PaginationParams) ([]*models.Order, int64, error)
	GetByRoomID(roomID int, pagination *PaginationParams) ([]*models.Order, int64, error)
	GetActiveOrderByRoom(roomID int) (*models.Order, error)
	UpdateStatus(id int, status string) error
	GetOrdersInTimeRange(startTime, endTime time.Time) ([]*models.Order, error)
	GetTodayIncome() (float64, error)
	GetIncomeByDateRange(startDate, endDate time.Time) ([]models.IncomeData, error)
	GetTotalStats() (int64, float64, error)
	GetDailyStats(date string) (int64, float64, error)
	GetOrdersByDateRange(startDate, endDate time.Time) ([]*models.Order, error)
	GetOrdersByRoomAndDateRange(roomID int, startDate, endDate time.Time) ([]*models.Order, error)
	GetRevenueByDateRange(startDate, endDate time.Time) (float64, error)
	GetOrderCountByDateRange(startDate, endDate time.Time) (int64, error)
	GetActiveOrderCount() (int, error)
	GetOrderCountByRoom(roomID int) (int, error)
}

// orderRepository 订单仓储实现
type orderRepository struct {
	BaseRepository
}

// Create 创建订单
func (r *orderRepository) Create(order *models.Order) error {
	query := `
		INSERT INTO orders (user_id, room_id, start_time, end_time, total_amount, paid_amount, status, created_at, updated_at)
		VALUES (:user_id, :room_id, :start_time, :end_time, :total_amount, :paid_amount, :status, :created_at, :updated_at)
	`
	
	now := time.Now()
	order.CreatedAt = now
	order.UpdatedAt = now
	
	result, err := r.DB.NamedExec(query, order)
	if err != nil {
		return fmt.Errorf("创建订单失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取订单ID失败: %v", err)
	}
	
	order.ID = int(id)
	return nil
}

// GetByID 根据ID获取订单
func (r *orderRepository) GetByID(id int) (*models.Order, error) {
	var order models.Order
	query := "SELECT * FROM orders WHERE id = ?"
	
	err := r.DB.Get(&order, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取订单失败: %v", err)
	}
	
	return &order, nil
}

// Update 更新订单
func (r *orderRepository) Update(order *models.Order) error {
	query := `
		UPDATE orders 
		SET user_id = :user_id, room_id = :room_id, start_time = :start_time, end_time = :end_time,
		    total_amount = :total_amount, paid_amount = :paid_amount, status = :status, updated_at = :updated_at
		WHERE id = :id
	`
	
	order.UpdatedAt = time.Now()
	
	result, err := r.DB.NamedExec(query, order)
	if err != nil {
		return fmt.Errorf("更新订单失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在")
	}
	
	return nil
}

// Delete 删除订单
func (r *orderRepository) Delete(id int) error {
	query := "DELETE FROM orders WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除订单失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在")
	}
	
	return nil
}

// List 获取订单列表
func (r *orderRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.Order, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()
	
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM orders " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取订单总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM orders " + whereClause + " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, pagination.PageSize, pagination.Offset)
	
	var orders []*models.Order
	err = r.DB.Select(&orders, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取订单列表失败: %v", err)
	}
	
	return orders, total, nil
}

// GetByUserID 根据用户ID获取订单列表
func (r *orderRepository) GetByUserID(userID int, pagination *PaginationParams) ([]*models.Order, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM orders WHERE user_id = ?"
	total, err := r.GetCount(countQuery, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户订单总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
	
	var orders []*models.Order
	err = r.DB.Select(&orders, query, userID, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户订单列表失败: %v", err)
	}
	
	return orders, total, nil
}

// GetByRoomID 根据房间ID获取订单列表
func (r *orderRepository) GetByRoomID(roomID int, pagination *PaginationParams) ([]*models.Order, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM orders WHERE room_id = ?"
	total, err := r.GetCount(countQuery, roomID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间订单总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM orders WHERE room_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
	
	var orders []*models.Order
	err = r.DB.Select(&orders, query, roomID, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间订单列表失败: %v", err)
	}
	
	return orders, total, nil
}

// GetActiveOrderByRoom 获取房间的活跃订单
func (r *orderRepository) GetActiveOrderByRoom(roomID int) (*models.Order, error) {
	var order models.Order
	query := "SELECT * FROM orders WHERE room_id = ? AND status IN (?, ?, ?) ORDER BY created_at DESC LIMIT 1"

	err := r.DB.Get(&order, query, roomID, models.OrderStatusPending, models.OrderStatusPaid, models.OrderStatusInUse)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取房间活跃订单失败: %v", err)
	}

	return &order, nil
}

// UpdateStatus 更新订单状态
func (r *orderRepository) UpdateStatus(id int, status string) error {
	query := "UPDATE orders SET status = ?, updated_at = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, status, time.Now(), id)
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在")
	}
	
	return nil
}

// GetOrdersInTimeRange 获取时间范围内的订单
func (r *orderRepository) GetOrdersInTimeRange(startTime, endTime time.Time) ([]*models.Order, error) {
	query := "SELECT * FROM orders WHERE created_at BETWEEN ? AND ? ORDER BY created_at DESC"
	
	var orders []*models.Order
	err := r.DB.Select(&orders, query, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取时间范围内订单失败: %v", err)
	}
	
	return orders, nil
}

// GetTodayIncome 获取今日收入
func (r *orderRepository) GetTodayIncome() (float64, error) {
	today := time.Now().Format("2006-01-02")
	query := `
		SELECT COALESCE(SUM(paid_amount), 0) as income 
		FROM orders 
		WHERE DATE(created_at) = ? AND status IN (?, ?)
	`
	
	var income float64
	err := r.DB.Get(&income, query, today, models.OrderStatusPaid, models.OrderStatusCompleted)
	if err != nil {
		return 0, fmt.Errorf("获取今日收入失败: %v", err)
	}
	
	return income, nil
}

// GetIncomeByDateRange 获取日期范围内的收入统计
func (r *orderRepository) GetIncomeByDateRange(startDate, endDate time.Time) ([]models.IncomeData, error) {
	query := `
		SELECT DATE(created_at) as date, COALESCE(SUM(paid_amount), 0) as income
		FROM orders 
		WHERE DATE(created_at) BETWEEN ? AND ? AND status IN (?, ?)
		GROUP BY DATE(created_at)
		ORDER BY date ASC
	`
	
	var incomeData []models.IncomeData
	err := r.DB.Select(&incomeData, query, 
		startDate.Format("2006-01-02"), 
		endDate.Format("2006-01-02"),
		models.OrderStatusPaid, 
		models.OrderStatusCompleted)
	if err != nil {
		return nil, fmt.Errorf("获取收入统计失败: %v", err)
	}
	
	return incomeData, nil
}

// GetTotalStats 获取总统计数据
func (r *orderRepository) GetTotalStats() (int64, float64, error) {
	query := `
		SELECT
			COUNT(*) as total_orders,
			COALESCE(SUM(paid_amount), 0) as total_amount
		FROM orders
		WHERE status IN ('paid', 'completed')
	`

	var totalOrders int64
	var totalAmount float64

	err := r.DB.QueryRow(query).Scan(&totalOrders, &totalAmount)
	if err != nil {
		return 0, 0, fmt.Errorf("获取总统计数据失败: %v", err)
	}

	return totalOrders, totalAmount, nil
}

// GetDailyStats 获取指定日期的统计数据
func (r *orderRepository) GetDailyStats(date string) (int64, float64, error) {
	query := `
		SELECT
			COUNT(*) as daily_orders,
			COALESCE(SUM(paid_amount), 0) as daily_amount
		FROM orders
		WHERE status IN ('paid', 'completed')
		AND DATE(created_at) = ?
	`

	var dailyOrders int64
	var dailyAmount float64

	err := r.DB.QueryRow(query, date).Scan(&dailyOrders, &dailyAmount)
	if err != nil {
		return 0, 0, fmt.Errorf("获取日统计数据失败: %v", err)
	}

	return dailyOrders, dailyAmount, nil
}

// GetOrdersByDateRange 根据日期范围获取订单
func (r *orderRepository) GetOrdersByDateRange(startDate, endDate time.Time) ([]*models.Order, error) {
	query := `
		SELECT id, user_id, room_id, start_time, end_time,
		       total_amount, paid_amount, status, created_at, updated_at
		FROM orders
		WHERE created_at >= ? AND created_at <= ?
		ORDER BY created_at ASC
	`

	rows, err := r.DB.Query(query, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	defer rows.Close()

	var orders []*models.Order
	for rows.Next() {
		order := &models.Order{}
		err := rows.Scan(
			&order.ID, &order.UserID, &order.RoomID,
			&order.StartTime, &order.EndTime,
			&order.TotalAmount, &order.PaidAmount, &order.Status,
			&order.CreatedAt, &order.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描订单数据失败: %v", err)
		}
		orders = append(orders, order)
	}

	return orders, nil
}

// GetOrdersByRoomAndDateRange 根据房间和日期范围获取订单
func (r *orderRepository) GetOrdersByRoomAndDateRange(roomID int, startDate, endDate time.Time) ([]*models.Order, error) {
	query := `
		SELECT id, user_id, room_id, start_time, end_time,
		       total_amount, paid_amount, status, created_at, updated_at
		FROM orders
		WHERE room_id = ? AND created_at >= ? AND created_at <= ?
		ORDER BY created_at ASC
	`

	rows, err := r.DB.Query(query, roomID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("查询房间订单失败: %v", err)
	}
	defer rows.Close()

	var orders []*models.Order
	for rows.Next() {
		order := &models.Order{}
		err := rows.Scan(
			&order.ID, &order.UserID, &order.RoomID,
			&order.StartTime, &order.EndTime,
			&order.TotalAmount, &order.PaidAmount, &order.Status,
			&order.CreatedAt, &order.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描订单数据失败: %v", err)
		}
		orders = append(orders, order)
	}

	return orders, nil
}

// GetRevenueByDateRange 根据日期范围获取收入总额
func (r *orderRepository) GetRevenueByDateRange(startDate, endDate time.Time) (float64, error) {
	query := `
		SELECT COALESCE(SUM(total_amount), 0)
		FROM orders
		WHERE created_at >= ? AND created_at <= ?
		AND status IN ('paid', 'completed')
	`

	var revenue float64
	err := r.DB.QueryRow(query, startDate, endDate).Scan(&revenue)
	if err != nil {
		return 0, fmt.Errorf("获取收入总额失败: %v", err)
	}

	return revenue, nil
}

// GetActiveOrderCount 获取活跃订单数量（正在进行中的订单）
func (r *orderRepository) GetActiveOrderCount() (int, error) {
	query := `
		SELECT COUNT(*) as count
		FROM orders
		WHERE status IN (?, ?, ?)
	`

	var count int
	err := r.DB.Get(&count, query, models.OrderStatusPending, models.OrderStatusPaid, models.OrderStatusInUse)
	if err != nil {
		return 0, fmt.Errorf("获取活跃订单数量失败: %v", err)
	}

	return count, nil
}

// GetOrderCountByDateRange 根据日期范围获取订单数量
func (r *orderRepository) GetOrderCountByDateRange(startDate, endDate time.Time) (int64, error) {
	query := `
		SELECT COUNT(*)
		FROM orders
		WHERE created_at >= ? AND created_at <= ?
	`

	var count int64
	err := r.DB.QueryRow(query, startDate, endDate).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("获取订单数量失败: %v", err)
	}

	return count, nil
}

// GetOrderCountByRoom 获取房间的所有订单数量（包括历史订单）
func (r *orderRepository) GetOrderCountByRoom(roomID int) (int, error) {
	var count int
	query := "SELECT COUNT(*) FROM orders WHERE room_id = ?"
	err := r.DB.Get(&count, query, roomID)
	if err != nil {
		return 0, fmt.Errorf("获取房间订单数量失败: %v", err)
	}
	return count, nil
}
