package repositories

import (
	"database/sql"
	"fmt"
	"mahjong-system/models"
	"time"
)

// ReservationRepository 预约仓储接口
type ReservationRepository interface {
	Repository
	Create(reservation *models.Reservation) error
	GetByID(id int) (*models.Reservation, error)
	Update(reservation *models.Reservation) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.Reservation, int64, error)
	GetByUserID(userID int, pagination *PaginationParams) ([]*models.Reservation, int64, error)
	GetByRoomID(roomID int, pagination *PaginationParams) ([]*models.Reservation, int64, error)
	CheckConflict(roomID int, startTime, endTime time.Time, excludeID ...int) (bool, error)
	GetUpcomingReservations(limit int) ([]*models.Reservation, error)
	UpdateStatus(id int, status string) error
	GetReservationsInTimeRange(startTime, endTime time.Time) ([]*models.Reservation, error)
}

// reservationRepository 预约仓储实现
type reservationRepository struct {
	BaseRepository
}

// Create 创建预约
func (r *reservationRepository) Create(reservation *models.Reservation) error {
	query := `
		INSERT INTO reservations (user_id, room_id, start_time, end_time, status, created_at)
		VALUES (:user_id, :room_id, :start_time, :end_time, :status, :created_at)
	`
	
	reservation.CreatedAt = time.Now()
	
	result, err := r.DB.NamedExec(query, reservation)
	if err != nil {
		return fmt.Errorf("创建预约失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取预约ID失败: %v", err)
	}
	
	reservation.ID = int(id)
	return nil
}

// GetByID 根据ID获取预约
func (r *reservationRepository) GetByID(id int) (*models.Reservation, error) {
	var reservation models.Reservation
	query := "SELECT * FROM reservations WHERE id = ?"
	
	err := r.DB.Get(&reservation, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取预约失败: %v", err)
	}
	
	return &reservation, nil
}

// Update 更新预约
func (r *reservationRepository) Update(reservation *models.Reservation) error {
	query := `
		UPDATE reservations 
		SET user_id = :user_id, room_id = :room_id, start_time = :start_time, 
		    end_time = :end_time, status = :status
		WHERE id = :id
	`
	
	result, err := r.DB.NamedExec(query, reservation)
	if err != nil {
		return fmt.Errorf("更新预约失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("预约不存在")
	}
	
	return nil
}

// Delete 删除预约
func (r *reservationRepository) Delete(id int) error {
	query := "DELETE FROM reservations WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除预约失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("预约不存在")
	}
	
	return nil
}

// List 获取预约列表
func (r *reservationRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.Reservation, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()
	
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM reservations " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取预约总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM reservations " + whereClause + " ORDER BY start_time DESC LIMIT ? OFFSET ?"
	args = append(args, pagination.PageSize, pagination.Offset)
	
	var reservations []*models.Reservation
	err = r.DB.Select(&reservations, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取预约列表失败: %v", err)
	}
	
	return reservations, total, nil
}

// GetByUserID 根据用户ID获取预约列表
func (r *reservationRepository) GetByUserID(userID int, pagination *PaginationParams) ([]*models.Reservation, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM reservations WHERE user_id = ?"
	total, err := r.GetCount(countQuery, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户预约总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM reservations WHERE user_id = ? ORDER BY start_time DESC LIMIT ? OFFSET ?"
	
	var reservations []*models.Reservation
	err = r.DB.Select(&reservations, query, userID, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户预约列表失败: %v", err)
	}
	
	return reservations, total, nil
}

// GetByRoomID 根据房间ID获取预约列表
func (r *reservationRepository) GetByRoomID(roomID int, pagination *PaginationParams) ([]*models.Reservation, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM reservations WHERE room_id = ?"
	total, err := r.GetCount(countQuery, roomID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间预约总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM reservations WHERE room_id = ? ORDER BY start_time DESC LIMIT ? OFFSET ?"
	
	var reservations []*models.Reservation
	err = r.DB.Select(&reservations, query, roomID, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间预约列表失败: %v", err)
	}
	
	return reservations, total, nil
}

// CheckConflict 检查预约时间冲突
func (r *reservationRepository) CheckConflict(roomID int, startTime, endTime time.Time, excludeID ...int) (bool, error) {
	query := `
		SELECT COUNT(*) as count FROM reservations 
		WHERE room_id = ? AND status = ? 
		AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))
	`
	args := []interface{}{roomID, models.ReservationStatusConfirmed, startTime, startTime, endTime, endTime}
	
	// 如果有排除的ID，添加到查询条件中
	if len(excludeID) > 0 {
		query += " AND id != ?"
		args = append(args, excludeID[0])
	}
	
	var count int
	err := r.DB.Get(&count, query, args...)
	if err != nil {
		return false, fmt.Errorf("检查预约冲突失败: %v", err)
	}
	
	return count > 0, nil
}

// GetUpcomingReservations 获取即将到来的预约
func (r *reservationRepository) GetUpcomingReservations(limit int) ([]*models.Reservation, error) {
	query := `
		SELECT * FROM reservations 
		WHERE status = ? AND start_time > ? 
		ORDER BY start_time ASC 
		LIMIT ?
	`
	
	var reservations []*models.Reservation
	err := r.DB.Select(&reservations, query, models.ReservationStatusConfirmed, time.Now(), limit)
	if err != nil {
		return nil, fmt.Errorf("获取即将到来的预约失败: %v", err)
	}
	
	return reservations, nil
}

// UpdateStatus 更新预约状态
func (r *reservationRepository) UpdateStatus(id int, status string) error {
	query := "UPDATE reservations SET status = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, status, id)
	if err != nil {
		return fmt.Errorf("更新预约状态失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("预约不存在")
	}
	
	return nil
}

// GetReservationsInTimeRange 获取时间范围内的预约
func (r *reservationRepository) GetReservationsInTimeRange(startTime, endTime time.Time) ([]*models.Reservation, error) {
	query := `
		SELECT * FROM reservations 
		WHERE start_time BETWEEN ? AND ? 
		ORDER BY start_time ASC
	`
	
	var reservations []*models.Reservation
	err := r.DB.Select(&reservations, query, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取时间范围内预约失败: %v", err)
	}
	
	return reservations, nil
}

// PromotionRepository 优惠活动仓储接口
type PromotionRepository interface {
	Repository
	Create(promotion *models.Promotion) error
	GetByID(id int) (*models.Promotion, error)
	Update(promotion *models.Promotion) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.Promotion, int64, error)
	GetActivePromotions() ([]*models.Promotion, error)
	GetValidPromotions() ([]*models.Promotion, error)
}

// promotionRepository 优惠活动仓储实现
type promotionRepository struct {
	BaseRepository
}

// Create 创建优惠活动
func (r *promotionRepository) Create(promotion *models.Promotion) error {
	query := `
		INSERT INTO promotions (name, description, discount, start_time, end_time, is_active, created_at, updated_at)
		VALUES (:name, :description, :discount, :start_time, :end_time, :is_active, :created_at, :updated_at)
	`
	
	now := time.Now()
	promotion.CreatedAt = now
	promotion.UpdatedAt = now
	
	result, err := r.DB.NamedExec(query, promotion)
	if err != nil {
		return fmt.Errorf("创建优惠活动失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取优惠活动ID失败: %v", err)
	}
	
	promotion.ID = int(id)
	return nil
}

// GetByID 根据ID获取优惠活动
func (r *promotionRepository) GetByID(id int) (*models.Promotion, error) {
	var promotion models.Promotion
	query := "SELECT * FROM promotions WHERE id = ?"
	
	err := r.DB.Get(&promotion, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取优惠活动失败: %v", err)
	}
	
	return &promotion, nil
}

// Update 更新优惠活动
func (r *promotionRepository) Update(promotion *models.Promotion) error {
	query := `
		UPDATE promotions 
		SET name = :name, description = :description, discount = :discount,
		    start_time = :start_time, end_time = :end_time, is_active = :is_active, updated_at = :updated_at
		WHERE id = :id
	`
	
	promotion.UpdatedAt = time.Now()
	
	result, err := r.DB.NamedExec(query, promotion)
	if err != nil {
		return fmt.Errorf("更新优惠活动失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("优惠活动不存在")
	}
	
	return nil
}

// Delete 删除优惠活动
func (r *promotionRepository) Delete(id int) error {
	query := "DELETE FROM promotions WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除优惠活动失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("优惠活动不存在")
	}
	
	return nil
}

// List 获取优惠活动列表
func (r *promotionRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.Promotion, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()
	
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM promotions " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取优惠活动总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM promotions " + whereClause + " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, pagination.PageSize, pagination.Offset)
	
	var promotions []*models.Promotion
	err = r.DB.Select(&promotions, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取优惠活动列表失败: %v", err)
	}
	
	return promotions, total, nil
}

// GetActivePromotions 获取激活的优惠活动
func (r *promotionRepository) GetActivePromotions() ([]*models.Promotion, error) {
	query := "SELECT * FROM promotions WHERE is_active = 1 ORDER BY created_at DESC"
	
	var promotions []*models.Promotion
	err := r.DB.Select(&promotions, query)
	if err != nil {
		return nil, fmt.Errorf("获取激活优惠活动失败: %v", err)
	}
	
	return promotions, nil
}

// GetValidPromotions 获取当前有效的优惠活动
func (r *promotionRepository) GetValidPromotions() ([]*models.Promotion, error) {
	now := time.Now()
	query := `
		SELECT * FROM promotions 
		WHERE is_active = 1 AND start_time <= ? AND end_time >= ? 
		ORDER BY created_at DESC
	`
	
	var promotions []*models.Promotion
	err := r.DB.Select(&promotions, query, now, now)
	if err != nil {
		return nil, fmt.Errorf("获取有效优惠活动失败: %v", err)
	}
	
	return promotions, nil
}
