package repositories

import (
	"database/sql"
	"fmt"
	"mahjong-system/models"
	"time"
)

// DeviceRepository 设备仓储接口
type DeviceRepository interface {
	Repository
	Create(device *models.Device) error
	GetByID(id int) (*models.Device, error)
	GetByMacAddress(macAddress string) (*models.Device, error)
	Update(device *models.Device) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.Device, int64, error)
	GetByRoomID(roomID int) ([]*models.Device, error)
	GetByType(deviceType string) ([]*models.Device, error)
	UpdateStatus(id int, status string) error
	UpdateHeartbeat(id int, heartbeatTime time.Time) error
	GetOnlineDevices() ([]*models.Device, error)
	GetOfflineDevices() ([]*models.Device, error)
	GetDeviceStatistics() (map[string]int, error)
}

// deviceRepository 设备仓储实现
type deviceRepository struct {
	BaseRepository
}

// Create 创建设备
func (r *deviceRepository) Create(device *models.Device) error {
	query := `
		INSERT INTO devices (type, room_id, mac_address, status, last_heartbeat, installed_at)
		VALUES (:type, :room_id, :mac_address, :status, :last_heartbeat, :installed_at)
	`
	
	device.InstalledAt = time.Now()
	
	result, err := r.DB.NamedExec(query, device)
	if err != nil {
		return fmt.Errorf("创建设备失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取设备ID失败: %v", err)
	}
	
	device.ID = int(id)
	return nil
}

// GetByID 根据ID获取设备
func (r *deviceRepository) GetByID(id int) (*models.Device, error) {
	var device models.Device
	query := "SELECT * FROM devices WHERE id = ?"
	
	err := r.DB.Get(&device, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取设备失败: %v", err)
	}
	
	return &device, nil
}

// GetByMacAddress 根据MAC地址获取设备
func (r *deviceRepository) GetByMacAddress(macAddress string) (*models.Device, error) {
	var device models.Device
	query := "SELECT * FROM devices WHERE mac_address = ?"
	
	err := r.DB.Get(&device, query, macAddress)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取设备失败: %v", err)
	}
	
	return &device, nil
}

// Update 更新设备
func (r *deviceRepository) Update(device *models.Device) error {
	query := `
		UPDATE devices 
		SET type = :type, room_id = :room_id, mac_address = :mac_address, 
		    status = :status, last_heartbeat = :last_heartbeat
		WHERE id = :id
	`
	
	result, err := r.DB.NamedExec(query, device)
	if err != nil {
		return fmt.Errorf("更新设备失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}
	
	return nil
}

// Delete 删除设备
func (r *deviceRepository) Delete(id int) error {
	query := "DELETE FROM devices WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除设备失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}
	
	return nil
}

// List 获取设备列表
func (r *deviceRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.Device, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()
	
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM devices " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取设备总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM devices " + whereClause + " ORDER BY installed_at DESC LIMIT ? OFFSET ?"
	args = append(args, pagination.PageSize, pagination.Offset)
	
	var devices []*models.Device
	err = r.DB.Select(&devices, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取设备列表失败: %v", err)
	}
	
	return devices, total, nil
}

// GetByRoomID 根据房间ID获取设备列表
func (r *deviceRepository) GetByRoomID(roomID int) ([]*models.Device, error) {
	query := "SELECT * FROM devices WHERE room_id = ? ORDER BY type ASC"
	
	var devices []*models.Device
	err := r.DB.Select(&devices, query, roomID)
	if err != nil {
		return nil, fmt.Errorf("获取房间设备失败: %v", err)
	}
	
	return devices, nil
}

// GetByType 根据设备类型获取设备列表
func (r *deviceRepository) GetByType(deviceType string) ([]*models.Device, error) {
	query := "SELECT * FROM devices WHERE type = ? ORDER BY installed_at DESC"
	
	var devices []*models.Device
	err := r.DB.Select(&devices, query, deviceType)
	if err != nil {
		return nil, fmt.Errorf("获取设备类型列表失败: %v", err)
	}
	
	return devices, nil
}

// UpdateStatus 更新设备状态
func (r *deviceRepository) UpdateStatus(id int, status string) error {
	query := "UPDATE devices SET status = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, status, id)
	if err != nil {
		return fmt.Errorf("更新设备状态失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}
	
	return nil
}

// UpdateHeartbeat 更新设备心跳时间
func (r *deviceRepository) UpdateHeartbeat(id int, heartbeatTime time.Time) error {
	query := "UPDATE devices SET last_heartbeat = ?, status = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, heartbeatTime, models.DeviceStatusOnline, id)
	if err != nil {
		return fmt.Errorf("更新设备心跳失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("设备不存在")
	}
	
	return nil
}

// GetOnlineDevices 获取在线设备列表
func (r *deviceRepository) GetOnlineDevices() ([]*models.Device, error) {
	// 3分钟内有心跳的设备认为是在线的
	threeMinutesAgo := time.Now().Add(-3 * time.Minute)
	query := "SELECT * FROM devices WHERE last_heartbeat > ? ORDER BY last_heartbeat DESC"
	
	var devices []*models.Device
	err := r.DB.Select(&devices, query, threeMinutesAgo)
	if err != nil {
		return nil, fmt.Errorf("获取在线设备失败: %v", err)
	}
	
	return devices, nil
}

// GetOfflineDevices 获取离线设备列表
func (r *deviceRepository) GetOfflineDevices() ([]*models.Device, error) {
	// 3分钟内没有心跳的设备认为是离线的
	threeMinutesAgo := time.Now().Add(-3 * time.Minute)
	query := "SELECT * FROM devices WHERE last_heartbeat IS NULL OR last_heartbeat <= ? ORDER BY last_heartbeat DESC"
	
	var devices []*models.Device
	err := r.DB.Select(&devices, query, threeMinutesAgo)
	if err != nil {
		return nil, fmt.Errorf("获取离线设备失败: %v", err)
	}
	
	return devices, nil
}

// GetDeviceStatistics 获取设备统计信息
func (r *deviceRepository) GetDeviceStatistics() (map[string]int, error) {
	// 获取总设备数
	totalQuery := "SELECT COUNT(*) as total FROM devices"
	var total int
	err := r.DB.Get(&total, totalQuery)
	if err != nil {
		return nil, fmt.Errorf("获取设备总数失败: %v", err)
	}
	
	// 获取在线设备数
	threeMinutesAgo := time.Now().Add(-3 * time.Minute)
	onlineQuery := "SELECT COUNT(*) as online FROM devices WHERE last_heartbeat > ?"
	var online int
	err = r.DB.Get(&online, onlineQuery, threeMinutesAgo)
	if err != nil {
		return nil, fmt.Errorf("获取在线设备数失败: %v", err)
	}
	
	// 获取各类型设备数量
	typeQuery := "SELECT type, COUNT(*) as count FROM devices GROUP BY type"
	rows, err := r.DB.Query(typeQuery)
	if err != nil {
		return nil, fmt.Errorf("获取设备类型统计失败: %v", err)
	}
	defer rows.Close()
	
	statistics := map[string]int{
		"total":   total,
		"online":  online,
		"offline": total - online,
	}
	
	for rows.Next() {
		var deviceType string
		var count int
		if err := rows.Scan(&deviceType, &count); err != nil {
			return nil, fmt.Errorf("扫描设备类型统计失败: %v", err)
		}
		statistics[deviceType] = count
	}
	
	return statistics, nil
}
