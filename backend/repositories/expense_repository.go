package repositories

import (
	"fmt"
	"time"

	"mahjong-system/models"
)

// ExpenseRepository 支出记录仓库接口
type ExpenseRepository interface {
	Repository
	Create(expense *models.Expense) error
	GetByID(id int) (*models.Expense, error)
	Update(expense *models.Expense) error
	Delete(id int) error
	GetList(page, pageSize int, filters map[string]interface{}) ([]*models.Expense, int64, error)
	GetByDateRange(startDate, endDate time.Time) ([]*models.Expense, error)
	GetTotalByType(expenseType string, startDate, endDate time.Time) (float64, error)
	GetTotalByDateRange(startDate, endDate time.Time) (float64, error)
}

// expenseRepository 支出记录仓库实现
type expenseRepository struct {
	BaseRepository
}

// Create 创建支出记录
func (r *expenseRepository) Create(expense *models.Expense) error {
	query := `
		INSERT INTO expenses (type, amount, date, description, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	now := time.Now()
	expense.CreatedAt = now
	expense.UpdatedAt = now

	result, err := r.DB.Exec(query, expense.Type, expense.Amount, expense.Date,
		expense.Description, expense.CreatedAt, expense.UpdatedAt)
	if err != nil {
		return fmt.Errorf("创建支出记录失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取插入ID失败: %v", err)
	}

	expense.ID = int(id)
	return nil
}

// GetByID 根据ID获取支出记录
func (r *expenseRepository) GetByID(id int) (*models.Expense, error) {
	query := `
		SELECT id, type, amount, date, description, created_at, updated_at
		FROM expenses WHERE id = ?
	`

	expense := &models.Expense{}
	err := r.DB.QueryRow(query, id).Scan(
		&expense.ID, &expense.Type, &expense.Amount, &expense.Date,
		&expense.Description, &expense.CreatedAt, &expense.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("获取支出记录失败: %v", err)
	}

	return expense, nil
}

// Update 更新支出记录
func (r *expenseRepository) Update(expense *models.Expense) error {
	query := `
		UPDATE expenses
		SET type = ?, amount = ?, date = ?, description = ?, updated_at = ?
		WHERE id = ?
	`

	expense.UpdatedAt = time.Now()
	_, err := r.DB.Exec(query, expense.Type, expense.Amount, expense.Date,
		expense.Description, expense.UpdatedAt, expense.ID)
	if err != nil {
		return fmt.Errorf("更新支出记录失败: %v", err)
	}

	return nil
}

// Delete 删除支出记录
func (r *expenseRepository) Delete(id int) error {
	query := `DELETE FROM expenses WHERE id = ?`
	_, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除支出记录失败: %v", err)
	}
	return nil
}

// GetList 获取支出记录列表
func (r *expenseRepository) GetList(page, pageSize int, filters map[string]interface{}) ([]*models.Expense, int64, error) {
	// 构建查询条件
	whereClause := "1=1"
	args := []interface{}{}

	if expenseType, ok := filters["type"]; ok {
		whereClause += " AND type = ?"
		args = append(args, expenseType)
	}

	if startDate, ok := filters["start_date"]; ok {
		whereClause += " AND date >= ?"
		args = append(args, startDate)
	}

	if endDate, ok := filters["end_date"]; ok {
		whereClause += " AND date <= ?"
		args = append(args, endDate)
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM expenses WHERE %s", whereClause)
	var total int64
	err := r.DB.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("获取支出记录总数失败: %v", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`
		SELECT id, type, amount, date, description, created_at, updated_at
		FROM expenses WHERE %s
		ORDER BY date DESC
		LIMIT ? OFFSET ?
	`, whereClause)

	args = append(args, pageSize, offset)
	rows, err := r.DB.Query(dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取支出记录列表失败: %v", err)
	}
	defer rows.Close()

	var expenses []*models.Expense
	for rows.Next() {
		expense := &models.Expense{}
		err := rows.Scan(
			&expense.ID, &expense.Type, &expense.Amount, &expense.Date,
			&expense.Description, &expense.CreatedAt, &expense.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描支出记录失败: %v", err)
		}
		expenses = append(expenses, expense)
	}

	return expenses, total, nil
}

// GetByDateRange 根据日期范围获取支出记录
func (r *expenseRepository) GetByDateRange(startDate, endDate time.Time) ([]*models.Expense, error) {
	query := `
		SELECT id, type, amount, date, description, created_at, updated_at
		FROM expenses
		WHERE date >= ? AND date <= ?
		ORDER BY date ASC
	`

	rows, err := r.DB.Query(query, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("获取支出记录失败: %v", err)
	}
	defer rows.Close()

	var expenses []*models.Expense
	for rows.Next() {
		expense := &models.Expense{}
		err := rows.Scan(
			&expense.ID, &expense.Type, &expense.Amount, &expense.Date,
			&expense.Description, &expense.CreatedAt, &expense.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描支出记录失败: %v", err)
		}
		expenses = append(expenses, expense)
	}

	return expenses, nil
}

// GetTotalByType 根据类型和日期范围获取支出总额
func (r *expenseRepository) GetTotalByType(expenseType string, startDate, endDate time.Time) (float64, error) {
	query := `
		SELECT COALESCE(SUM(amount), 0)
		FROM expenses
		WHERE type = ? AND date >= ? AND date <= ?
	`

	var total float64
	err := r.DB.QueryRow(query, expenseType, startDate, endDate).Scan(&total)
	if err != nil {
		return 0, fmt.Errorf("获取支出总额失败: %v", err)
	}

	return total, nil
}

// GetTotalByDateRange 根据日期范围获取支出总额
func (r *expenseRepository) GetTotalByDateRange(startDate, endDate time.Time) (float64, error) {
	query := `
		SELECT COALESCE(SUM(amount), 0)
		FROM expenses
		WHERE date >= ? AND date <= ?
	`

	var total float64
	err := r.DB.QueryRow(query, startDate, endDate).Scan(&total)
	if err != nil {
		return 0, fmt.Errorf("获取支出总额失败: %v", err)
	}

	return total, nil
}


