package repositories

import (
	"database/sql"
	"fmt"
	"mahjong-system/models"
	"strings"
	"time"
)

// RoomRepository 房间仓储接口
type RoomRepository interface {
	Repository
	Create(room *models.Room) error
	GetByID(id int) (*models.Room, error)
	GetByRoomNumber(roomNumber string) (*models.Room, error)
	Update(room *models.Room) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.Room, int64, error)
	UpdateStatus(id int, status string) error
	GetAvailableRooms() ([]*models.Room, error)
	GetRoomWithDevices(id int) (*models.Room, []models.Device, error)
}

// roomRepository 房间仓储实现
type roomRepository struct {
	BaseRepository
}

// Create 创建房间
func (r *roomRepository) Create(room *models.Room) error {
	query := `
		INSERT INTO rooms (room_number, name, description, status, pricing_rule_id, created_at, updated_at)
		VALUES (:room_number, :name, :description, :status, :pricing_rule_id, :created_at, :updated_at)
	`

	now := time.Now()
	room.CreatedAt = now
	room.UpdatedAt = now

	result, err := r.DB.NamedExec(query, room)
	if err != nil {
		// 检查是否是唯一约束错误
		if strings.Contains(err.Error(), "UNIQUE constraint failed") {
			if strings.Contains(err.Error(), "room_number") {
				return fmt.Errorf("房间号已存在")
			}
			return fmt.Errorf("数据重复")
		}
		return fmt.Errorf("创建房间失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取房间ID失败: %v", err)
	}

	room.ID = int(id)
	return nil
}

// GetByID 根据ID获取房间
func (r *roomRepository) GetByID(id int) (*models.Room, error) {
	var room models.Room
	query := "SELECT * FROM rooms WHERE id = ?"
	
	err := r.DB.Get(&room, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取房间失败: %v", err)
	}
	
	return &room, nil
}

// GetByRoomNumber 根据房间号获取房间
func (r *roomRepository) GetByRoomNumber(roomNumber string) (*models.Room, error) {
	var room models.Room
	query := "SELECT * FROM rooms WHERE room_number = ?"
	
	err := r.DB.Get(&room, query, roomNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取房间失败: %v", err)
	}
	
	return &room, nil
}

// Update 更新房间
func (r *roomRepository) Update(room *models.Room) error {
	query := `
		UPDATE rooms 
		SET name = :name, description = :description, status = :status, 
		    pricing_rule_id = :pricing_rule_id, updated_at = :updated_at
		WHERE id = :id
	`
	
	room.UpdatedAt = time.Now()
	
	result, err := r.DB.NamedExec(query, room)
	if err != nil {
		return fmt.Errorf("更新房间失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("房间不存在")
	}
	
	return nil
}

// Delete 删除房间
func (r *roomRepository) Delete(id int) error {
	query := "DELETE FROM rooms WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除房间失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("房间不存在")
	}
	
	return nil
}

// List 获取房间列表
func (r *roomRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.Room, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()
	
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM rooms " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM rooms " + whereClause + " ORDER BY room_number ASC LIMIT ? OFFSET ?"
	args = append(args, pagination.PageSize, pagination.Offset)
	
	var rooms []*models.Room
	err = r.DB.Select(&rooms, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间列表失败: %v", err)
	}
	
	return rooms, total, nil
}

// UpdateStatus 更新房间状态
func (r *roomRepository) UpdateStatus(id int, status string) error {
	query := "UPDATE rooms SET status = ?, updated_at = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, status, time.Now(), id)
	if err != nil {
		return fmt.Errorf("更新房间状态失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("房间不存在")
	}
	
	return nil
}

// GetAvailableRooms 获取可用房间列表
func (r *roomRepository) GetAvailableRooms() ([]*models.Room, error) {
	query := "SELECT * FROM rooms WHERE status = ? ORDER BY room_number ASC"
	
	var rooms []*models.Room
	err := r.DB.Select(&rooms, query, models.RoomStatusAvailable)
	if err != nil {
		return nil, fmt.Errorf("获取可用房间失败: %v", err)
	}
	
	return rooms, nil
}

// GetRoomWithDevices 获取房间及其设备信息
func (r *roomRepository) GetRoomWithDevices(id int) (*models.Room, []models.Device, error) {
	// 获取房间信息
	room, err := r.GetByID(id)
	if err != nil {
		return nil, nil, err
	}
	if room == nil {
		return nil, nil, nil
	}
	
	// 获取房间设备
	query := "SELECT * FROM devices WHERE room_id = ? ORDER BY type ASC"
	var devices []models.Device
	err = r.DB.Select(&devices, query, id)
	if err != nil {
		return nil, nil, fmt.Errorf("获取房间设备失败: %v", err)
	}
	
	return room, devices, nil
}

// PricingRuleRepository 计费规则仓储接口
type PricingRuleRepository interface {
	Repository
	Create(rule *models.PricingRule) error
	GetByID(id int) (*models.PricingRule, error)
	Update(rule *models.PricingRule) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.PricingRule, int64, error)
	GetActiveRules() ([]*models.PricingRule, error)
}

// pricingRuleRepository 计费规则仓储实现
type pricingRuleRepository struct {
	BaseRepository
}

// Create 创建计费规则
func (r *pricingRuleRepository) Create(rule *models.PricingRule) error {
	query := `
		INSERT INTO pricing_rules (name, price_per_hour, overnight_price, start_time, end_time, is_weekend, is_holiday, created_at)
		VALUES (:name, :price_per_hour, :overnight_price, :start_time, :end_time, :is_weekend, :is_holiday, :created_at)
	`
	
	rule.CreatedAt = time.Now()
	
	result, err := r.DB.NamedExec(query, rule)
	if err != nil {
		return fmt.Errorf("创建计费规则失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取计费规则ID失败: %v", err)
	}
	
	rule.ID = int(id)
	return nil
}

// GetByID 根据ID获取计费规则
func (r *pricingRuleRepository) GetByID(id int) (*models.PricingRule, error) {
	var rule models.PricingRule
	query := "SELECT * FROM pricing_rules WHERE id = ?"
	
	err := r.DB.Get(&rule, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取计费规则失败: %v", err)
	}
	
	return &rule, nil
}

// Update 更新计费规则
func (r *pricingRuleRepository) Update(rule *models.PricingRule) error {
	query := `
		UPDATE pricing_rules 
		SET name = :name, price_per_hour = :price_per_hour, overnight_price = :overnight_price,
		    start_time = :start_time, end_time = :end_time, is_weekend = :is_weekend, is_holiday = :is_holiday
		WHERE id = :id
	`
	
	result, err := r.DB.NamedExec(query, rule)
	if err != nil {
		return fmt.Errorf("更新计费规则失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("计费规则不存在")
	}
	
	return nil
}

// Delete 删除计费规则
func (r *pricingRuleRepository) Delete(id int) error {
	query := "DELETE FROM pricing_rules WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除计费规则失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("计费规则不存在")
	}
	
	return nil
}

// List 获取计费规则列表（支持分页）
func (r *pricingRuleRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.PricingRule, int64, error) {
	// 构建查询条件
	whereClause := ""
	var args []interface{}
	if filters != nil {
		whereClause, args = filters.BuildWhereClause()
	}

	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM pricing_rules " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取计费规则总数失败: %v", err)
	}

	// 查询数据
	query := "SELECT * FROM pricing_rules " + whereClause + " ORDER BY created_at DESC"
	if pagination != nil {
		query += " LIMIT ? OFFSET ?"
		args = append(args, pagination.PageSize, pagination.Offset)
	}

	var rules []*models.PricingRule
	err = r.DB.Select(&rules, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取计费规则列表失败: %v", err)
	}

	return rules, total, nil
}

// GetActiveRules 获取有效的计费规则
func (r *pricingRuleRepository) GetActiveRules() ([]*models.PricingRule, error) {
	// 这里可以根据时间、周末、节假日等条件筛选有效规则
	query := "SELECT * FROM pricing_rules ORDER BY created_at DESC"
	
	var rules []*models.PricingRule
	err := r.DB.Select(&rules, query)
	if err != nil {
		return nil, fmt.Errorf("获取有效计费规则失败: %v", err)
	}
	
	return rules, nil
}
