package repositories

import (
	"database/sql"
	"fmt"
	"mahjong-system/models"
	"time"
)

// PlatformOrderRepository 外卖平台订单仓储接口
type PlatformOrderRepository interface {
	Repository
	Create(order *models.PlatformOrder) error
	GetByID(id int) (*models.PlatformOrder, error)
	GetByPlatformOrderID(platformOrderID string) (*models.PlatformOrder, error)
	Update(order *models.PlatformOrder) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.PlatformOrder, int64, error)
	GetByPlatformType(platformType string, pagination *PaginationParams) ([]*models.PlatformOrder, int64, error)
	GetByUserID(userID int, pagination *PaginationParams) ([]*models.PlatformOrder, int64, error)
	GetByRoomID(roomID int, pagination *PaginationParams) ([]*models.PlatformOrder, int64, error)
	UpdateOrderStatus(id int, status string) error
	UpdateVerificationStatus(id int, status string) error
	GetPendingVerificationOrders() ([]*models.PlatformOrder, error)
	GetOrdersInTimeRange(startTime, endTime time.Time) ([]*models.PlatformOrder, error)
	GetPlatformStatistics() (map[string]interface{}, error)
}

// platformOrderRepository 外卖平台订单仓储实现
type platformOrderRepository struct {
	BaseRepository
}

// Create 创建外卖平台订单
func (r *platformOrderRepository) Create(order *models.PlatformOrder) error {
	query := `
		INSERT INTO platform_orders (platform_type, platform_order_id, room_id, user_id, 
		                           original_amount, discount_amount, paid_amount, order_status, 
		                           verification_status, created_at, verified_at)
		VALUES (:platform_type, :platform_order_id, :room_id, :user_id, 
		        :original_amount, :discount_amount, :paid_amount, :order_status, 
		        :verification_status, :created_at, :verified_at)
	`
	
	order.CreatedAt = time.Now()
	
	result, err := r.DB.NamedExec(query, order)
	if err != nil {
		return fmt.Errorf("创建外卖平台订单失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取外卖平台订单ID失败: %v", err)
	}
	
	order.ID = int(id)
	return nil
}

// GetByID 根据ID获取外卖平台订单
func (r *platformOrderRepository) GetByID(id int) (*models.PlatformOrder, error) {
	// 关联查询rooms表获取房间信息
	query := `
		SELECT
			po.*,
			COALESCE(r.room_number, '') as room_number,
			COALESCE(r.status, '') as room_status
		FROM platform_orders po
		LEFT JOIN rooms r ON po.room_id = r.id
		WHERE po.id = ?
	`

	// 使用自定义结构体接收查询结果
	type OrderWithRoom struct {
		ID                 int        `db:"id"`
		PlatformType       string     `db:"platform_type"`
		PlatformOrderID    string     `db:"platform_order_id"`
		RoomID             *int       `db:"room_id"`
		UserID             *int       `db:"user_id"`
		CustomerName       *string    `db:"customer_name"`
		CustomerPhone      *string    `db:"customer_phone"`
		OriginalAmount     float64    `db:"original_amount"`
		DiscountAmount     float64    `db:"discount_amount"`
		PaidAmount         float64    `db:"paid_amount"`
		OrderStatus        string     `db:"order_status"`
		VerificationStatus string     `db:"verification_status"`
		VerificationCode   *string    `db:"verification_code"`
		VerifiedAt         *time.Time `db:"verified_at"`
		VerifiedBy         *string    `db:"verified_by"`
		VerificationMethod *string    `db:"verification_method"`
		OrderItems         *string    `db:"order_items"`
		CreatedAt          time.Time  `db:"created_at"`
		UpdatedAt          time.Time  `db:"updated_at"`
		ExpiresAt          *time.Time `db:"expires_at"`
		PackageID          *int       `db:"package_id"`
		OriginalPrice      *float64   `db:"original_price"`
		PlatformCommission *float64   `db:"platform_commission"`
		ActualIncome       *float64   `db:"actual_income"`
		PlatformData       *string    `db:"platform_data"`
		RoomNumberDB       string     `db:"room_number"`
		RoomStatusDB       string     `db:"room_status"`
	}

	var result OrderWithRoom
	err := r.DB.Get(&result, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取外卖平台订单失败: %v", err)
	}

	// 转换为PlatformOrder并填充房间信息
	order := &models.PlatformOrder{
		ID:                 result.ID,
		PlatformType:       result.PlatformType,
		PlatformOrderID:    result.PlatformOrderID,
		RoomID:             result.RoomID,
		UserID:             result.UserID,
		CustomerName:       result.CustomerName,
		CustomerPhone:      result.CustomerPhone,
		OriginalAmount:     result.OriginalAmount,
		DiscountAmount:     result.DiscountAmount,
		PaidAmount:         result.PaidAmount,
		OrderStatus:        result.OrderStatus,
		VerificationStatus: result.VerificationStatus,
		VerificationCode:   result.VerificationCode,
		VerifiedAt:         result.VerifiedAt,
		VerifiedBy:         result.VerifiedBy,
		VerificationMethod: result.VerificationMethod,
		OrderItems:         result.OrderItems,
		CreatedAt:          result.CreatedAt,
		UpdatedAt:          result.UpdatedAt,
		RoomNumber:         result.RoomNumberDB,
		RoomStatus:         result.RoomStatusDB,
	}

	return order, nil
}

// GetByPlatformOrderID 根据平台订单ID获取订单
func (r *platformOrderRepository) GetByPlatformOrderID(platformOrderID string) (*models.PlatformOrder, error) {
	var order models.PlatformOrder
	query := "SELECT * FROM platform_orders WHERE platform_order_id = ?"
	
	err := r.DB.Get(&order, query, platformOrderID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取外卖平台订单失败: %v", err)
	}
	
	return &order, nil
}

// Update 更新外卖平台订单
func (r *platformOrderRepository) Update(order *models.PlatformOrder) error {
	query := `
		UPDATE platform_orders 
		SET platform_type = :platform_type, platform_order_id = :platform_order_id, 
		    room_id = :room_id, user_id = :user_id, original_amount = :original_amount,
		    discount_amount = :discount_amount, paid_amount = :paid_amount, 
		    order_status = :order_status, verification_status = :verification_status, 
		    verified_at = :verified_at
		WHERE id = :id
	`
	
	result, err := r.DB.NamedExec(query, order)
	if err != nil {
		return fmt.Errorf("更新外卖平台订单失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("外卖平台订单不存在")
	}
	
	return nil
}

// Delete 删除外卖平台订单
func (r *platformOrderRepository) Delete(id int) error {
	query := "DELETE FROM platform_orders WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除外卖平台订单失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("外卖平台订单不存在")
	}
	
	return nil
}

// List 获取外卖平台订单列表
func (r *platformOrderRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.PlatformOrder, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()

	// 查询总数 - 使用相同的表别名
	countQuery := `
		SELECT COUNT(*) as total
		FROM platform_orders po
		LEFT JOIN rooms r ON po.room_id = r.id
		` + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取外卖平台订单总数失败: %v", err)
	}

	// 查询数据 - 关联查询rooms表获取房间信息
	query := `
		SELECT
			po.*,
			COALESCE(r.room_number, '') as room_number,
			COALESCE(r.status, '') as room_status
		FROM platform_orders po
		LEFT JOIN rooms r ON po.room_id = r.id
		` + whereClause + `
		ORDER BY po.created_at DESC
		LIMIT ? OFFSET ?
	`
	args = append(args, pagination.PageSize, pagination.Offset)

	// 使用自定义结构体接收查询结果
	type OrderWithRoom struct {
		ID                 int        `db:"id"`
		PlatformType       string     `db:"platform_type"`
		PlatformOrderID    string     `db:"platform_order_id"`
		RoomID             *int       `db:"room_id"`
		UserID             *int       `db:"user_id"`
		CustomerName       *string    `db:"customer_name"`
		CustomerPhone      *string    `db:"customer_phone"`
		OriginalAmount     float64    `db:"original_amount"`
		DiscountAmount     float64    `db:"discount_amount"`
		PaidAmount         float64    `db:"paid_amount"`
		OrderStatus        string     `db:"order_status"`
		VerificationStatus string     `db:"verification_status"`
		VerificationCode   *string    `db:"verification_code"`
		VerifiedAt         *time.Time `db:"verified_at"`
		VerifiedBy         *string    `db:"verified_by"`
		VerificationMethod *string    `db:"verification_method"`
		OrderItems         *string    `db:"order_items"`
		CreatedAt          time.Time  `db:"created_at"`
		UpdatedAt          time.Time  `db:"updated_at"`
		ExpiresAt          *time.Time `db:"expires_at"`
		PackageID          *int       `db:"package_id"`
		OriginalPrice      *float64   `db:"original_price"`
		PlatformCommission *float64   `db:"platform_commission"`
		ActualIncome       *float64   `db:"actual_income"`
		PlatformData       *string    `db:"platform_data"`
		RoomNumberDB       string     `db:"room_number"`
		RoomStatusDB       string     `db:"room_status"`
	}

	var orderResults []OrderWithRoom
	err = r.DB.Select(&orderResults, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取外卖平台订单列表失败: %v", err)
	}

	// 转换为PlatformOrder切片并填充房间信息
	var orders []*models.PlatformOrder
	for _, result := range orderResults {
		order := &models.PlatformOrder{
			ID:                 result.ID,
			PlatformType:       result.PlatformType,
			PlatformOrderID:    result.PlatformOrderID,
			RoomID:             result.RoomID,
			UserID:             result.UserID,
			CustomerName:       result.CustomerName,
			CustomerPhone:      result.CustomerPhone,
			OriginalAmount:     result.OriginalAmount,
			DiscountAmount:     result.DiscountAmount,
			PaidAmount:         result.PaidAmount,
			OrderStatus:        result.OrderStatus,
			VerificationStatus: result.VerificationStatus,
			VerificationCode:   result.VerificationCode,
			VerifiedAt:         result.VerifiedAt,
			VerifiedBy:         result.VerifiedBy,
			VerificationMethod: result.VerificationMethod,
			OrderItems:         result.OrderItems,
			CreatedAt:          result.CreatedAt,
			UpdatedAt:          result.UpdatedAt,
			RoomNumber:         result.RoomNumberDB,
			RoomStatus:         result.RoomStatusDB,
		}
		orders = append(orders, order)
	}

	return orders, total, nil
}

// GetByPlatformType 根据平台类型获取订单列表
func (r *platformOrderRepository) GetByPlatformType(platformType string, pagination *PaginationParams) ([]*models.PlatformOrder, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM platform_orders WHERE platform_type = ?"
	total, err := r.GetCount(countQuery, platformType)
	if err != nil {
		return nil, 0, fmt.Errorf("获取平台订单总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM platform_orders WHERE platform_type = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
	
	var orders []*models.PlatformOrder
	err = r.DB.Select(&orders, query, platformType, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取平台订单列表失败: %v", err)
	}
	
	return orders, total, nil
}

// GetByUserID 根据用户ID获取外卖平台订单列表
func (r *platformOrderRepository) GetByUserID(userID int, pagination *PaginationParams) ([]*models.PlatformOrder, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM platform_orders WHERE user_id = ?"
	total, err := r.GetCount(countQuery, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户外卖平台订单总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM platform_orders WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
	
	var orders []*models.PlatformOrder
	err = r.DB.Select(&orders, query, userID, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户外卖平台订单列表失败: %v", err)
	}
	
	return orders, total, nil
}

// GetByRoomID 根据房间ID获取外卖平台订单列表
func (r *platformOrderRepository) GetByRoomID(roomID int, pagination *PaginationParams) ([]*models.PlatformOrder, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM platform_orders WHERE room_id = ?"
	total, err := r.GetCount(countQuery, roomID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间外卖平台订单总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM platform_orders WHERE room_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
	
	var orders []*models.PlatformOrder
	err = r.DB.Select(&orders, query, roomID, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取房间外卖平台订单列表失败: %v", err)
	}
	
	return orders, total, nil
}

// UpdateOrderStatus 更新订单状态
func (r *platformOrderRepository) UpdateOrderStatus(id int, status string) error {
	query := "UPDATE platform_orders SET order_status = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, status, id)
	if err != nil {
		return fmt.Errorf("更新外卖平台订单状态失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("外卖平台订单不存在")
	}
	
	return nil
}

// UpdateVerificationStatus 更新核销状态
func (r *platformOrderRepository) UpdateVerificationStatus(id int, status string) error {
	now := time.Now()
	query := "UPDATE platform_orders SET verification_status = ?, verified_at = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, status, now, id)
	if err != nil {
		return fmt.Errorf("更新外卖平台订单核销状态失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("外卖平台订单不存在")
	}
	
	return nil
}

// GetPendingVerificationOrders 获取待核销订单
func (r *platformOrderRepository) GetPendingVerificationOrders() ([]*models.PlatformOrder, error) {
	query := `
		SELECT
			po.*,
			COALESCE(r.room_number, '') as room_number,
			COALESCE(r.status, '') as room_status
		FROM platform_orders po
		LEFT JOIN rooms r ON po.room_id = r.id
		WHERE po.verification_status = ?
		ORDER BY po.created_at DESC
	`

	// 使用自定义结构体接收查询结果
	type OrderWithRoom struct {
		ID                 int        `db:"id"`
		PlatformType       string     `db:"platform_type"`
		PlatformOrderID    string     `db:"platform_order_id"`
		RoomID             *int       `db:"room_id"`
		UserID             *int       `db:"user_id"`
		CustomerName       *string    `db:"customer_name"`
		CustomerPhone      *string    `db:"customer_phone"`
		OriginalAmount     float64    `db:"original_amount"`
		DiscountAmount     float64    `db:"discount_amount"`
		PaidAmount         float64    `db:"paid_amount"`
		OrderStatus        string     `db:"order_status"`
		VerificationStatus string     `db:"verification_status"`
		VerificationCode   *string    `db:"verification_code"`
		VerifiedAt         *time.Time `db:"verified_at"`
		VerifiedBy         *string    `db:"verified_by"`
		VerificationMethod *string    `db:"verification_method"`
		OrderItems         *string    `db:"order_items"`
		CreatedAt          time.Time  `db:"created_at"`
		UpdatedAt          time.Time  `db:"updated_at"`
		ExpiresAt          *time.Time `db:"expires_at"`
		PackageID          *int       `db:"package_id"`
		OriginalPrice      *float64   `db:"original_price"`
		PlatformCommission *float64   `db:"platform_commission"`
		ActualIncome       *float64   `db:"actual_income"`
		PlatformData       *string    `db:"platform_data"`
		RoomNumberDB       string     `db:"room_number"`
		RoomStatusDB       string     `db:"room_status"`
	}

	var orderResults []OrderWithRoom
	err := r.DB.Select(&orderResults, query, models.VerificationStatusPending)
	if err != nil {
		return nil, fmt.Errorf("获取待核销订单失败: %v", err)
	}

	// 转换为PlatformOrder切片并填充房间信息
	var orders []*models.PlatformOrder
	for _, result := range orderResults {
		order := &models.PlatformOrder{
			ID:                 result.ID,
			PlatformType:       result.PlatformType,
			PlatformOrderID:    result.PlatformOrderID,
			RoomID:             result.RoomID,
			UserID:             result.UserID,
			CustomerName:       result.CustomerName,
			CustomerPhone:      result.CustomerPhone,
			OriginalAmount:     result.OriginalAmount,
			DiscountAmount:     result.DiscountAmount,
			PaidAmount:         result.PaidAmount,
			OrderStatus:        result.OrderStatus,
			VerificationStatus: result.VerificationStatus,
			VerificationCode:   result.VerificationCode,
			VerifiedAt:         result.VerifiedAt,
			VerifiedBy:         result.VerifiedBy,
			VerificationMethod: result.VerificationMethod,
			OrderItems:         result.OrderItems,
			CreatedAt:          result.CreatedAt,
			UpdatedAt:          result.UpdatedAt,
			RoomNumber:         result.RoomNumberDB,
			RoomStatus:         result.RoomStatusDB,
		}
		orders = append(orders, order)
	}

	return orders, nil
}

// GetOrdersInTimeRange 获取时间范围内的订单
func (r *platformOrderRepository) GetOrdersInTimeRange(startTime, endTime time.Time) ([]*models.PlatformOrder, error) {
	query := "SELECT * FROM platform_orders WHERE created_at BETWEEN ? AND ? ORDER BY created_at DESC"
	
	var orders []*models.PlatformOrder
	err := r.DB.Select(&orders, query, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取时间范围内外卖平台订单失败: %v", err)
	}
	
	return orders, nil
}

// GetPlatformStatistics 获取平台统计信息
func (r *platformOrderRepository) GetPlatformStatistics() (map[string]interface{}, error) {
	statistics := make(map[string]interface{})
	
	// 获取总订单数
	totalQuery := "SELECT COUNT(*) as total FROM platform_orders"
	var total int
	err := r.DB.Get(&total, totalQuery)
	if err != nil {
		return nil, fmt.Errorf("获取外卖平台订单总数失败: %v", err)
	}
	statistics["total_orders"] = total
	
	// 获取各平台订单数量
	platformQuery := `
		SELECT platform_type, COUNT(*) as count, COALESCE(SUM(paid_amount), 0) as income
		FROM platform_orders 
		GROUP BY platform_type
	`
	rows, err := r.DB.Query(platformQuery)
	if err != nil {
		return nil, fmt.Errorf("获取平台统计失败: %v", err)
	}
	defer rows.Close()
	
	platformStats := make(map[string]map[string]interface{})
	for rows.Next() {
		var platformType string
		var count int
		var income float64
		if err := rows.Scan(&platformType, &count, &income); err != nil {
			return nil, fmt.Errorf("扫描平台统计失败: %v", err)
		}
		platformStats[platformType] = map[string]interface{}{
			"orders": count,
			"income": income,
		}
	}
	statistics["platforms"] = platformStats
	
	// 获取核销状态统计
	verificationQuery := `
		SELECT verification_status, COUNT(*) as count
		FROM platform_orders 
		GROUP BY verification_status
	`
	rows, err = r.DB.Query(verificationQuery)
	if err != nil {
		return nil, fmt.Errorf("获取核销状态统计失败: %v", err)
	}
	defer rows.Close()
	
	verificationStats := make(map[string]int)
	for rows.Next() {
		var status string
		var count int
		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("扫描核销状态统计失败: %v", err)
		}
		verificationStats[status] = count
	}
	statistics["verification_status"] = verificationStats
	
	return statistics, nil
}
