package repositories

import (
	"fmt"
	"mahjong-system/models"
	"strings"
)

// PackageRepository 套餐仓储接口
type PackageRepository interface {
	Repository
	// 套餐管理
	Create(pkg *models.Package) error
	GetByID(id int) (*models.Package, error)
	Update(pkg *models.Package) error
	Delete(id int) error
	GetList(page, pageSize int, filters map[string]interface{}) ([]*models.Package, int64, error)
	GetActivePackages(packageType string) ([]*models.Package, error)
	UpdateStatus(id int, isActive bool) error
	GetStats() (*models.PackageStatsResponse, error)
}

// UserPackageRepository 用户套餐仓储接口
type UserPackageRepository interface {
	Repository
	// 用户套餐管理
	Create(userPkg *models.UserPackage) error
	GetByID(id int) (*models.UserPackage, error)
	Update(userPkg *models.UserPackage) error
	GetUserPackages(userID int, status string, page, pageSize int) ([]*models.UserPackage, int64, error)
	GetUserActivePackages(userID int) ([]*models.UserPackage, error)
	GetUserStats(userID int) (*models.UserPackageStatsResponse, error)
	UsePackage(id int, hours float64) error
	ExpirePackages() error
}

// PlatformOrderPackageRepository 平台订单套餐仓储接口
type PlatformOrderPackageRepository interface {
	// 平台订单管理
	GetByVerificationCode(code string) (*models.PlatformOrderPackage, error)
	VerifyOrder(id int, userID int) error
	GetList(filters map[string]interface{}, page, pageSize int) ([]*models.PlatformOrderPackage, int64, error)
	GetStats(filters map[string]interface{}) (*models.PlatformStatsResponse, error)
}

// PackageUsageLogRepository 套餐使用记录仓储接口
type PackageUsageLogRepository interface {
	Repository
	Create(log *models.PackageUsageLog) error
	GetByUserPackageID(userPackageID int, page, pageSize int) ([]*models.PackageUsageLog, int64, error)
}

// packageRepository 套餐仓储实现
type packageRepository struct {
	BaseRepository
}

// NewPackageRepository 创建套餐仓储
func NewPackageRepository() PackageRepository {
	return &packageRepository{}
}

// Create 创建套餐
func (r *packageRepository) Create(pkg *models.Package) error {
	err := pkg.BeforeSave()
	if err != nil {
		return err
	}

	query := `
		INSERT INTO packages (
			name, type, duration_hours, original_price, sale_price, 
			discount_rate, description, features, is_active, sort_order, 
			valid_days, min_recharge_hours, max_recharge_hours
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	result, err := r.DB.Exec(query,
		pkg.Name, pkg.Type, pkg.DurationHours, pkg.OriginalPrice, pkg.SalePrice,
		pkg.DiscountRate, pkg.Description, pkg.FeaturesJSON, pkg.IsActive, pkg.SortOrder,
		pkg.ValidDays, pkg.MinRechargeHours, pkg.MaxRechargeHours,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	pkg.ID = int(id)

	return nil
}

// GetByID 根据ID获取套餐
func (r *packageRepository) GetByID(id int) (*models.Package, error) {
	query := `
		SELECT id, name, type, duration_hours, original_price, sale_price, 
			   discount_rate, description, features, is_active, sort_order, 
			   valid_days, min_recharge_hours, max_recharge_hours, 
			   created_at, updated_at
		FROM packages WHERE id = ?
	`
	
	pkg := &models.Package{}
	err := r.DB.QueryRow(query, id).Scan(
		&pkg.ID, &pkg.Name, &pkg.Type, &pkg.DurationHours, &pkg.OriginalPrice, &pkg.SalePrice,
		&pkg.DiscountRate, &pkg.Description, &pkg.FeaturesJSON, &pkg.IsActive, &pkg.SortOrder,
		&pkg.ValidDays, &pkg.MinRechargeHours, &pkg.MaxRechargeHours,
		&pkg.CreatedAt, &pkg.UpdatedAt,
	)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil
		}
		return nil, err
	}

	err = pkg.AfterLoad()
	if err != nil {
		return nil, err
	}

	return pkg, nil
}

// Update 更新套餐
func (r *packageRepository) Update(pkg *models.Package) error {
	err := pkg.BeforeSave()
	if err != nil {
		return err
	}

	query := `
		UPDATE packages SET 
			name = ?, type = ?, duration_hours = ?, original_price = ?, sale_price = ?,
			discount_rate = ?, description = ?, features = ?, is_active = ?, sort_order = ?,
			valid_days = ?, min_recharge_hours = ?, max_recharge_hours = ?, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?
	`
	
	_, err = r.DB.Exec(query,
		pkg.Name, pkg.Type, pkg.DurationHours, pkg.OriginalPrice, pkg.SalePrice,
		pkg.DiscountRate, pkg.Description, pkg.FeaturesJSON, pkg.IsActive, pkg.SortOrder,
		pkg.ValidDays, pkg.MinRechargeHours, pkg.MaxRechargeHours, pkg.ID,
	)
	
	return err
}

// Delete 删除套餐
func (r *packageRepository) Delete(id int) error {
	query := `DELETE FROM packages WHERE id = ?`
	_, err := r.DB.Exec(query, id)
	return err
}

// GetList 获取套餐列表
func (r *packageRepository) GetList(page, pageSize int, filters map[string]interface{}) ([]*models.Package, int64, error) {
	var conditions []string
	var args []interface{}

	// 构建查询条件
	if keyword, ok := filters["keyword"]; ok && keyword != "" {
		conditions = append(conditions, "name LIKE ?")
		args = append(args, "%"+keyword.(string)+"%")
	}
	
	if packageType, ok := filters["type"]; ok && packageType != "" {
		conditions = append(conditions, "type = ?")
		args = append(args, packageType)
	}
	
	if isActive, ok := filters["is_active"]; ok {
		conditions = append(conditions, "is_active = ?")
		args = append(args, isActive)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM packages %s", whereClause)
	var total int64
	err := r.DB.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 查询数据
	query := fmt.Sprintf(`
		SELECT p.id, p.name, p.type, p.duration_hours, p.original_price, p.sale_price, 
			   p.discount_rate, p.description, p.features, p.is_active, p.sort_order, 
			   p.valid_days, p.min_recharge_hours, p.max_recharge_hours, 
			   p.created_at, p.updated_at,
			   COALESCE(stats.sales_count, 0) as sales_count,
			   COALESCE(stats.revenue, 0) as revenue
		FROM packages p
		LEFT JOIN (
			SELECT package_id, COUNT(*) as sales_count, SUM(purchase_price) as revenue
			FROM user_packages 
			GROUP BY package_id
		) stats ON p.id = stats.package_id
		%s
		ORDER BY p.sort_order DESC, p.created_at DESC
		LIMIT ? OFFSET ?
	`, whereClause)

	offset := (page - 1) * pageSize
	args = append(args, pageSize, offset)

	rows, err := r.DB.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var packages []*models.Package
	for rows.Next() {
		pkg := &models.Package{}
		err := rows.Scan(
			&pkg.ID, &pkg.Name, &pkg.Type, &pkg.DurationHours, &pkg.OriginalPrice, &pkg.SalePrice,
			&pkg.DiscountRate, &pkg.Description, &pkg.FeaturesJSON, &pkg.IsActive, &pkg.SortOrder,
			&pkg.ValidDays, &pkg.MinRechargeHours, &pkg.MaxRechargeHours,
			&pkg.CreatedAt, &pkg.UpdatedAt, &pkg.SalesCount, &pkg.Revenue,
		)
		if err != nil {
			return nil, 0, err
		}

		err = pkg.AfterLoad()
		if err != nil {
			return nil, 0, err
		}

		packages = append(packages, pkg)
	}

	return packages, total, nil
}

// GetActivePackages 获取有效套餐
func (r *packageRepository) GetActivePackages(packageType string) ([]*models.Package, error) {
	query := `
		SELECT id, name, type, duration_hours, original_price, sale_price, 
			   discount_rate, description, features, is_active, sort_order, 
			   valid_days, min_recharge_hours, max_recharge_hours, 
			   created_at, updated_at
		FROM packages 
		WHERE is_active = 1
	`
	
	args := []interface{}{}
	if packageType != "" {
		query += " AND type = ?"
		args = append(args, packageType)
	}
	
	query += " ORDER BY sort_order DESC, created_at DESC"

	rows, err := r.DB.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var packages []*models.Package
	for rows.Next() {
		pkg := &models.Package{}
		err := rows.Scan(
			&pkg.ID, &pkg.Name, &pkg.Type, &pkg.DurationHours, &pkg.OriginalPrice, &pkg.SalePrice,
			&pkg.DiscountRate, &pkg.Description, &pkg.FeaturesJSON, &pkg.IsActive, &pkg.SortOrder,
			&pkg.ValidDays, &pkg.MinRechargeHours, &pkg.MaxRechargeHours,
			&pkg.CreatedAt, &pkg.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		err = pkg.AfterLoad()
		if err != nil {
			return nil, err
		}

		packages = append(packages, pkg)
	}

	return packages, nil
}

// UpdateStatus 更新套餐状态
func (r *packageRepository) UpdateStatus(id int, isActive bool) error {
	query := `UPDATE packages SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
	_, err := r.DB.Exec(query, isActive, id)
	return err
}

// GetStats 获取套餐统计
func (r *packageRepository) GetStats() (*models.PackageStatsResponse, error) {
	query := `
		SELECT 
			COUNT(*) as total,
			SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
			COALESCE(SUM(stats.sales_count), 0) as total_sales,
			COALESCE(SUM(stats.revenue), 0) as total_revenue
		FROM packages p
		LEFT JOIN (
			SELECT package_id, COUNT(*) as sales_count, SUM(purchase_price) as revenue
			FROM user_packages 
			GROUP BY package_id
		) stats ON p.id = stats.package_id
	`

	stats := &models.PackageStatsResponse{}
	err := r.DB.QueryRow(query).Scan(
		&stats.Total, &stats.Active, &stats.TotalSales, &stats.TotalRevenue,
	)
	if err != nil {
		return nil, err
	}

	return stats, nil
}

// userPackageRepository 用户套餐仓储实现
type userPackageRepository struct {
	BaseRepository
}

// NewUserPackageRepository 创建用户套餐仓储
func NewUserPackageRepository() UserPackageRepository {
	return &userPackageRepository{}
}

// Create 创建用户套餐
func (r *userPackageRepository) Create(userPkg *models.UserPackage) error {
	query := `
		INSERT INTO user_packages (
			user_id, package_id, order_id, platform_order_id, total_hours,
			used_hours, remaining_hours, purchase_price, status, expires_at, activated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.DB.Exec(query,
		userPkg.UserID, userPkg.PackageID, userPkg.OrderID, userPkg.PlatformOrderID,
		userPkg.TotalHours, userPkg.UsedHours, userPkg.RemainingHours,
		userPkg.PurchasePrice, userPkg.Status, userPkg.ExpiresAt, userPkg.ActivatedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	userPkg.ID = int(id)

	return nil
}

// GetByID 根据ID获取用户套餐
func (r *userPackageRepository) GetByID(id int) (*models.UserPackage, error) {
	query := `
		SELECT up.id, up.user_id, up.package_id, up.order_id, up.platform_order_id,
			   up.total_hours, up.used_hours, up.remaining_hours, up.purchase_price,
			   up.status, up.expires_at, up.activated_at, up.created_at, up.updated_at,
			   p.name, p.type, p.duration_hours, p.description
		FROM user_packages up
		LEFT JOIN packages p ON up.package_id = p.id
		WHERE up.id = ?
	`

	userPkg := &models.UserPackage{}
	pkg := &models.Package{}

	err := r.DB.QueryRow(query, id).Scan(
		&userPkg.ID, &userPkg.UserID, &userPkg.PackageID, &userPkg.OrderID, &userPkg.PlatformOrderID,
		&userPkg.TotalHours, &userPkg.UsedHours, &userPkg.RemainingHours, &userPkg.PurchasePrice,
		&userPkg.Status, &userPkg.ExpiresAt, &userPkg.ActivatedAt, &userPkg.CreatedAt, &userPkg.UpdatedAt,
		&pkg.Name, &pkg.Type, &pkg.DurationHours, &pkg.Description,
	)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			return nil, nil
		}
		return nil, err
	}

	pkg.ID = userPkg.PackageID
	userPkg.Package = pkg

	return userPkg, nil
}

// Update 更新用户套餐
func (r *userPackageRepository) Update(userPkg *models.UserPackage) error {
	query := `
		UPDATE user_packages SET
			used_hours = ?, remaining_hours = ?, status = ?, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?
	`

	_, err := r.DB.Exec(query, userPkg.UsedHours, userPkg.RemainingHours, userPkg.Status, userPkg.ID)
	return err
}

// GetUserPackages 获取用户套餐列表
func (r *userPackageRepository) GetUserPackages(userID int, status string, page, pageSize int) ([]*models.UserPackage, int64, error) {
	var conditions []string
	var args []interface{}

	conditions = append(conditions, "up.user_id = ?")
	args = append(args, userID)

	if status != "" && status != "all" {
		conditions = append(conditions, "up.status = ?")
		args = append(args, status)
	}

	whereClause := "WHERE " + strings.Join(conditions, " AND ")

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM user_packages up %s", whereClause)
	var total int64
	err := r.DB.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 查询数据
	query := fmt.Sprintf(`
		SELECT up.id, up.user_id, up.package_id, up.order_id, up.platform_order_id,
			   up.total_hours, up.used_hours, up.remaining_hours, up.purchase_price,
			   up.status, up.expires_at, up.activated_at, up.created_at, up.updated_at,
			   p.name, p.type, p.duration_hours, p.description, p.features
		FROM user_packages up
		LEFT JOIN packages p ON up.package_id = p.id
		%s
		ORDER BY up.created_at DESC
		LIMIT ? OFFSET ?
	`, whereClause)

	offset := (page - 1) * pageSize
	args = append(args, pageSize, offset)

	rows, err := r.DB.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var userPackages []*models.UserPackage
	for rows.Next() {
		userPkg := &models.UserPackage{}
		pkg := &models.Package{}

		err := rows.Scan(
			&userPkg.ID, &userPkg.UserID, &userPkg.PackageID, &userPkg.OrderID, &userPkg.PlatformOrderID,
			&userPkg.TotalHours, &userPkg.UsedHours, &userPkg.RemainingHours, &userPkg.PurchasePrice,
			&userPkg.Status, &userPkg.ExpiresAt, &userPkg.ActivatedAt, &userPkg.CreatedAt, &userPkg.UpdatedAt,
			&pkg.Name, &pkg.Type, &pkg.DurationHours, &pkg.Description, &pkg.FeaturesJSON,
		)
		if err != nil {
			return nil, 0, err
		}

		err = pkg.AfterLoad()
		if err != nil {
			return nil, 0, err
		}

		pkg.ID = userPkg.PackageID
		userPkg.Package = pkg
		userPackages = append(userPackages, userPkg)
	}

	return userPackages, total, nil
}

// GetUserActivePackages 获取用户有效套餐
func (r *userPackageRepository) GetUserActivePackages(userID int) ([]*models.UserPackage, error) {
	query := `
		SELECT up.id, up.user_id, up.package_id, up.order_id, up.platform_order_id,
			   up.total_hours, up.used_hours, up.remaining_hours, up.purchase_price,
			   up.status, up.expires_at, up.activated_at, up.created_at, up.updated_at,
			   p.name, p.type, p.duration_hours, p.description
		FROM user_packages up
		LEFT JOIN packages p ON up.package_id = p.id
		WHERE up.user_id = ? AND up.status = 'active' AND up.remaining_hours > 0 AND up.expires_at > datetime('now')
		ORDER BY up.expires_at ASC
	`

	rows, err := r.DB.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userPackages []*models.UserPackage
	for rows.Next() {
		userPkg := &models.UserPackage{}
		pkg := &models.Package{}

		err := rows.Scan(
			&userPkg.ID, &userPkg.UserID, &userPkg.PackageID, &userPkg.OrderID, &userPkg.PlatformOrderID,
			&userPkg.TotalHours, &userPkg.UsedHours, &userPkg.RemainingHours, &userPkg.PurchasePrice,
			&userPkg.Status, &userPkg.ExpiresAt, &userPkg.ActivatedAt, &userPkg.CreatedAt, &userPkg.UpdatedAt,
			&pkg.Name, &pkg.Type, &pkg.DurationHours, &pkg.Description,
		)
		if err != nil {
			return nil, err
		}

		pkg.ID = userPkg.PackageID
		userPkg.Package = pkg
		userPackages = append(userPackages, userPkg)
	}

	return userPackages, nil
}

// GetUserStats 获取用户套餐统计
func (r *userPackageRepository) GetUserStats(userID int) (*models.UserPackageStatsResponse, error) {
	query := `
		SELECT
			COUNT(CASE WHEN status = 'active' AND remaining_hours > 0 AND expires_at > datetime('now') THEN 1 END) as active,
			COALESCE(SUM(CASE WHEN status = 'active' THEN remaining_hours ELSE 0 END), 0) as total_hours,
			COALESCE(SUM(purchase_price), 0) as total_value
		FROM user_packages
		WHERE user_id = ?
	`

	stats := &models.UserPackageStatsResponse{}
	err := r.DB.QueryRow(query, userID).Scan(&stats.Active, &stats.TotalHours, &stats.TotalValue)
	if err != nil {
		return nil, err
	}

	return stats, nil
}

// UsePackage 使用套餐
func (r *userPackageRepository) UsePackage(id int, hours float64) error {
	tx, err := r.DB.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 获取当前套餐信息
	var usedHours, remainingHours float64
	var status string
	err = tx.QueryRow("SELECT used_hours, remaining_hours, status FROM user_packages WHERE id = ?", id).
		Scan(&usedHours, &remainingHours, &status)
	if err != nil {
		return err
	}

	// 检查是否可以使用
	if status != "active" {
		return fmt.Errorf("套餐状态不可用")
	}
	if remainingHours < hours {
		return fmt.Errorf("剩余时长不足")
	}

	// 更新套餐使用情况
	newUsedHours := usedHours + hours
	newRemainingHours := remainingHours - hours
	newStatus := status
	if newRemainingHours <= 0 {
		newStatus = "used_up"
	}

	_, err = tx.Exec(
		"UPDATE user_packages SET used_hours = ?, remaining_hours = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
		newUsedHours, newRemainingHours, newStatus, id,
	)
	if err != nil {
		return err
	}

	return tx.Commit()
}

// ExpirePackages 过期套餐处理
func (r *userPackageRepository) ExpirePackages() error {
	query := `
		UPDATE user_packages
		SET status = 'expired', updated_at = CURRENT_TIMESTAMP
		WHERE status = 'active' AND expires_at <= datetime('now')
	`
	_, err := r.DB.Exec(query)
	return err
}

// packageUsageLogRepository 套餐使用记录仓储实现
type packageUsageLogRepository struct {
	BaseRepository
}

// NewPackageUsageLogRepository 创建套餐使用记录仓储
func NewPackageUsageLogRepository() PackageUsageLogRepository {
	return &packageUsageLogRepository{}
}

// Create 创建使用记录
func (r *packageUsageLogRepository) Create(log *models.PackageUsageLog) error {
	query := `
		INSERT INTO package_usage_logs (
			user_package_id, order_id, hours_used, hours_before, hours_after,
			room_id, started_at, ended_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.DB.Exec(query,
		log.UserPackageID, log.OrderID, log.HoursUsed, log.HoursBefore, log.HoursAfter,
		log.RoomID, log.StartedAt, log.EndedAt,
	)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}
	log.ID = int(id)

	return nil
}

// GetByUserPackageID 根据用户套餐ID获取使用记录
func (r *packageUsageLogRepository) GetByUserPackageID(userPackageID int, page, pageSize int) ([]*models.PackageUsageLog, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) FROM package_usage_logs WHERE user_package_id = ?"
	var total int64
	err := r.DB.QueryRow(countQuery, userPackageID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 查询数据
	query := `
		SELECT pul.id, pul.user_package_id, pul.order_id, pul.hours_used,
			   pul.hours_before, pul.hours_after, pul.room_id, pul.started_at,
			   pul.ended_at, pul.created_at,
			   r.name, r.room_number
		FROM package_usage_logs pul
		LEFT JOIN rooms r ON pul.room_id = r.id
		WHERE pul.user_package_id = ?
		ORDER BY pul.started_at DESC
		LIMIT ? OFFSET ?
	`

	offset := (page - 1) * pageSize
	rows, err := r.DB.Query(query, userPackageID, pageSize, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var logs []*models.PackageUsageLog
	for rows.Next() {
		log := &models.PackageUsageLog{}
		room := &models.Room{}

		err := rows.Scan(
			&log.ID, &log.UserPackageID, &log.OrderID, &log.HoursUsed,
			&log.HoursBefore, &log.HoursAfter, &log.RoomID, &log.StartedAt,
			&log.EndedAt, &log.CreatedAt,
			&room.Name, &room.RoomNumber,
		)
		if err != nil {
			return nil, 0, err
		}

		room.ID = log.RoomID
		log.Room = room
		logs = append(logs, log)
	}

	return logs, total, nil
}
