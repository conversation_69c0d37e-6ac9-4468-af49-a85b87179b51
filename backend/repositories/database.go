package repositories

import (
	"fmt"
	"log"
	"strings"

	"github.com/jmoiron/sqlx"
	_ "github.com/mattn/go-sqlite3"
)

// Database 数据库连接管理
type Database struct {
	DB *sqlx.DB
}

// NewDatabase 创建数据库连接
func NewDatabase(dsn string) (*Database, error) {
	// 在DSN中添加外键约束参数
	if dsn != "" && !strings.Contains(dsn, "?") {
		dsn += "?_foreign_keys=on"
	} else if dsn != "" && !strings.Contains(dsn, "_foreign_keys") {
		dsn += "&_foreign_keys=on"
	}

	db, err := sqlx.Connect("sqlite3", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)

	// 启用外键约束（双重保险）
	if _, err := db.Exec("PRAGMA foreign_keys = ON"); err != nil {
		return nil, fmt.Errorf("启用外键约束失败: %v", err)
	}

	// 验证外键约束是否启用
	var foreignKeysEnabled int
	if err := db.Get(&foreignKeysEnabled, "PRAGMA foreign_keys"); err != nil {
		return nil, fmt.Errorf("检查外键约束状态失败: %v", err)
	}
	if foreignKeysEnabled != 1 {
		return nil, fmt.Errorf("外键约束未能正确启用")
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %v", err)
	}

	log.Printf("数据库连接成功，外键约束已启用")

	return &Database{DB: db}, nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	return d.DB.Close()
}

// BeginTx 开始事务
func (d *Database) BeginTx() (*sqlx.Tx, error) {
	return d.DB.Beginx()
}

// WithTransaction 在事务中执行操作
func (d *Database) WithTransaction(fn func(*sqlx.Tx) error) error {
	tx, err := d.BeginTx()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	err = fn(tx)
	return err
}

// Repository 基础仓储接口
type Repository interface {
	SetDB(db *sqlx.DB)
}

// BaseRepository 基础仓储结构
type BaseRepository struct {
	DB *sqlx.DB
}

// SetDB 设置数据库连接
func (r *BaseRepository) SetDB(db *sqlx.DB) {
	r.DB = db
}

// GetDB 获取数据库连接
func (r *BaseRepository) GetDB() *sqlx.DB {
	return r.DB
}

// Repositories 所有仓储的集合
type Repositories struct {
	User         UserRepository
	Room         RoomRepository
	Order        OrderRepository
	Device       DeviceRepository
	Reservation  ReservationRepository
	PricingRule  PricingRuleRepository
	Promotion    PromotionRepository
	PlatformOrder PlatformOrderRepository
	Expense      ExpenseRepository
	Package      PackageRepository
	UserPackage  UserPackageRepository
	PackageUsageLog PackageUsageLogRepository
}

// NewRepositories 创建所有仓储实例
func NewRepositories(db *Database) *Repositories {
	repos := &Repositories{
		User:         &userRepository{},
		Room:         &roomRepository{},
		Order:        &orderRepository{},
		Device:       &deviceRepository{},
		Reservation:  &reservationRepository{},
		PricingRule:  &pricingRuleRepository{},
		Promotion:    &promotionRepository{},
		PlatformOrder: &platformOrderRepository{},
		Expense:      &expenseRepository{},
		Package:      &packageRepository{},
		UserPackage:  &userPackageRepository{},
		PackageUsageLog: &packageUsageLogRepository{},
	}

	// 为所有仓储设置数据库连接
	repos.User.SetDB(db.DB)
	repos.Room.SetDB(db.DB)
	repos.Order.SetDB(db.DB)
	repos.Device.SetDB(db.DB)
	repos.Reservation.SetDB(db.DB)
	repos.PricingRule.SetDB(db.DB)
	repos.Promotion.SetDB(db.DB)
	repos.PlatformOrder.SetDB(db.DB)
	repos.Expense.SetDB(db.DB)
	repos.Package.SetDB(db.DB)
	repos.UserPackage.SetDB(db.DB)
	repos.PackageUsageLog.SetDB(db.DB)

	return repos
}

// PaginationParams 分页参数
type PaginationParams struct {
	Page     int
	PageSize int
	Offset   int
}

// NewPaginationParams 创建分页参数
func NewPaginationParams(page, pageSize int) *PaginationParams {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	offset := (page - 1) * pageSize

	return &PaginationParams{
		Page:     page,
		PageSize: pageSize,
		Offset:   offset,
	}
}

// FilterParams 过滤参数
type FilterParams struct {
	Conditions []string
	Args       []interface{}
}

// NewFilterParams 创建过滤参数
func NewFilterParams() *FilterParams {
	return &FilterParams{
		Conditions: make([]string, 0),
		Args:       make([]interface{}, 0),
	}
}

// AddCondition 添加过滤条件
func (f *FilterParams) AddCondition(condition string, args ...interface{}) {
	f.Conditions = append(f.Conditions, condition)
	f.Args = append(f.Args, args...)
}

// BuildWhereClause 构建WHERE子句
func (f *FilterParams) BuildWhereClause() (string, []interface{}) {
	if len(f.Conditions) == 0 {
		return "", nil
	}

	whereClause := "WHERE " + f.Conditions[0]
	for i := 1; i < len(f.Conditions); i++ {
		whereClause += " AND " + f.Conditions[i]
	}

	return whereClause, f.Args
}

// CountResult 计数查询结果
type CountResult struct {
	Total int64 `db:"total"`
}

// GetCount 获取记录总数
func (r *BaseRepository) GetCount(query string, args ...interface{}) (int64, error) {
	var result CountResult
	err := r.DB.Get(&result, query, args...)
	if err != nil {
		return 0, err
	}
	return result.Total, nil
}
