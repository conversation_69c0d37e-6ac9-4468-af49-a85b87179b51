package repositories

import (
	"database/sql"
	"fmt"
	"mahjong-system/models"
	"time"

	"github.com/jmoiron/sqlx"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	Repository
	Create(user *models.User) error
	GetByID(id int) (*models.User, error)
	GetByOpenID(openid string) (*models.User, error)
	Update(user *models.User) error
	Delete(id int) error
	List(pagination *PaginationParams, filters *FilterParams) ([]*models.User, int64, error)
	UpdateBalance(userID int, amount float64) error
	CreateBalanceRecord(record *models.BalanceRecord) error
	GetBalanceRecords(userID int, pagination *PaginationParams) ([]*models.BalanceRecord, int64, error)
	GetUserOrderStats(userID int) (int, float64, error)
	GetUsersWithOrderStats(pagination *PaginationParams, filters *FilterParams) ([]*models.UserWithStats, int64, error)
	GetDB() *sqlx.DB
}

// userRepository 用户仓储实现
type userRepository struct {
	BaseRepository
}

// Create 创建用户
func (r *userRepository) Create(user *models.User) error {
	query := `
		INSERT INTO users (openid, nickname, avatar_url, phone, balance, created_at, updated_at)
		VALUES (:openid, :nickname, :avatar_url, :phone, :balance, :created_at, :updated_at)
	`
	
	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now
	
	result, err := r.DB.NamedExec(query, user)
	if err != nil {
		return fmt.Errorf("创建用户失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取用户ID失败: %v", err)
	}
	
	user.ID = int(id)
	return nil
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id int) (*models.User, error) {
	var user models.User
	query := "SELECT * FROM users WHERE id = ?"
	
	err := r.DB.Get(&user, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取用户失败: %v", err)
	}
	
	return &user, nil
}

// GetByOpenID 根据OpenID获取用户
func (r *userRepository) GetByOpenID(openid string) (*models.User, error) {
	var user models.User
	query := "SELECT * FROM users WHERE openid = ?"
	
	err := r.DB.Get(&user, query, openid)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取用户失败: %v", err)
	}
	
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(user *models.User) error {
	query := `
		UPDATE users 
		SET nickname = :nickname, avatar_url = :avatar_url, phone = :phone, 
		    balance = :balance, updated_at = :updated_at
		WHERE id = :id
	`
	
	user.UpdatedAt = time.Now()
	
	result, err := r.DB.NamedExec(query, user)
	if err != nil {
		return fmt.Errorf("更新用户失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("用户不存在")
	}
	
	return nil
}

// Delete 删除用户
func (r *userRepository) Delete(id int) error {
	query := "DELETE FROM users WHERE id = ?"
	
	result, err := r.DB.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除用户失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("用户不存在")
	}
	
	return nil
}

// List 获取用户列表
func (r *userRepository) List(pagination *PaginationParams, filters *FilterParams) ([]*models.User, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()
	
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM users " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户总数失败: %v", err)
	}
	
	// 查询数据
	query := "SELECT * FROM users " + whereClause + " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, pagination.PageSize, pagination.Offset)
	
	var users []*models.User
	err = r.DB.Select(&users, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户列表失败: %v", err)
	}
	
	return users, total, nil
}

// UpdateBalance 更新用户余额
func (r *userRepository) UpdateBalance(userID int, amount float64) error {
	query := "UPDATE users SET balance = balance + ?, updated_at = ? WHERE id = ?"
	
	result, err := r.DB.Exec(query, amount, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("更新用户余额失败: %v", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %v", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("用户不存在")
	}
	
	return nil
}

// CreateBalanceRecord 创建余额变动记录
func (r *userRepository) CreateBalanceRecord(record *models.BalanceRecord) error {
	// 注意：这里需要先在数据库中创建balance_records表
	query := `
		INSERT INTO balance_records (user_id, type, amount, balance, description, created_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`
	
	record.CreatedAt = time.Now()
	
	result, err := r.DB.Exec(query, record.UserID, record.Type, record.Amount, 
		record.Balance, record.Description, record.CreatedAt)
	if err != nil {
		return fmt.Errorf("创建余额记录失败: %v", err)
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取余额记录ID失败: %v", err)
	}
	
	record.ID = int(id)
	return nil
}

// GetBalanceRecords 获取用户余额变动记录
func (r *userRepository) GetBalanceRecords(userID int, pagination *PaginationParams) ([]*models.BalanceRecord, int64, error) {
	// 查询总数
	countQuery := "SELECT COUNT(*) as total FROM balance_records WHERE user_id = ?"
	total, err := r.GetCount(countQuery, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取余额记录总数失败: %v", err)
	}
	
	// 查询数据
	query := `
		SELECT * FROM balance_records 
		WHERE user_id = ? 
		ORDER BY created_at DESC 
		LIMIT ? OFFSET ?
	`
	
	var records []*models.BalanceRecord
	err = r.DB.Select(&records, query, userID, pagination.PageSize, pagination.Offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取余额记录失败: %v", err)
	}
	
	return records, total, nil
}

// GetUsersWithOrderStats 获取包含订单统计的用户列表（优化版本，避免N+1查询）
func (r *userRepository) GetUsersWithOrderStats(pagination *PaginationParams, filters *FilterParams) ([]*models.UserWithStats, int64, error) {
	// 构建查询条件
	whereClause, args := filters.BuildWhereClause()

	// 查询总数（使用索引优化）
	countQuery := "SELECT COUNT(*) as total FROM users " + whereClause
	total, err := r.GetCount(countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户总数失败: %v", err)
	}

	// 优化的查询：使用LEFT JOIN一次性获取用户和订单统计
	// 利用新创建的索引 idx_orders_user_stats 和 idx_users_created_at_desc
	query := `
		SELECT
			u.id, u.openid, u.nickname, u.phone, u.avatar_url, u.balance, u.created_at, u.updated_at,
			COALESCE(o.order_count, 0) as order_count,
			COALESCE(o.total_consumption, 0.0) as total_consumption
		FROM users u
		LEFT JOIN (
			SELECT
				user_id,
				COUNT(*) as order_count,
				SUM(total_amount) as total_consumption
			FROM orders
			WHERE status IN ('paid', 'completed', 'in_use')
			GROUP BY user_id
		) o ON u.id = o.user_id
		` + whereClause + `
		ORDER BY u.created_at DESC
		LIMIT ? OFFSET ?
	`

	// 添加分页参数
	args = append(args, pagination.PageSize, pagination.Offset)

	var users []*models.UserWithStats
	err = r.DB.Select(&users, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("获取用户列表失败: %v", err)
	}

	return users, total, nil
}

// GetUserOrderStats 获取用户订单统计信息
func (r *userRepository) GetUserOrderStats(userID int) (int, float64, error) {
	query := `
		SELECT
			COUNT(*) as order_count,
			COALESCE(SUM(paid_amount), 0) as total_consumption
		FROM orders
		WHERE user_id = ? AND status IN ('paid', 'completed')
	`

	var orderCount int
	var totalConsumption float64

	err := r.DB.QueryRow(query, userID).Scan(&orderCount, &totalConsumption)
	if err != nil {
		return 0, 0, fmt.Errorf("获取用户订单统计失败: %v", err)
	}

	return orderCount, totalConsumption, nil
}
