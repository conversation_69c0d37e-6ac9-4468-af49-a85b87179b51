# 自助麻将室系统配置文件

# 服务器配置
server:
  port: 8080
  mode: debug  # debug, release, test

# 数据库配置
database:
  driver: sqlite3
  dsn: "majiang.db"
  max_open_conns: 10
  max_idle_conns: 5

# MQTT 配置
mqtt:
  broker: "tcp://test.mosquitto.org:1883"
  client_id: "mahjong-backend-test"
  username: ""
  password: ""
  qos: 1
  topics:
    control: "mahjong/device/+/control"
    status: "mahjong/device/+/status"
    heartbeat: "mahjong/device/+/heartbeat"

# 日志配置
log:
  level: debug  # debug, info, warn, error
  file_path: ""  # 空字符串表示只使用控制台输出
  max_size: 100  # MB
  max_backups: 3
  max_age: 28  # days

# 微信支付配置
wechat_pay:
  app_id: ""
  mch_id: ""
  api_key: ""
  notify_url: ""

# 美团配置
meituan:
  app_id: ""
  app_secret: ""
  api_url: "https://api.meituan.com"

# 饿了么配置
eleme:
  app_id: ""
  app_secret: ""
  api_url: "https://open-api.shop.ele.me"

# JWT 配置
jwt:
  secret: "mahjong-system-jwt-secret"
  expire_hours: 24

# 系统配置
system:
  default_pricing_rule_id: 1
  heartbeat_timeout: 180  # 秒
  order_timeout: 3600     # 秒
