package config

import (
	"fmt"
	"mahjong-system/mqtt"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Server    ServerConfig    `yaml:"server"`
	Database  DatabaseConfig  `yaml:"database"`
	MQTT      mqtt.MQTTConfig `yaml:"mqtt"`
	Log       LogConfig       `yaml:"log"`
	WechatPay WechatPayConfig `yaml:"wechat_pay"`
	<PERSON><PERSON><PERSON>anConfig   `yaml:"meituan"`
	Eleme     ElemeConfig     `yaml:"eleme"`
	JWT       JWTConfig       `yaml:"jwt"`
	System    SystemConfig    `yaml:"system"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `yaml:"port"`
	Mode string `yaml:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver       string `yaml:"driver"`
	DSN          string `yaml:"dsn"`
	MaxOpenConns int    `yaml:"max_open_conns"`
	MaxIdleConns int    `yaml:"max_idle_conns"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	FilePath   string `yaml:"file_path"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// WechatPayConfig 微信支付配置
type WechatPayConfig struct {
	AppID     string `yaml:"app_id"`
	MchID     string `yaml:"mch_id"`
	APIKey    string `yaml:"api_key"`
	NotifyURL string `yaml:"notify_url"`
}

// MeituanConfig 美团配置
type MeituanConfig struct {
	AppID     string `yaml:"app_id"`
	AppSecret string `yaml:"app_secret"`
	APIURL    string `yaml:"api_url"`
}

// ElemeConfig 饿了么配置
type ElemeConfig struct {
	AppID     string `yaml:"app_id"`
	AppSecret string `yaml:"app_secret"`
	APIURL    string `yaml:"api_url"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret      string `yaml:"secret"`
	ExpireHours int    `yaml:"expire_hours"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	DefaultPricingRuleID int `yaml:"default_pricing_rule_id"`
	HeartbeatTimeout     int `yaml:"heartbeat_timeout"`
	OrderTimeout         int `yaml:"order_timeout"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析配置
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.port", "8080")
	viper.SetDefault("server.mode", "debug")

	// 数据库默认配置
	viper.SetDefault("database.driver", "sqlite3")
	viper.SetDefault("database.dsn", "../database/mahjong.db")
	viper.SetDefault("database.max_open_conns", 10)
	viper.SetDefault("database.max_idle_conns", 5)

	// MQTT默认配置
	viper.SetDefault("mqtt.broker", "tcp://localhost:1883")
	viper.SetDefault("mqtt.client_id", "mahjong-backend")
	viper.SetDefault("mqtt.username", "backend_user")
	viper.SetDefault("mqtt.password", "backend_password")
	viper.SetDefault("mqtt.qos", 1)
	viper.SetDefault("mqtt.topics.control", "mahjong/device/+/control")
	viper.SetDefault("mqtt.topics.status", "mahjong/device/+/status")
	viper.SetDefault("mqtt.topics.heartbeat", "mahjong/device/+/heartbeat")

	// 日志默认配置
	viper.SetDefault("log.level", "debug")
	viper.SetDefault("log.file_path", "./logs/app.log")
	viper.SetDefault("log.max_size", 100)
	viper.SetDefault("log.max_backups", 3)
	viper.SetDefault("log.max_age", 28)

	// JWT默认配置
	viper.SetDefault("jwt.secret", "mahjong-system-jwt-secret")
	viper.SetDefault("jwt.expire_hours", 24)

	// 系统默认配置
	viper.SetDefault("system.default_pricing_rule_id", 1)
	viper.SetDefault("system.heartbeat_timeout", 180)
	viper.SetDefault("system.order_timeout", 3600)
}

// GetMQTTConfig 获取MQTT配置
func (c *Config) GetMQTTConfig() *mqtt.MQTTConfig {
	return &c.MQTT
}

// IsDevelopment 是否为开发模式
func (c *Config) IsDevelopment() bool {
	return c.Server.Mode == "debug"
}

// IsProduction 是否为生产模式
func (c *Config) IsProduction() bool {
	return c.Server.Mode == "release"
}

// GetServerAddress 获取服务器地址
func (c *Config) GetServerAddress() string {
	return ":" + c.Server.Port
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 验证必要的配置项
	if c.Database.DSN == "" {
		return fmt.Errorf("数据库DSN不能为空")
	}

	if c.MQTT.Broker == "" {
		return fmt.Errorf("MQTT Broker地址不能为空")
	}

	if c.JWT.Secret == "" {
		return fmt.Errorf("JWT密钥不能为空")
	}

	return nil
}
