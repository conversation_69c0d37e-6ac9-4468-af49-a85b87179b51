const { chromium } = require('playwright');

(async () => {
  console.log('🚀 启动详细订单界面测试...');
  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-dev-shm-usage']
  });
  
  const page = await browser.newPage();
  
  // 监听控制台消息
  page.on('console', msg => {
    const text = msg.text();
    if (!text.includes('ElementPlusError') && !text.includes('[vite]')) {
      console.log('🖥️ 浏览器控制台:', text);
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log('❌ 页面错误:', error.message);
  });
  
  try {
    console.log('📄 访问订单列表页面...');
    await page.goto('http://localhost:3000/orders/list');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    console.log('📊 检查统计卡片...');
    
    // 检查统计卡片
    const statsCards = await page.locator('.stats-card').count();
    console.log('统计卡片数量:', statsCards);
    
    if (statsCards > 0) {
      for (let i = 0; i < statsCards; i++) {
        const card = page.locator('.stats-card').nth(i);
        const value = await card.locator('.stats-value').textContent();
        const label = await card.locator('.stats-label').textContent();
        console.log(`统计卡片 ${i + 1}: ${label} = ${value}`);
      }
    }
    
    console.log('📋 检查订单表格...');
    
    // 检查订单表格
    const tableRows = await page.locator('.el-table tbody tr').count();
    console.log('订单行数:', tableRows);
    
    if (tableRows > 0) {
      console.log('📝 检查订单数据...');
      
      // 检查第一行订单数据
      const firstRow = page.locator('.el-table tbody tr').first();
      const orderNumber = await firstRow.locator('td').nth(0).textContent();
      const roomName = await firstRow.locator('td').nth(1).textContent();
      const amount = await firstRow.locator('td').nth(3).textContent();
      const status = await firstRow.locator('td').nth(4).textContent();
      
      console.log('第一个订单信息:');
      console.log('  订单号:', orderNumber?.trim());
      console.log('  房间:', roomName?.trim());
      console.log('  金额:', amount?.trim());
      console.log('  状态:', status?.trim());
    }
    
    console.log('🔍 检查搜索功能...');
    
    // 检查搜索表单
    const searchForm = await page.locator('.search-form, .filter-form').count();
    console.log('搜索表单数量:', searchForm);
    
    // 检查输入框
    const inputs = await page.locator('input').count();
    console.log('输入框总数:', inputs);
    
    // 检查下拉选择框
    const selects = await page.locator('.el-select').count();
    console.log('下拉选择框数量:', selects);
    
    console.log('🔘 检查操作按钮...');
    
    // 检查操作按钮
    const actionButtons = await page.locator('.el-table .el-button').count();
    console.log('表格操作按钮数量:', actionButtons);
    
    // 检查分页
    console.log('📄 检查分页功能...');
    const pagination = await page.locator('.el-pagination').count();
    if (pagination > 0) {
      const totalText = await page.locator('.el-pagination .el-pagination__total').textContent();
      console.log('分页信息:', totalText?.trim());
    }
    
    console.log('✅ 订单界面功能检查完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('🔒 关闭浏览器...');
  await browser.close();
  console.log('✅ 详细订单界面测试完成!');
})().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
