package utils

import (
	"fmt"
	"regexp"
	"strings"
	"time"
)

// ValidateAmount 验证金额
func ValidateAmount(amount float64, min, max float64) error {
	if amount <= min {
		return fmt.Errorf("金额必须大于%.2f", min)
	}
	if amount > max {
		return fmt.Errorf("金额不能超过%.2f", max)
	}
	// 检查小数位数（最多2位）
	if amount*100 != float64(int(amount*100)) {
		return fmt.Errorf("金额最多支持2位小数")
	}
	return nil
}

// ValidatePhoneNumber 验证手机号
func ValidatePhoneNumber(phone string) error {
	if phone == "" {
		return nil // 手机号可以为空
	}
	
	// 中国大陆手机号正则
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(phone) {
		return fmt.Errorf("手机号格式不正确")
	}
	return nil
}

// ValidateRoomNumber 验证房间号
func ValidateRoomNumber(roomNumber string) error {
	if len(roomNumber) == 0 {
		return fmt.E<PERSON><PERSON>("房间号不能为空")
	}
	if len(roomNumber) > 20 {
		return fmt.Errorf("房间号长度不能超过20个字符")
	}
	
	// 房间号只能包含字母、数字和短横线
	roomRegex := regexp.MustCompile(`^[A-Za-z0-9\-]+$`)
	if !roomRegex.MatchString(roomNumber) {
		return fmt.Errorf("房间号只能包含字母、数字和短横线")
	}
	return nil
}

// ValidateTimeRange 验证时间范围
func ValidateTimeRange(startTime, endTime time.Time) error {
	if endTime.Before(startTime) {
		return fmt.Errorf("结束时间不能早于开始时间")
	}
	
	// 检查时间范围是否合理（不超过30天）
	duration := endTime.Sub(startTime)
	if duration > 30*24*time.Hour {
		return fmt.Errorf("时间范围不能超过30天")
	}
	
	return nil
}

// ValidateHours 验证小时数
func ValidateHours(hours int, min, max int) error {
	if hours < min {
		return fmt.Errorf("小时数不能少于%d", min)
	}
	if hours > max {
		return fmt.Errorf("小时数不能超过%d", max)
	}
	return nil
}

// ValidatePaymentMethod 验证支付方式
func ValidatePaymentMethod(method string) error {
	validMethods := []string{"wechat", "alipay", "balance"}
	for _, valid := range validMethods {
		if method == valid {
			return nil
		}
	}
	return fmt.Errorf("不支持的支付方式: %s", method)
}

// ValidateRoomStatus 验证房间状态
func ValidateRoomStatus(status string) error {
	validStatuses := []string{"available", "occupied", "maintenance"}
	for _, valid := range validStatuses {
		if status == valid {
			return nil
		}
	}
	return fmt.Errorf("无效的房间状态: %s", status)
}

// ValidateOrderStatus 验证订单状态
func ValidateOrderStatus(status string) error {
	validStatuses := []string{"pending", "paid", "in_use", "completed", "cancelled"}
	for _, valid := range validStatuses {
		if status == valid {
			return nil
		}
	}
	return fmt.Errorf("无效的订单状态: %s", status)
}

// ValidateStringLength 验证字符串长度
func ValidateStringLength(str, fieldName string, min, max int) error {
	length := len(strings.TrimSpace(str))
	if min > 0 && length < min {
		return fmt.Errorf("%s长度不能少于%d个字符", fieldName, min)
	}
	if max > 0 && length > max {
		return fmt.Errorf("%s长度不能超过%d个字符", fieldName, max)
	}
	return nil
}

// ValidateID 验证ID
func ValidateID(id int, fieldName string) error {
	if id <= 0 {
		return fmt.Errorf("%sID必须大于0", fieldName)
	}
	return nil
}

// ValidatePaginationParams 验证分页参数
func ValidatePaginationParams(page, pageSize int) (int, int, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		return 0, 0, fmt.Errorf("每页数量不能超过100")
	}
	return page, pageSize, nil
}

// ValidateTimeFormat 验证时间格式
func ValidateTimeFormat(timeStr, format string) error {
	if timeStr == "" {
		return nil // 空时间可以接受
	}
	
	_, err := time.Parse(format, timeStr)
	if err != nil {
		return fmt.Errorf("时间格式不正确，应为: %s", format)
	}
	return nil
}

// ValidateBusinessHours 验证营业时间
func ValidateBusinessHours(startTime, endTime string) error {
	if startTime == "" || endTime == "" {
		return nil // 空时间可以接受
	}
	
	start, err := time.Parse("15:04", startTime)
	if err != nil {
		return fmt.Errorf("开始时间格式不正确，应为HH:MM")
	}
	
	end, err := time.Parse("15:04", endTime)
	if err != nil {
		return fmt.Errorf("结束时间格式不正确，应为HH:MM")
	}
	
	// 如果结束时间小于开始时间，说明跨天了（如22:00-06:00）
	if end.Before(start) {
		// 跨天的情况是允许的
		return nil
	}
	
	return nil
}

// ValidateDiscountRate 验证折扣率
func ValidateDiscountRate(rate float64) error {
	if rate < 0 || rate > 1 {
		return fmt.Errorf("折扣率必须在0-1之间")
	}
	return nil
}

// ValidatePrice 验证价格关系
func ValidatePrice(salePrice, originalPrice float64) error {
	if salePrice > originalPrice {
		return fmt.Errorf("售价不能高于原价")
	}
	if salePrice < 0 || originalPrice < 0 {
		return fmt.Errorf("价格不能为负数")
	}
	return nil
}

// SanitizeString 清理字符串（去除危险字符）
func SanitizeString(str string) string {
	// 去除首尾空格
	str = strings.TrimSpace(str)
	
	// 替换可能的SQL注入字符
	str = strings.ReplaceAll(str, "'", "''")
	str = strings.ReplaceAll(str, "\"", "\\\"")
	
	// 去除HTML标签
	htmlRegex := regexp.MustCompile(`<[^>]*>`)
	str = htmlRegex.ReplaceAllString(str, "")
	
	return str
}

// ValidateArrayLength 验证数组长度
func ValidateArrayLength(arr []interface{}, fieldName string, min, max int) error {
	length := len(arr)
	if min > 0 && length < min {
		return fmt.Errorf("%s数量不能少于%d个", fieldName, min)
	}
	if max > 0 && length > max {
		return fmt.Errorf("%s数量不能超过%d个", fieldName, max)
	}
	return nil
}

// ValidateEnum 验证枚举值
func ValidateEnum(value string, validValues []string, fieldName string) error {
	for _, valid := range validValues {
		if value == valid {
			return nil
		}
	}
	return fmt.Errorf("%s的值无效，有效值为: %s", fieldName, strings.Join(validValues, ", "))
}
