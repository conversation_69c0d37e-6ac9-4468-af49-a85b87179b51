package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID        int       `json:"id" db:"id"`
	OpenID    string    `json:"openid" db:"openid"`
	Nickname  *string   `json:"nickname" db:"nickname"`
	AvatarURL *string   `json:"avatar_url" db:"avatar_url"`
	Phone     *string   `json:"phone" db:"phone"`
	Balance   float64   `json:"balance" db:"balance"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// UserRegisterRequest 用户注册请求
type UserRegisterRequest struct {
	OpenID    string `json:"openid" binding:"required"`
	Nickname  string `json:"nickname"`
	AvatarURL string `json:"avatar_url"`
	Phone     string `json:"phone"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	OpenID string `json:"openid" binding:"required"`
}

// UserUpdateRequest 用户信息更新请求
type UserUpdateRequest struct {
	Nickname  string `json:"nickname"`
	AvatarURL string `json:"avatar_url"`
	Phone     string `json:"phone"`
}

// UserRechargeRequest 用户充值请求
type UserRechargeRequest struct {
	Amount        float64 `json:"amount" binding:"required,gt=0,lte=10000"`        // 金额必须大于0且不超过10000元
	PaymentMethod string  `json:"payment_method" binding:"required,oneof=wechat alipay"` // 支付方式限制
}

// UserResponse 用户响应数据
type UserResponse struct {
	ID               int       `json:"id"`
	OpenID           string    `json:"openid"`
	Nickname         string    `json:"nickname"`
	AvatarURL        string    `json:"avatar_url"`
	Phone            string    `json:"phone"`
	Balance          float64   `json:"balance"`
	OrderCount       int       `json:"order_count"`
	TotalConsumption float64   `json:"total_consumption"`
	LastLoginAt      *time.Time `json:"last_login_at"`
	CreatedAt        time.Time `json:"created_at"`
}

// ToResponse 转换为响应数据
func (u *User) ToResponse() *UserResponse {
	response := &UserResponse{
		ID:        u.ID,
		OpenID:    u.OpenID,
		Balance:   u.Balance,
		CreatedAt: u.CreatedAt,
	}

	// 处理可能为nil的指针字段
	if u.Nickname != nil {
		response.Nickname = *u.Nickname
	}
	if u.AvatarURL != nil {
		response.AvatarURL = *u.AvatarURL
	}
	if u.Phone != nil {
		response.Phone = *u.Phone
	}

	return response
}

// BalanceRecord 余额变动记录
type BalanceRecord struct {
	ID          int       `json:"id" db:"id"`
	UserID      int       `json:"user_id" db:"user_id"`
	Type        string    `json:"type" db:"type"` // recharge, consume, refund
	Amount      float64   `json:"amount" db:"amount"`
	Balance     float64   `json:"balance" db:"balance"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
}

// UserWithStats 包含统计数据的用户信息
type UserWithStats struct {
	User
	OrderCount       int     `db:"order_count" json:"order_count"`
	TotalConsumption float64 `db:"total_consumption" json:"total_consumption"`
}

// ToResponseWithStats 转换为包含统计数据的响应格式
func (u *UserWithStats) ToResponseWithStats() *UserResponse {
	response := u.User.ToResponse()
	response.OrderCount = u.OrderCount
	response.TotalConsumption = u.TotalConsumption
	return response
}
