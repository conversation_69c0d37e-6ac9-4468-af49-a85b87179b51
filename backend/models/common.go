package models

import (
	"time"
)

// APIResponse 通用API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int `json:"page" form:"page" binding:"min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	Total      int64       `json:"total"`
	TotalPages int         `json:"total_pages"`
	Data       interface{} `json:"data"`
}

// TimeRange 时间范围
type TimeRange struct {
	StartTime time.Time `json:"start_time" form:"start_time"`
	EndTime   time.Time `json:"end_time" form:"end_time"`
}

// DashboardData 仪表盘数据
type DashboardData struct {
	TodayIncome      float64              `json:"today_income"`
	TotalRooms       int                  `json:"total_rooms"`
	OccupiedRooms    int                  `json:"occupied_rooms"`
	AvailableRooms   int                  `json:"available_rooms"`
	TodayOrders      int                  `json:"today_orders"`
	OnlineDevices    int                  `json:"online_devices"`
	OfflineDevices   int                  `json:"offline_devices"`
	RoomStatusChart  []RoomStatusData     `json:"room_status_chart"`
	IncomeChart      []IncomeData         `json:"income_chart"`
	OrderTrendChart  []OrderTrendData     `json:"order_trend_chart"`
}

// RoomStatusData 房间状态数据
type RoomStatusData struct {
	Status string `json:"status"`
	Count  int    `json:"count"`
}

// IncomeData 收入数据
type IncomeData struct {
	Date   string  `json:"date"`
	Income float64 `json:"income"`
}

// OrderTrendData 订单趋势数据
type OrderTrendData struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

// FinanceReport 财务报表
type FinanceReport struct {
	Period           string               `json:"period"`
	TotalIncome      float64              `json:"total_income"`
	TotalOrders      int                  `json:"total_orders"`
	AverageOrderValue float64             `json:"average_order_value"`
	PlatformIncome   []PlatformIncomeData `json:"platform_income"`
	RoomIncome       []RoomIncomeData     `json:"room_income"`
	DailyIncome      []IncomeData         `json:"daily_income"`
}

// PlatformIncomeData 平台收入数据
type PlatformIncomeData struct {
	Platform string  `json:"platform"`
	Income   float64 `json:"income"`
	Orders   int     `json:"orders"`
}

// RoomIncomeData 房间收入数据
type RoomIncomeData struct {
	RoomNumber string  `json:"room_number"`
	RoomName   string  `json:"room_name"`
	Income     float64 `json:"income"`
	Orders     int     `json:"orders"`
	Usage      float64 `json:"usage"` // 使用率
}

// UserStatistics 用户统计
type UserStatistics struct {
	TotalUsers       int                `json:"total_users"`
	NewUsersToday    int                `json:"new_users_today"`
	ActiveUsers      int                `json:"active_users"`
	UserGrowthChart  []UserGrowthData   `json:"user_growth_chart"`
	UserActivityChart []UserActivityData `json:"user_activity_chart"`
}

// UserGrowthData 用户增长数据
type UserGrowthData struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

// UserActivityData 用户活动数据
type UserActivityData struct {
	Date   string `json:"date"`
	Orders int    `json:"orders"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	DefaultPricingRuleID int    `json:"default_pricing_rule_id"`
	HeartbeatTimeout     int    `json:"heartbeat_timeout"`
	OrderTimeout         int    `json:"order_timeout"`
	BusinessHours        string `json:"business_hours"`
	ContactPhone         string `json:"contact_phone"`
	NotificationEnabled  bool   `json:"notification_enabled"`
}

// NotificationMessage 通知消息
type NotificationMessage struct {
	Type    string      `json:"type"` // order, device, system
	Title   string      `json:"title"`
	Content string      `json:"content"`
	Data    interface{} `json:"data,omitempty"`
	Time    time.Time   `json:"time"`
}

// ErrorCode 错误码常量
const (
	CodeSuccess           = 200
	CodeBadRequest        = 400
	CodeUnauthorized      = 401
	CodeForbidden         = 403
	CodeNotFound          = 404
	CodeInternalError     = 500
	CodeDatabaseError     = 1001
	CodeValidationError   = 1002
	CodeBusinessError     = 1003
	CodeThirdPartyError   = 1004
)

// Success 成功响应
func Success(data interface{}) *APIResponse {
	return &APIResponse{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
	}
}

// Error 错误响应
func Error(code int, message string) *APIResponse {
	return &APIResponse{
		Code:    code,
		Message: message,
	}
}

// BusinessError 业务错误响应
func BusinessError(message string) *APIResponse {
	return &APIResponse{
		Code:    CodeBusinessError,
		Message: message,
	}
}
