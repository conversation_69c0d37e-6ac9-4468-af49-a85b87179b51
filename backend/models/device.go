package models

import (
	"time"
)

// Device 设备模型
type Device struct {
	ID            int        `json:"id" db:"id"`
	Type          string     `json:"type" db:"type"` // main_lock, room_lock, power, speaker
	RoomID        *int       `json:"room_id" db:"room_id"`
	MacAddress    string     `json:"mac_address" db:"mac_address"`
	Status        string     `json:"status" db:"status"` // online, offline, maintenance
	LastHeartbeat *time.Time `json:"last_heartbeat" db:"last_heartbeat"`
	InstalledAt   time.Time  `json:"installed_at" db:"installed_at"`
}

// DeviceCreateRequest 设备创建请求
type DeviceCreateRequest struct {
	Type       string `json:"type" binding:"required"`
	RoomID     *int   `json:"room_id"`
	MacAddress string `json:"mac_address" binding:"required"`
}

// DeviceUpdateRequest 设备更新请求
type DeviceUpdateRequest struct {
	Type       string `json:"type"`
	RoomID     *int   `json:"room_id"`
	MacAddress string `json:"mac_address"`
	Status     string `json:"status"`
}

// DeviceControlRequest 设备控制请求
type DeviceControlRequest struct {
	DeviceID int                    `json:"device_id" binding:"required"`
	Command  string                 `json:"command" binding:"required"`
	Params   map[string]interface{} `json:"params"`
}

// BatchDeviceControlRequest 批量设备控制请求
type BatchDeviceControlRequest struct {
	DeviceIDs []int                  `json:"device_ids" binding:"required"`
	Command   string                 `json:"command" binding:"required"`
	Params    map[string]interface{} `json:"params"`
}

// RoomDeviceControlRequest 房间设备控制请求
type RoomDeviceControlRequest struct {
	RoomID     int                    `json:"room_id" binding:"required"`
	DeviceType string                 `json:"device_type" binding:"required"`
	Command    string                 `json:"command" binding:"required"`
	Params     map[string]interface{} `json:"params"`
}

// AudioMessageRequest 音频消息请求
type AudioMessageRequest struct {
	RoomID   int    `json:"room_id" binding:"required"`
	Message  string `json:"message" binding:"required"`
	Type     string `json:"type"`     // warning, reminder, notice
	Volume   int    `json:"volume"`   // 音量 0-100
	Duration int    `json:"duration"` // 播放时长（秒）
}

// DeviceResponse 设备响应数据
type DeviceResponse struct {
	ID            int        `json:"id"`
	Type          string     `json:"type"`
	TypeName      string     `json:"type_name"`      // 设备类型中文名称
	RoomID        *int       `json:"room_id"`
	RoomNumber    string     `json:"room_number,omitempty"`
	RoomName      string     `json:"room_name,omitempty"`
	MacAddress    string     `json:"mac_address"`
	Status        string     `json:"status"`
	StatusName    string     `json:"status_name"`    // 状态中文名称
	LastHeartbeat *time.Time `json:"last_heartbeat"`
	IsOnline      bool       `json:"is_online"`
	InstalledAt   time.Time  `json:"installed_at"`
}

// DeviceGroupResponse 设备分组响应数据
type DeviceGroupResponse struct {
	Type        string            `json:"type"`
	TypeName    string            `json:"type_name"`
	Count       int               `json:"count"`
	OnlineCount int               `json:"online_count"`
	Devices     []*DeviceResponse `json:"devices"`
}

// DeviceListResponse 设备列表响应数据
type DeviceListResponse struct {
	Groups []*DeviceGroupResponse `json:"groups"`
	Stats  *DeviceStats          `json:"stats"`
}

// DeviceStats 设备统计数据
type DeviceStats struct {
	Total   int `json:"total"`
	Online  int `json:"online"`
	Offline int `json:"offline"`
	Fault   int `json:"fault"`
}

// DeviceType 设备类型常量
const (
	// 门锁设备
	DeviceTypeMainLock = "main_lock"   // 店铺主门禁锁
	DeviceTypeRoomLock = "room_lock"   // 房间门锁

	// 电源控制设备
	DeviceTypePower = "power"          // 房间电源控制

	// 音响设备
	DeviceTypeSpeaker = "speaker"      // 房间音响/喇叭
)

// DeviceStatus 设备状态常量
const (
	DeviceStatusOnline      = "online"
	DeviceStatusOffline     = "offline"
	DeviceStatusMaintenance = "maintenance"
)

// DeviceCommand 设备控制命令常量
const (
	// 门锁控制命令
	CommandOpenLock   = "open_lock"
	CommandCloseLock  = "close_lock"
	CommandUnlock     = "unlock"       // 开锁
	CommandLock       = "lock"         // 锁门

	// 电源控制命令
	CommandPowerOn    = "power_on"
	CommandPowerOff   = "power_off"

	// 音响控制命令
	CommandPlayAudio  = "play_audio"   // 播放音频
	CommandStopAudio  = "stop_audio"   // 停止播放
	CommandSetVolume  = "set_volume"   // 设置音量

	// 消息发送命令
	CommandSendMessage = "send_message" // 发送消息
	CommandWarning     = "warning"      // 警告消息
	CommandReminder    = "reminder"     // 提醒消息
	CommandNotice      = "notice"       // 通知消息
)

// ToResponse 转换为响应数据
func (d *Device) ToResponse() *DeviceResponse {
	response := &DeviceResponse{
		ID:            d.ID,
		Type:          d.Type,
		TypeName:      GetDeviceTypeName(d.Type),
		RoomID:        d.RoomID,
		MacAddress:    d.MacAddress,
		Status:        d.Status,
		StatusName:    GetDeviceStatusName(d.Status),
		LastHeartbeat: d.LastHeartbeat,
		InstalledAt:   d.InstalledAt,
	}

	// 判断设备是否在线（3分钟内有心跳）
	if d.LastHeartbeat != nil {
		response.IsOnline = time.Since(*d.LastHeartbeat) < 3*time.Minute
	} else {
		response.IsOnline = false
	}

	return response
}

// GetDeviceTypeName 获取设备类型中文名称
func GetDeviceTypeName(deviceType string) string {
	switch deviceType {
	case DeviceTypeMainLock:
		return "店铺主门禁锁"
	case DeviceTypeRoomLock:
		return "房间门锁"
	case DeviceTypePower:
		return "房间电源控制"
	case DeviceTypeSpeaker:
		return "音响设备"
	default:
		return "未知设备"
	}
}

// GetDeviceStatusName 获取设备状态中文名称
func GetDeviceStatusName(status string) string {
	switch status {
	case DeviceStatusOnline:
		return "在线"
	case DeviceStatusOffline:
		return "离线"
	case DeviceStatusMaintenance:
		return "维护中"
	default:
		return "未知状态"
	}
}

// MQTTMessage MQTT消息结构
type MQTTMessage struct {
	Command   string                 `json:"cmd,omitempty"`
	Status    string                 `json:"status,omitempty"`
	Heartbeat bool                   `json:"heartbeat,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
	Params    map[string]interface{} `json:"params,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Battery   int                    `json:"battery,omitempty"`
	Signal    int                    `json:"signal,omitempty"`
	Version   string                 `json:"version,omitempty"`
}

// MQTTError MQTT错误响应
type MQTTError struct {
	Code      int       `json:"code"`
	Message   string    `json:"message"`
	Details   string    `json:"details,omitempty"`
	Timestamp time.Time `json:"timestamp"`
	RequestID string    `json:"request_id,omitempty"`
}

// MQTTConnectionStatus MQTT连接状态响应
type MQTTConnectionStatus struct {
	Status         bool                   `json:"status"`
	MQTTConnected  bool                   `json:"mqtt_connected"`
	Broker         string                 `json:"broker"`
	ClientID       string                 `json:"client_id"`
	Topics         map[string]string      `json:"topics"`
	ConnectionInfo map[string]interface{} `json:"connection_info"`
}

// MQTTControlResponse MQTT控制响应
type MQTTControlResponse struct {
	Message   string `json:"message"`
	Action    string `json:"action"`
	RoomID    int    `json:"room_id,omitempty"`
	DeviceID  string `json:"device_id,omitempty"`
	Timestamp string `json:"timestamp"`
}

// MQTTHeartbeatResponse MQTT心跳响应
type MQTTHeartbeatResponse struct {
	Message    string `json:"message"`
	MacAddress string `json:"mac_address"`
	Timestamp  string `json:"timestamp"`
	Status     string `json:"status"`
}
