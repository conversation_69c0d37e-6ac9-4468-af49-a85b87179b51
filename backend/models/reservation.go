package models

import (
	"time"
)

// Reservation 预约模型
type Reservation struct {
	ID        int       `json:"id" db:"id"`
	UserID    int       `json:"user_id" db:"user_id"`
	RoomID    int       `json:"room_id" db:"room_id"`
	StartTime time.Time `json:"start_time" db:"start_time"`
	EndTime   time.Time `json:"end_time" db:"end_time"`
	Status    string    `json:"status" db:"status"` // confirmed, cancelled, completed
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// ReservationCreateRequest 预约创建请求
type ReservationCreateRequest struct {
	RoomID    int       `json:"room_id" binding:"required"`
	StartTime time.Time `json:"start_time" binding:"required"`
	EndTime   time.Time `json:"end_time" binding:"required"`
}

// ReservationUpdateRequest 预约更新请求
type ReservationUpdateRequest struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Status    string    `json:"status"`
}

// ReservationResponse 预约响应数据
type ReservationResponse struct {
	ID        int           `json:"id"`
	User      *UserResponse `json:"user,omitempty"`
	Room      *RoomResponse `json:"room,omitempty"`
	StartTime time.Time     `json:"start_time"`
	EndTime   time.Time     `json:"end_time"`
	Status    string        `json:"status"`
	Duration  int           `json:"duration"` // 预约时长（分钟）
	CreatedAt time.Time     `json:"created_at"`
}

// ReservationStatus 预约状态常量
const (
	ReservationStatusConfirmed = "confirmed"
	ReservationStatusCancelled = "cancelled"
	ReservationStatusCompleted = "completed"
)

// ToResponse 转换为响应数据
func (r *Reservation) ToResponse() *ReservationResponse {
	duration := r.EndTime.Sub(r.StartTime)
	return &ReservationResponse{
		ID:        r.ID,
		StartTime: r.StartTime,
		EndTime:   r.EndTime,
		Status:    r.Status,
		Duration:  int(duration.Minutes()),
		CreatedAt: r.CreatedAt,
	}
}

// Promotion 优惠活动模型
type Promotion struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	Discount    float64   `json:"discount" db:"discount"` // 0.00 - 1.00
	StartTime   time.Time `json:"start_time" db:"start_time"`
	EndTime     time.Time `json:"end_time" db:"end_time"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// PromotionCreateRequest 优惠活动创建请求
type PromotionCreateRequest struct {
	Name        string    `json:"name" binding:"required"`
	Description string    `json:"description"`
	Discount    float64   `json:"discount" binding:"required,gte=0,lte=1"`
	StartTime   time.Time `json:"start_time" binding:"required"`
	EndTime     time.Time `json:"end_time" binding:"required"`
}

// PromotionUpdateRequest 优惠活动更新请求
type PromotionUpdateRequest struct {
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Discount    float64   `json:"discount" binding:"gte=0,lte=1"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	IsActive    bool      `json:"is_active"`
}

// PromotionResponse 优惠活动响应数据
type PromotionResponse struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Discount    float64   `json:"discount"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	IsActive    bool      `json:"is_active"`
	IsValid     bool      `json:"is_valid"` // 当前是否有效
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应数据
func (p *Promotion) ToResponse() *PromotionResponse {
	now := time.Now()
	isValid := p.IsActive && now.After(p.StartTime) && now.Before(p.EndTime)
	
	return &PromotionResponse{
		ID:          p.ID,
		Name:        p.Name,
		Description: p.Description,
		Discount:    p.Discount,
		StartTime:   p.StartTime,
		EndTime:     p.EndTime,
		IsActive:    p.IsActive,
		IsValid:     isValid,
		CreatedAt:   p.CreatedAt,
		UpdatedAt:   p.UpdatedAt,
	}
}
