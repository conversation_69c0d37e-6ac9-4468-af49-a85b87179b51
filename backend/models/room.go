package models

import (
	"time"
)

// Room 房间模型
type Room struct {
	ID            int       `json:"id" db:"id"`
	RoomNumber    string    `json:"room_number" db:"room_number"`
	Name          string    `json:"name" db:"name"`
	Description   string    `json:"description" db:"description"`
	Status        string    `json:"status" db:"status"` // available, occupied, maintenance
	PricingRuleID int       `json:"pricing_rule_id" db:"pricing_rule_id"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// RoomCreateRequest 房间创建请求
type RoomCreateRequest struct {
	RoomNumber    string `json:"room_number" binding:"required,min=1,max=50"`     // 房间号长度限制
	Name          string `json:"name" binding:"required,min=1,max=100"`           // 房间名称长度限制
	Description   string `json:"description" binding:"max=500"`                   // 描述长度限制
	PricingRuleID int    `json:"pricing_rule_id" binding:"required,gt=0"`         // 计费规则ID必须大于0
}

// RoomUpdateRequest 房间更新请求
type RoomUpdateRequest struct {
	Name          string `json:"name"`
	Description   string `json:"description"`
	Status        string `json:"status"`
	PricingRuleID int    `json:"pricing_rule_id"`
}

// RoomResponse 房间响应数据
type RoomResponse struct {
	ID            int          `json:"id"`
	RoomNumber    string       `json:"room_number"`
	Name          string       `json:"name"`
	Description   string       `json:"description"`
	Status        string       `json:"status"`
	PricingRule   *PricingRule `json:"pricing_rule,omitempty"`
	Devices       []Device     `json:"devices,omitempty"`
	CreatedAt     time.Time    `json:"created_at"`
	UpdatedAt     time.Time    `json:"updated_at"`
}

// PricingRule 计费规则模型
type PricingRule struct {
	ID             int        `json:"id" db:"id"`
	Name           string     `json:"name" db:"name"`
	PricePerHour   float64    `json:"price_per_hour" db:"price_per_hour"`
	OvernightPrice *float64   `json:"overnight_price" db:"overnight_price"`
	StartTime      *string    `json:"start_time" db:"start_time"`
	EndTime        *string    `json:"end_time" db:"end_time"`
	IsWeekend      bool       `json:"is_weekend" db:"is_weekend"`
	IsHoliday      bool       `json:"is_holiday" db:"is_holiday"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`
}

// PricingRuleCreateRequest 计费规则创建请求
type PricingRuleCreateRequest struct {
	Name           string  `json:"name" binding:"required,min=1,max=50"`           // 规则名称长度限制
	PricePerHour   float64 `json:"price_per_hour" binding:"required,gt=0,lte=500"` // 每小时价格限制
	OvernightPrice float64 `json:"overnight_price" binding:"gte=0,lte=1000"`       // 包夜价格限制
	StartTime      string  `json:"start_time"`
	EndTime        string  `json:"end_time"`
	IsWeekend      bool    `json:"is_weekend"`
	IsHoliday      bool    `json:"is_holiday"`
}

// PricingRuleUpdateRequest 计费规则更新请求
type PricingRuleUpdateRequest struct {
	Name           string  `json:"name"`
	PricePerHour   float64 `json:"price_per_hour" binding:"gt=0"`
	OvernightPrice float64 `json:"overnight_price"`
	StartTime      string  `json:"start_time"`
	EndTime        string  `json:"end_time"`
	IsWeekend      bool    `json:"is_weekend"`
	IsHoliday      bool    `json:"is_holiday"`
}

// RoomStatus 房间状态常量
const (
	RoomStatusAvailable   = "available"
	RoomStatusOccupied    = "occupied"
	RoomStatusMaintenance = "maintenance"
)

// ToResponse 转换为响应数据
func (r *Room) ToResponse() *RoomResponse {
	return &RoomResponse{
		ID:          r.ID,
		RoomNumber:  r.RoomNumber,
		Name:        r.Name,
		Description: r.Description,
		Status:      r.Status,
		CreatedAt:   r.CreatedAt,
		UpdatedAt:   r.UpdatedAt,
	}
}
