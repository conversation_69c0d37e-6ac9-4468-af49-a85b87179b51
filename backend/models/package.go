package models

import (
	"encoding/json"
	"fmt"
	"time"
)

// Package 套餐模型
type Package struct {
	ID               int       `json:"id" db:"id"`
	Name             string    `json:"name" db:"name"`
	Type             string    `json:"type" db:"type"`
	DurationHours    *int      `json:"duration_hours" db:"duration_hours"`
	OriginalPrice    float64   `json:"original_price" db:"original_price"`
	SalePrice        float64   `json:"sale_price" db:"sale_price"`
	DiscountRate     float64   `json:"discount_rate" db:"discount_rate"`
	Description      string    `json:"description" db:"description"`
	Features         []string  `json:"features" db:"-"`
	FeaturesJSON     string    `json:"-" db:"features"`
	IsActive         bool      `json:"is_active" db:"is_active"`
	SortOrder        int       `json:"sort_order" db:"sort_order"`
	ValidDays        int       `json:"valid_days" db:"valid_days"`
	MinRechargeHours *int      `json:"min_recharge_hours" db:"min_recharge_hours"`
	MaxRechargeHours *int      `json:"max_recharge_hours" db:"max_recharge_hours"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
	
	// 统计字段
	SalesCount int     `json:"sales_count,omitempty" db:"sales_count"`
	Revenue    float64 `json:"revenue,omitempty" db:"revenue"`
}

// UserPackage 用户套餐模型
type UserPackage struct {
	ID              int       `json:"id" db:"id"`
	UserID          int       `json:"user_id" db:"user_id"`
	PackageID       int       `json:"package_id" db:"package_id"`
	OrderID         *int      `json:"order_id" db:"order_id"`
	PlatformOrderID *int      `json:"platform_order_id" db:"platform_order_id"`
	TotalHours      float64   `json:"total_hours" db:"total_hours"`
	UsedHours       float64   `json:"used_hours" db:"used_hours"`
	RemainingHours  float64   `json:"remaining_hours" db:"remaining_hours"`
	PurchasePrice   float64   `json:"purchase_price" db:"purchase_price"`
	Status          string    `json:"status" db:"status"`
	ExpiresAt       time.Time `json:"expires_at" db:"expires_at"`
	ActivatedAt     *time.Time `json:"activated_at" db:"activated_at"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
	
	// 关联数据
	Package *Package `json:"package,omitempty" db:"-"`
	User    *User    `json:"user,omitempty" db:"-"`
}

// PlatformOrder 外卖平台订单模型（扩展现有的）
type PlatformOrderPackage struct {
	ID                 int                    `json:"id" db:"id"`
	PlatformType       string                 `json:"platform_type" db:"platform_type"`
	PlatformOrderID    string                 `json:"platform_order_id" db:"platform_order_id"`
	VerificationCode   string                 `json:"verification_code" db:"verification_code"`
	PackageID          *int                   `json:"package_id" db:"package_id"`
	UserID             *int                   `json:"user_id" db:"user_id"`
	OriginalPrice      float64                `json:"original_price" db:"original_price"`
	PlatformCommission float64                `json:"platform_commission" db:"platform_commission"`
	ActualIncome       float64                `json:"actual_income" db:"actual_income"`
	Status             string                 `json:"status" db:"verification_status"`
	VerifiedAt         *time.Time             `json:"verified_at" db:"verified_at"`
	ExpiresAt          *time.Time             `json:"expires_at" db:"expires_at"`
	PlatformData       map[string]interface{} `json:"platform_data" db:"-"`
	PlatformDataJSON   string                 `json:"-" db:"platform_data"`
	CreatedAt          time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt          time.Time              `json:"updated_at" db:"updated_at"`
	
	// 关联数据
	Package *Package `json:"package,omitempty" db:"-"`
	User    *User    `json:"user,omitempty" db:"-"`
}

// PackageUsageLog 套餐使用记录模型
type PackageUsageLog struct {
	ID              int       `json:"id" db:"id"`
	UserPackageID   int       `json:"user_package_id" db:"user_package_id"`
	OrderID         int       `json:"order_id" db:"order_id"`
	HoursUsed       float64   `json:"hours_used" db:"hours_used"`
	HoursBefore     float64   `json:"hours_before" db:"hours_before"`
	HoursAfter      float64   `json:"hours_after" db:"hours_after"`
	RoomID          int       `json:"room_id" db:"room_id"`
	StartedAt       time.Time `json:"started_at" db:"started_at"`
	EndedAt         *time.Time `json:"ended_at" db:"ended_at"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	
	// 关联数据
	Room  *Room  `json:"room,omitempty" db:"-"`
	Order *Order `json:"order,omitempty" db:"-"`
}

// 请求模型

// PackageCreateRequest 创建套餐请求
type PackageCreateRequest struct {
	Name             string   `json:"name" binding:"required,max=100"`
	Type             string   `json:"type" binding:"required,oneof=fixed_duration flexible_recharge"`
	DurationHours    *int     `json:"duration_hours"`
	OriginalPrice    float64  `json:"original_price" binding:"required,min=0"`
	SalePrice        float64  `json:"sale_price" binding:"required,min=0"`
	Description      string   `json:"description" binding:"max=500"`
	Features         []string `json:"features"`
	ValidDays        int      `json:"valid_days" binding:"required,min=1"`
	MinRechargeHours *int     `json:"min_recharge_hours"`
	MaxRechargeHours *int     `json:"max_recharge_hours"`
	SortOrder        int      `json:"sort_order"`
}

// PackageUpdateRequest 更新套餐请求
type PackageUpdateRequest struct {
	Name             string   `json:"name" binding:"max=100"`
	Type             string   `json:"type" binding:"oneof=fixed_duration flexible_recharge"`
	DurationHours    *int     `json:"duration_hours"`
	OriginalPrice    float64  `json:"original_price" binding:"min=0"`
	SalePrice        float64  `json:"sale_price" binding:"min=0"`
	Description      string   `json:"description" binding:"max=500"`
	Features         []string `json:"features"`
	ValidDays        int      `json:"valid_days" binding:"min=1"`
	MinRechargeHours *int     `json:"min_recharge_hours"`
	MaxRechargeHours *int     `json:"max_recharge_hours"`
	SortOrder        int      `json:"sort_order"`
	IsActive         *bool    `json:"is_active"`
}

// PackagePurchaseRequest 购买套餐请求
type PackagePurchaseRequest struct {
	PaymentMethod string  `json:"payment_method" binding:"required,oneof=wechat alipay"`
	Hours         *int    `json:"hours"` // 仅灵活续费套餐需要
	ReturnURL     string  `json:"return_url"`
	NotifyURL     string  `json:"notify_url"`
}

// VerificationRequest 核销请求
type VerificationRequest struct {
	VerificationCode string `json:"verification_code" binding:"required"`
	UserID           int    `json:"user_id" binding:"required"`
}

// UsePackageRequest 使用套餐请求
type UsePackageRequest struct {
	RoomID          int     `json:"room_id" binding:"required"`
	EstimatedHours  float64 `json:"estimated_hours" binding:"required,min=0.5,max=24"`
}

// 响应模型

// PackageResponse 套餐响应
type PackageResponse struct {
	*Package
	IsPopular     bool `json:"is_popular,omitempty"`
	IsRecommended bool `json:"is_recommended,omitempty"`
}

// UserPackageResponse 用户套餐响应
type UserPackageResponse struct {
	*UserPackage
}

// PackageStatsResponse 套餐统计响应
type PackageStatsResponse struct {
	Total       int     `json:"total"`
	Active      int     `json:"active"`
	TotalSales  int     `json:"total_sales"`
	TotalRevenue float64 `json:"total_revenue"`
}

// UserPackageStatsResponse 用户套餐统计响应
type UserPackageStatsResponse struct {
	Active     int     `json:"active"`
	TotalHours float64 `json:"total_hours"`
	TotalValue float64 `json:"total_value"`
}

// PlatformStatsResponse 平台统计响应
type PlatformStatsResponse struct {
	TotalOrders      int     `json:"total_orders"`
	VerifiedOrders   int     `json:"verified_orders"`
	PendingOrders    int     `json:"pending_orders"`
	ExpiredOrders    int     `json:"expired_orders"`
	TotalRevenue     float64 `json:"total_revenue"`
	PlatformCommission float64 `json:"platform_commission"`
	ActualIncome     float64 `json:"actual_income"`
	VerificationRate float64 `json:"verification_rate"`
	PlatformBreakdown []PlatformBreakdown `json:"platform_breakdown"`
}

// PlatformBreakdown 平台分解统计
type PlatformBreakdown struct {
	PlatformType string  `json:"platform_type"`
	Orders       int     `json:"orders"`
	Revenue      float64 `json:"revenue"`
	Commission   float64 `json:"commission"`
	Income       float64 `json:"income"`
}

// 辅助方法

// BeforeSave 保存前处理
func (p *Package) BeforeSave() error {
	if p.Features != nil {
		featuresJSON, err := json.Marshal(p.Features)
		if err != nil {
			return err
		}
		p.FeaturesJSON = string(featuresJSON)
	}
	
	// 计算折扣率
	if p.OriginalPrice > 0 && p.SalePrice < p.OriginalPrice {
		p.DiscountRate = ((p.OriginalPrice - p.SalePrice) / p.OriginalPrice) * 100
	}
	
	return nil
}

// AfterLoad 加载后处理
func (p *Package) AfterLoad() error {
	if p.FeaturesJSON != "" {
		err := json.Unmarshal([]byte(p.FeaturesJSON), &p.Features)
		if err != nil {
			return err
		}
	}
	return nil
}

// BeforeSave 保存前处理
func (po *PlatformOrderPackage) BeforeSave() error {
	if po.PlatformData != nil {
		dataJSON, err := json.Marshal(po.PlatformData)
		if err != nil {
			return err
		}
		po.PlatformDataJSON = string(dataJSON)
	}
	return nil
}

// AfterLoad 加载后处理
func (po *PlatformOrderPackage) AfterLoad() error {
	if po.PlatformDataJSON != "" {
		err := json.Unmarshal([]byte(po.PlatformDataJSON), &po.PlatformData)
		if err != nil {
			return err
		}
	}
	return nil
}

// IsExpired 检查是否过期
func (up *UserPackage) IsExpired() bool {
	return time.Now().After(up.ExpiresAt)
}

// IsUsedUp 检查是否用完
func (up *UserPackage) IsUsedUp() bool {
	return up.RemainingHours <= 0
}

// CanUse 检查是否可以使用
func (up *UserPackage) CanUse() bool {
	return up.Status == "active" && !up.IsExpired() && !up.IsUsedUp()
}

// UseHours 使用时长
func (up *UserPackage) UseHours(hours float64) error {
	if !up.CanUse() {
		return fmt.Errorf("套餐不可用")
	}

	if hours > up.RemainingHours {
		return fmt.Errorf("剩余时长不足")
	}
	
	up.UsedHours += hours
	up.RemainingHours -= hours
	
	// 检查是否用完
	if up.RemainingHours <= 0 {
		up.Status = "used_up"
	}
	
	return nil
}
