package models

import "time"

// Expense 支出记录模型
type Expense struct {
	ID          int       `json:"id" gorm:"primaryKey"`
	Type        string    `json:"type" gorm:"not null"`        // 支出类型
	Amount      float64   `json:"amount" gorm:"not null"`      // 支出金额
	Date        time.Time `json:"date" gorm:"not null"`        // 支出日期
	Description string    `json:"description"`                 // 备注说明
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ExpenseCreateRequest 创建支出记录请求
type ExpenseCreateRequest struct {
	Type        string  `json:"type" binding:"required"`        // 支出类型
	Amount      float64 `json:"amount" binding:"required,gt=0"` // 支出金额
	Date        string  `json:"date" binding:"required"`        // 支出日期
	Description string  `json:"description"`                    // 备注说明
}

// FinanceReportData 财务报表数据
type FinanceReportData struct {
	Summary           *FinanceSummary         `json:"summary"`
	RevenueChart      []ChartData             `json:"revenueChart"`
	RevenueSource     []RevenueSourceData     `json:"revenueSource"`
	RoomRanking       []RoomRankingData       `json:"roomRanking"`
	ExpenseCategories []ExpenseCategoryData   `json:"expenseCategories"`
}

// FinanceChartsData 财务图表数据
type FinanceChartsData struct {
	ExpenseTrend   []ExpenseTrendData    `json:"expenseTrend"`
	ProfitAnalysis []ProfitAnalysisData  `json:"profitAnalysis"`
}

// FinanceSummary 财务概要数据
type FinanceSummary struct {
	TotalRevenue          float64 `json:"totalRevenue"`          // 总收入
	TotalOrders           int     `json:"totalOrders"`           // 总订单数
	AvgOrderValue         float64 `json:"avgOrderValue"`         // 客单价
	EstimatedProfit       float64 `json:"estimatedProfit"`       // 预估利润
	TotalExpenses         float64 `json:"totalExpenses"`         // 总支出
	NetProfit             float64 `json:"netProfit"`             // 净利润
	ProfitMargin          float64 `json:"profitMargin"`          // 利润率
	RevenueTrend          float64 `json:"revenueTrend"`          // 收入趋势
	OrdersTrend           float64 `json:"ordersTrend"`           // 订单趋势
	AvgOrderTrend         float64 `json:"avgOrderTrend"`         // 客单价趋势
	ProfitTrend           float64 `json:"profitTrend"`           // 利润趋势
	AvgUsageDuration      float64 `json:"avgUsageDuration"`      // 平均使用时长
	CustomerRetentionRate float64 `json:"customerRetentionRate"` // 客户复购率
	RoomUtilizationRate   float64 `json:"roomUtilizationRate"`   // 房间利用率
	RevenuePerSqm         float64 `json:"revenuePerSqm"`         // 每平米收入
	PeakHourRevenueRatio  float64 `json:"peakHourRevenueRatio"`  // 高峰时段收入占比
}

// RevenueData 收入数据
type RevenueData struct {
	Date    string  `json:"date"`
	Revenue float64 `json:"revenue"`
}

// ExpenseData 支出数据
type ExpenseData struct {
	Date   string  `json:"date"`
	Amount float64 `json:"amount"`
}

// ChartData 图表数据
type ChartData struct {
	Date    string  `json:"date"`
	Revenue float64 `json:"revenue"`
}

// RevenueSourceData 收入来源数据
type RevenueSourceData struct {
	Name   string  `json:"name"`
	Amount float64 `json:"amount"`
}

// RoomRankingData 房间排行数据
type RoomRankingData struct {
	RoomID      int     `json:"roomId"`
	RoomName    string  `json:"roomName"`
	Revenue     float64 `json:"revenue"`
	OrdersCount int     `json:"ordersCount"`
	UsageRate   float64 `json:"usageRate"`
	Trend       float64 `json:"trend"`
}

// ExpenseCategoryData 支出分类数据
type ExpenseCategoryData struct {
	Type   string  `json:"type"`
	Amount float64 `json:"amount"`
}

// ExpenseTrendData 支出趋势数据
type ExpenseTrendData struct {
	Date   string  `json:"date"`
	Amount float64 `json:"amount"`
}

// ProfitAnalysisData 利润分析数据
type ProfitAnalysisData struct {
	Period       string  `json:"period"`
	Revenue      float64 `json:"revenue"`
	Expense      float64 `json:"expense"`
	Profit       float64 `json:"profit"`
	ProfitMargin float64 `json:"profitMargin"`
}



// 支出类型常量
const (
	ExpenseTypeRent        = "rent"        // 房租费用
	ExpenseTypeUtilities   = "utilities"   // 水电费
	ExpenseTypeMaintenance = "maintenance" // 设备维护
	ExpenseTypeLabor       = "labor"       // 人工成本
	ExpenseTypeMarketing   = "marketing"   // 营销推广
	ExpenseTypeOther       = "other"       // 其他费用
)

// GetExpenseTypeName 获取支出类型名称
func GetExpenseTypeName(expenseType string) string {
	switch expenseType {
	case ExpenseTypeRent:
		return "房租费用"
	case ExpenseTypeUtilities:
		return "水电费"
	case ExpenseTypeMaintenance:
		return "设备维护"
	case ExpenseTypeLabor:
		return "人工成本"
	case ExpenseTypeMarketing:
		return "营销推广"
	case ExpenseTypeOther:
		return "其他费用"
	default:
		return "未知类型"
	}
}
