package models

import (
	"fmt"
	"time"
)

// Order 订单模型
type Order struct {
	ID               int        `json:"id" db:"id"`
	OrderNumber      string     `json:"order_number" db:"order_number"`
	UserID           *int       `json:"user_id" db:"user_id"`        // 允许为 NULL
	RoomID           *int       `json:"room_id" db:"room_id"`        // 允许为 NULL
	StartTime        time.Time  `json:"start_time" db:"start_time"`
	EndTime          *time.Time `json:"end_time" db:"end_time"`
	TotalAmount      float64    `json:"total_amount" db:"total_amount"`
	PaidAmount       float64    `json:"paid_amount" db:"paid_amount"`
	Status           string     `json:"status" db:"status"` // pending, paid, in_use, completed, cancelled
	PackageType      string     `json:"package_type" db:"package_type"` // hourly, package
	UserPackageID    *int       `json:"user_package_id" db:"user_package_id"`
	PackageHoursUsed float64    `json:"package_hours_used" db:"package_hours_used"`
	PaymentMethod    string     `json:"payment_method" db:"payment_method"` // wechat, alipay, package, platform_voucher
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at" db:"updated_at"`
}

// OrderCreateRequest 订单创建请求
type OrderCreateRequest struct {
	RoomID      int     `json:"room_id" binding:"required,gt=0"`                    // 房间ID必须大于0
	TotalAmount float64 `json:"total_amount" binding:"required,gt=0,lte=5000"`      // 金额必须大于0且不超过5000元
}

// OrderPaymentRequest 订单支付请求
type OrderPaymentRequest struct {
	PaymentMethod string  `json:"payment_method" binding:"required,oneof=wechat alipay balance"` // 支付方式限制
	Amount        float64 `json:"amount" binding:"required,gt=0,lte=5000"`                       // 金额必须大于0且不超过5000元
}

// OrderExtendRequest 订单续费请求
type OrderExtendRequest struct {
	ExtendHours int     `json:"extend_hours" binding:"required,gt=0,lte=24"`  // 续费小时数必须大于0且不超过24小时
	Amount      float64 `json:"amount" binding:"required,gt=0,lte=2000"`      // 金额必须大于0且不超过2000元
}

// OrderResponse 订单响应数据
type OrderResponse struct {
	ID          int           `json:"id"`
	OrderNumber string        `json:"order_number"`
	UserID      *int          `json:"user_id"`              // 允许为 NULL
	RoomID      *int          `json:"room_id"`              // 允许为 NULL
	User        *UserResponse `json:"user,omitempty"`
	Room        *RoomResponse `json:"room,omitempty"`
	StartTime   time.Time     `json:"start_time"`
	EndTime     *time.Time    `json:"end_time"`
	TotalAmount float64       `json:"total_amount"`
	PaidAmount  float64       `json:"paid_amount"`
	Status      string        `json:"status"`
	Duration    int           `json:"duration"` // 使用时长（分钟）
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// PlatformOrder 外卖平台订单模型
type PlatformOrder struct {
	ID                 int        `json:"id" db:"id"`
	PlatformType       string     `json:"platformType" db:"platform_type"` // meituan, eleme
	PlatformOrderID    string     `json:"platformOrderId" db:"platform_order_id"`
	RoomID             *int       `json:"roomId" db:"room_id"`
	UserID             *int       `json:"userId" db:"user_id"`
	RoomNumber         string     `json:"roomNumber" db:"-"` // 关联查询获取
	RoomStatus         string     `json:"roomStatus" db:"-"` // 关联查询获取
	CustomerName       *string    `json:"customerName" db:"customer_name"`
	CustomerPhone      *string    `json:"customerPhone" db:"customer_phone"`
	OriginalAmount     float64    `json:"originalAmount" db:"original_amount"`
	DiscountAmount     float64    `json:"discountAmount" db:"discount_amount"`
	PaidAmount         float64    `json:"paidAmount" db:"paid_amount"`
	OrderStatus        string     `json:"orderStatus" db:"order_status"`               // pending, paid, completed, cancelled
	VerificationStatus string     `json:"verificationStatus" db:"verification_status"` // pending, verified, refunded
	VerificationCode   *string    `json:"verificationCode" db:"verification_code"`
	VerifiedAt         *time.Time `json:"verifiedAt" db:"verified_at"`
	VerifiedBy         *string    `json:"verifiedBy" db:"verified_by"`
	VerificationMethod *string    `json:"verificationMethod" db:"verification_method"`
	OrderItems         *string    `json:"orderItems" db:"order_items"`
	CreatedAt          time.Time  `json:"createdAt" db:"created_at"`
	UpdatedAt          time.Time  `json:"updatedAt" db:"updated_at"`
}

// PlatformOrderCreateRequest 外卖平台订单创建请求
type PlatformOrderCreateRequest struct {
	PlatformType    string  `json:"platform_type" binding:"required"`
	PlatformOrderID string  `json:"platform_order_id" binding:"required"`
	RoomID          int     `json:"room_id" binding:"required"`
	OriginalAmount  float64 `json:"original_amount" binding:"required,gt=0"`
	DiscountAmount  float64 `json:"discount_amount"`
	PaidAmount      float64 `json:"paid_amount" binding:"required,gt=0"`
}

// PlatformOrderVerifyRequest 外卖平台订单核销请求
type PlatformOrderVerifyRequest struct {
	PlatformOrderID    string `json:"platformOrderId" binding:"required"`
	VerificationCode   string `json:"verificationCode"`
	PlatformType       string `json:"platformType"`
	RoomNumber         string `json:"roomNumber"`
	VerificationMethod string `json:"verificationMethod"`
}

// OrderStatus 订单状态常量
const (
	OrderStatusPending   = "pending"   // 待支付
	OrderStatusPaid      = "paid"      // 已支付
	OrderStatusInUse     = "in_use"    // 进行中
	OrderStatusCompleted = "completed" // 已完成
	OrderStatusCancelled = "cancelled" // 已取消
)

// PlatformType 平台类型常量
const (
	PlatformTypeMeituan = "meituan"
	PlatformTypeEleme   = "eleme"
)

// VerificationStatus 核销状态常量
const (
	VerificationStatusPending  = "pending"
	VerificationStatusVerified = "verified"
	VerificationStatusRefunded = "refunded"
)

// ToResponse 转换为响应数据
func (o *Order) ToResponse() *OrderResponse {
	response := &OrderResponse{
		ID:          o.ID,
		OrderNumber: o.OrderNumber,
		UserID:      o.UserID,
		RoomID:      o.RoomID,
		StartTime:   o.StartTime,
		EndTime:     o.EndTime,
		TotalAmount: o.TotalAmount,
		PaidAmount:  o.PaidAmount,
		Status:      o.Status,
		CreatedAt:   o.CreatedAt,
		UpdatedAt:   o.UpdatedAt,
	}

	// 计算使用时长
	if o.EndTime != nil {
		// 有明确结束时间的订单（已完成或已取消）
		duration := o.EndTime.Sub(o.StartTime)
		minutes := int(duration.Minutes())
		if minutes < 1 && duration.Seconds() > 0 {
			minutes = 1 // 至少显示1分钟
		}
		response.Duration = minutes
	} else if o.Status == OrderStatusPaid || o.Status == OrderStatusInUse {
		// 正在使用中，计算当前时长
		duration := time.Now().Sub(o.StartTime)
		minutes := int(duration.Minutes())
		if minutes < 1 && duration.Seconds() > 0 {
			minutes = 1 // 至少显示1分钟
		}
		response.Duration = minutes
	} else if o.Status == OrderStatusCancelled {
		// 已取消但没有结束时间的订单，使用更新时间作为结束时间
		duration := o.UpdatedAt.Sub(o.StartTime)
		minutes := int(duration.Minutes())
		if minutes < 1 && duration.Seconds() > 0 {
			minutes = 1 // 至少显示1分钟
		}
		response.Duration = minutes
	} else if o.Status == OrderStatusPending {
		// 待支付订单，计算从创建到现在的时长
		duration := time.Now().Sub(o.StartTime)
		minutes := int(duration.Minutes())
		if minutes < 1 && duration.Seconds() > 0 {
			minutes = 1 // 至少显示1分钟
		}
		response.Duration = minutes
	} else {
		// 其他状态，设置为0
		response.Duration = 0
	}

	return response
}

// 批量操作请求结构
type BatchVerifyRequest struct {
	OrderIds []string `json:"orderIds" binding:"required"`
}

type BatchDeleteRequest struct {
	OrderIds []int `json:"orderIds" binding:"required"`
}

// 统计数据结构
type PlatformOrderStats struct {
	Meituan  PlatformStats `json:"meituan"`
	Eleme    PlatformStats `json:"eleme"`
	Pending  PlatformStats `json:"pending"`
	Verified PlatformStats `json:"verified"`
}

type PlatformStats struct {
	Count  int     `json:"count"`
	Amount float64 `json:"amount"`
}

type VerificationStats struct {
	Today      TodayStats      `json:"today"`
	Pending    PendingStats    `json:"pending"`
	Efficiency EfficiencyStats `json:"efficiency"`
}

type TodayStats struct {
	Verified int     `json:"verified"`
	Trend    float64 `json:"trend"`
}

type PendingStats struct {
	Count  int     `json:"count"`
	Amount float64 `json:"amount"`
}

type EfficiencyStats struct {
	Rate    float64 `json:"rate"`
	AvgTime float64 `json:"avgTime"`
}

// 分析数据结构
type PlatformAnalytics struct {
	Summary             AnalyticsSummary            `json:"summary"`
	RevenueChart        []ChartData                 `json:"revenueChart"`
	PlatformComparison  []PlatformComparisonData    `json:"platformComparison"`
	TimeDistribution    []TimeDistributionData      `json:"timeDistribution"`
	PlatformPerformance []PlatformPerformanceData   `json:"platformPerformance"`
	PeakHours          []PeakHourData              `json:"peakHours"`
}

type AnalyticsSummary struct {
	TotalRevenue    float64 `json:"totalRevenue"`
	TotalOrders     int     `json:"totalOrders"`
	AvgOrderValue   float64 `json:"avgOrderValue"`
	ConversionRate  float64 `json:"conversionRate"`
	RevenueTrend    float64 `json:"revenueTrend"`
	OrdersTrend     float64 `json:"ordersTrend"`
	AvgOrderTrend   float64 `json:"avgOrderTrend"`
	ConversionTrend float64 `json:"conversionTrend"`
}

type PlatformComparisonData struct {
	Platform string  `json:"platform"`
	Revenue  float64 `json:"revenue"`
	Orders   int     `json:"orders"`
}

type TimeDistributionData struct {
	Hour    int     `json:"hour"`
	Orders  int     `json:"orders"`
	Revenue float64 `json:"revenue"`
}

type PlatformPerformanceData struct {
	Type           string  `json:"type"`
	Orders         int     `json:"orders"`
	Revenue        float64 `json:"revenue"`
	AvgOrderValue  float64 `json:"avgOrderValue"`
	MarketShare    float64 `json:"marketShare"`
	GrowthRate     float64 `json:"growthRate"`
}

type PeakHourData struct {
	Hour    string  `json:"hour"`
	Orders  int     `json:"orders"`
	Revenue float64 `json:"revenue"`
}

type RoomAnalysis struct {
	RoomNumber        int     `json:"roomNumber"`
	RoomName          string  `json:"roomName"`
	TotalOrders       int     `json:"totalOrders"`
	TotalRevenue      float64 `json:"totalRevenue"`
	AvgOrderValue     float64 `json:"avgOrderValue"`
	OrderFrequency    float64 `json:"orderFrequency"`
	PreferredPlatform string  `json:"preferredPlatform"`
	PeakHour          string  `json:"peakHour"`
}

// OrderStats 订单统计数据
type OrderStats struct {
	Total        int64   `json:"total"`         // 总订单数
	TotalAmount  float64 `json:"totalAmount"`   // 总金额
	TodayOrders  int64   `json:"todayOrders"`   // 今日订单数
	TodayAmount  float64 `json:"todayAmount"`   // 今日收入
}

// OrderListResponse 订单列表响应
type OrderListResponse struct {
	Page       int          `json:"page"`
	PageSize   int          `json:"page_size"`
	Total      int64        `json:"total"`
	TotalPages int          `json:"total_pages"`
	Data       interface{}  `json:"data"`
	Stats      *OrderStats  `json:"stats"`
}

// GenerateOrderNumber 生成订单号
// 格式：MJ + YYYYMMDD + 3位序号
func GenerateOrderNumber() string {
	now := time.Now()
	dateStr := now.Format("20060102")
	// 使用时间戳的后3位作为序号，确保唯一性
	sequence := now.UnixNano() % 1000
	return fmt.Sprintf("MJ%s%03d", dateStr, sequence)
}
