-- 数据库性能优化索引
-- 执行时间: 2025-07-28
-- 目标: 优化查询性能，特别是用户列表、订单查询、设备管理等高频操作

-- ==================== 用户相关优化 ====================

-- 用户列表查询优化（支持昵称和手机号搜索）
CREATE INDEX IF NOT EXISTS idx_users_nickname_phone ON users(nickname, phone);

-- 用户创建时间范围查询优化
CREATE INDEX IF NOT EXISTS idx_users_created_at_desc ON users(created_at DESC);

-- 用户状态和余额查询优化
CREATE INDEX IF NOT EXISTS idx_users_balance ON users(balance DESC);

-- ==================== 订单相关优化 ====================

-- 订单列表查询优化（用户+状态+时间）
CREATE INDEX IF NOT EXISTS idx_orders_user_status_time ON orders(user_id, status, created_at DESC);

-- 订单房间查询优化（房间+状态+时间）
CREATE INDEX IF NOT EXISTS idx_orders_room_status_time ON orders(room_id, status, created_at DESC);

-- 订单时间范围查询优化
CREATE INDEX IF NOT EXISTS idx_orders_start_end_time ON orders(start_time, end_time);

-- 订单金额统计优化
CREATE INDEX IF NOT EXISTS idx_orders_amount_time ON orders(total_amount, created_at);

-- 活跃订单查询优化（正在使用的订单）
CREATE INDEX IF NOT EXISTS idx_orders_active ON orders(status, start_time) WHERE status IN ('paid', 'in_use');

-- ==================== 房间相关优化 ====================

-- 房间状态和计费规则查询优化
CREATE INDEX IF NOT EXISTS idx_rooms_status_pricing ON rooms(status, pricing_rule_id);

-- 可用房间快速查询
CREATE INDEX IF NOT EXISTS idx_rooms_available ON rooms(status, created_at) WHERE status = 'available';

-- ==================== 设备相关优化 ====================

-- 设备房间和状态查询优化
CREATE INDEX IF NOT EXISTS idx_devices_room_status ON devices(room_id, status);

-- 设备类型和状态查询优化
CREATE INDEX IF NOT EXISTS idx_devices_type_status ON devices(type, status);

-- 设备心跳时间查询优化（用于检测离线设备）
CREATE INDEX IF NOT EXISTS idx_devices_heartbeat ON devices(last_heartbeat DESC) WHERE last_heartbeat IS NOT NULL;

-- 在线设备快速查询
CREATE INDEX IF NOT EXISTS idx_devices_online ON devices(status, last_heartbeat) WHERE status = 'online';

-- ==================== 余额记录优化 ====================

-- 用户余额记录查询优化（用户+类型+时间）
CREATE INDEX IF NOT EXISTS idx_balance_records_user_type_time ON balance_records(user_id, type, created_at DESC);

-- 余额记录时间范围查询
CREATE INDEX IF NOT EXISTS idx_balance_records_time_range ON balance_records(created_at DESC, type);

-- ==================== 统计查询优化 ====================

-- 今日新用户统计
CREATE INDEX IF NOT EXISTS idx_users_today ON users(DATE(created_at)) WHERE DATE(created_at) = DATE('now');

-- 今日订单统计
CREATE INDEX IF NOT EXISTS idx_orders_today ON orders(DATE(created_at), status) WHERE DATE(created_at) = DATE('now');

-- 收入统计优化
CREATE INDEX IF NOT EXISTS idx_orders_income ON orders(status, total_amount, created_at) WHERE status IN ('paid', 'completed');

-- ==================== 复合查询优化 ====================

-- 用户订单统计查询优化（避免N+1查询）
CREATE INDEX IF NOT EXISTS idx_orders_user_stats ON orders(user_id, status, total_amount);

-- 房间使用率统计
CREATE INDEX IF NOT EXISTS idx_orders_room_usage ON orders(room_id, start_time, end_time, status);

-- 设备使用统计
CREATE INDEX IF NOT EXISTS idx_devices_usage_stats ON devices(room_id, type, status, last_heartbeat);

-- ==================== 分页查询优化 ====================

-- 用户列表分页优化
CREATE INDEX IF NOT EXISTS idx_users_pagination ON users(id DESC, created_at DESC);

-- 订单列表分页优化
CREATE INDEX IF NOT EXISTS idx_orders_pagination ON orders(id DESC, created_at DESC);

-- 设备列表分页优化
CREATE INDEX IF NOT EXISTS idx_devices_pagination ON devices(id DESC, installed_at DESC);

-- ==================== 外键关联查询优化 ====================

-- 订单关联用户查询优化
CREATE INDEX IF NOT EXISTS idx_orders_user_join ON orders(user_id, id, created_at);

-- 订单关联房间查询优化
CREATE INDEX IF NOT EXISTS idx_orders_room_join ON orders(room_id, id, created_at);

-- 设备关联房间查询优化
CREATE INDEX IF NOT EXISTS idx_devices_room_join ON devices(room_id, id, status);

-- ==================== 搜索优化 ====================

-- 用户昵称模糊搜索优化（如果支持全文搜索）
-- 注意：SQLite的LIKE查询在以%开头时无法使用索引，但可以优化前缀搜索
CREATE INDEX IF NOT EXISTS idx_users_nickname_prefix ON users(nickname COLLATE NOCASE);

-- 用户手机号搜索优化
CREATE INDEX IF NOT EXISTS idx_users_phone_search ON users(phone) WHERE phone IS NOT NULL AND phone != '';

-- 房间号搜索优化
CREATE INDEX IF NOT EXISTS idx_rooms_number_search ON rooms(room_number COLLATE NOCASE);

-- ==================== 时间范围查询优化 ====================

-- 按日期分组的统计查询
CREATE INDEX IF NOT EXISTS idx_orders_date_stats ON orders(DATE(created_at), status, total_amount);

-- 按月份分组的统计查询
CREATE INDEX IF NOT EXISTS idx_orders_month_stats ON orders(strftime('%Y-%m', created_at), status, total_amount);

-- 用户注册趋势分析
CREATE INDEX IF NOT EXISTS idx_users_date_trend ON users(DATE(created_at));

-- ==================== 性能监控索引 ====================

-- 慢查询监控（订单相关）
CREATE INDEX IF NOT EXISTS idx_orders_performance ON orders(created_at, user_id, room_id, status);

-- 用户活跃度监控
CREATE INDEX IF NOT EXISTS idx_users_activity ON users(created_at, id) WHERE created_at >= datetime('now', '-30 days');

-- 设备健康监控
CREATE INDEX IF NOT EXISTS idx_devices_health ON devices(last_heartbeat, status, type) WHERE last_heartbeat >= datetime('now', '-1 hour');

-- ==================== 清理和维护 ====================

-- 定期清理过期数据的索引
CREATE INDEX IF NOT EXISTS idx_balance_records_cleanup ON balance_records(created_at) WHERE created_at < datetime('now', '-1 year');

-- 订单归档索引
CREATE INDEX IF NOT EXISTS idx_orders_archive ON orders(created_at, status) WHERE created_at < datetime('now', '-6 months');

-- ==================== 分析和统计 ====================

-- 执行ANALYZE命令更新统计信息
ANALYZE;

-- 查看索引使用情况的视图（仅用于监控）
-- 注意：这些查询可以用于监控索引效果，但不会创建永久对象

-- 显示表的索引信息
-- SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='users' ORDER BY name;
-- SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='orders' ORDER BY name;
-- SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='rooms' ORDER BY name;
-- SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='devices' ORDER BY name;

-- 检查索引大小和效果
-- PRAGMA index_info('idx_users_nickname_phone');
-- PRAGMA index_list('users');

-- ==================== 优化说明 ====================

/*
索引优化策略说明：

1. 复合索引顺序：
   - 将选择性高的列放在前面
   - 将等值查询的列放在范围查询的列之前
   - 考虑ORDER BY子句的列顺序

2. 覆盖索引：
   - 某些索引包含查询所需的所有列，避免回表查询

3. 部分索引：
   - 使用WHERE条件创建部分索引，减少索引大小
   - 针对特定查询模式优化

4. 索引维护：
   - 定期执行ANALYZE更新统计信息
   - 监控索引使用情况
   - 删除未使用的索引

5. 查询优化建议：
   - 避免在WHERE子句中对索引列使用函数
   - 使用LIMIT减少返回的行数
   - 合理使用分页查询
   - 避免SELECT *，只查询需要的列

6. 监控指标：
   - 查询执行时间
   - 索引命中率
   - 数据库大小增长
   - 并发查询性能
*/
