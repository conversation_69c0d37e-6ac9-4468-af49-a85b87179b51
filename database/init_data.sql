-- 自助麻将室系统初始化数据

-- 插入初始计费规则
INSERT OR IGNORE INTO pricing_rules (id, name, price_per_hour, is_weekend, is_holiday) VALUES
(1, '工作日每小时', 20.00, FALSE, FALSE),
(2, '周末每小时', 25.00, TRUE, FALSE),
(3, '节假日每小时', 30.00, FALSE, TRUE);

-- 插入包夜计费规则
INSERT OR IGNORE INTO pricing_rules (id, name, overnight_price, start_time, end_time) VALUES
(4, '包夜优惠', 150.00, '22:00', '06:00');

-- 插入示例房间数据
INSERT OR IGNORE INTO rooms (id, room_number, name, description, pricing_rule_id) VALUES
(1, 'A01', '牡丹厅', '豪华大包间，配备高级麻将机，可容纳4-6人', 1),
(2, 'A02', '梅花厅', '标准包间，舒适环境，配备自动麻将机', 1),
(3, 'A03', '兰花厅', '中等包间，性价比高，适合朋友聚会', 1),
(4, 'B01', '竹韵厅', '雅致包间，环境优美，配备茶具', 1),
(5, 'B02', '荷香厅', '温馨包间，适合家庭聚会', 1),
(6, 'C01', 'VIP至尊厅', '豪华VIP包间，配备按摩椅和高端设施', 2);

-- 插入示例设备数据
INSERT OR IGNORE INTO devices (id, type, room_id, mac_address, status) VALUES
(1, 'lock', 1, '00:11:22:33:44:01', 'online'),
(2, 'socket', 1, '00:11:22:33:44:02', 'online'),
(3, 'sensor', 1, '00:11:22:33:44:03', 'online'),
(4, 'lock', 2, '00:11:22:33:44:04', 'online'),
(5, 'socket', 2, '00:11:22:33:44:05', 'online'),
(6, 'sensor', 2, '00:11:22:33:44:06', 'online'),
(7, 'lock', 3, '00:11:22:33:44:07', 'online'),
(8, 'socket', 3, '00:11:22:33:44:08', 'online'),
(9, 'sensor', 3, '00:11:22:33:44:09', 'online'),
(10, 'lock', 4, '00:11:22:33:44:10', 'online'),
(11, 'socket', 4, '00:11:22:33:44:11', 'online'),
(12, 'sensor', 4, '00:11:22:33:44:12', 'online'),
(13, 'lock', 5, '00:11:22:33:44:13', 'online'),
(14, 'socket', 5, '00:11:22:33:44:14', 'online'),
(15, 'sensor', 5, '00:11:22:33:44:15', 'online'),
(16, 'lock', 6, '00:11:22:33:44:16', 'online'),
(17, 'socket', 6, '00:11:22:33:44:17', 'online'),
(18, 'sensor', 6, '00:11:22:33:44:18', 'online');

-- 插入示例优惠活动数据
INSERT OR IGNORE INTO promotions (id, name, description, discount, start_time, end_time, is_active) VALUES
(1, '新用户首单立减', '新用户首次下单立减10元', 0.10, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1),
(2, '周末狂欢', '周末所有订单享受8折优惠', 0.20, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1),
(3, '包夜特惠', '晚上10点后包夜享受9折优惠', 0.10, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1);

-- 插入测试用户数据
INSERT OR IGNORE INTO users (id, openid, nickname, phone, balance) VALUES
(1, 'test_openid_001', '测试用户1', '13800138001', 100.00),
(2, 'test_openid_002', '测试用户2', '13800138002', 200.00),
(3, 'test_openid_003', '测试用户3', '13800138003', 50.00);

-- 插入测试订单数据
INSERT OR IGNORE INTO orders (id, user_id, room_id, start_time, end_time, total_amount, paid_amount, status) VALUES
(1, 1, 1, '2024-01-15 14:00:00', '2024-01-15 16:00:00', 40.00, 40.00, 'completed'),
(2, 2, 2, '2024-01-15 19:00:00', '2024-01-15 22:00:00', 60.00, 60.00, 'completed'),
(3, 3, 3, '2024-01-16 10:00:00', NULL, 20.00, 20.00, 'paid');

-- 插入测试预约数据
INSERT OR IGNORE INTO reservations (id, user_id, room_id, start_time, end_time, status) VALUES
(1, 1, 4, '2024-01-17 14:00:00', '2024-01-17 17:00:00', 'confirmed'),
(2, 2, 5, '2024-01-17 19:00:00', '2024-01-17 22:00:00', 'confirmed');

-- 插入测试支出数据
INSERT OR IGNORE INTO expenses (id, type, amount, date, description) VALUES
(1, 'rent', 8000.00, '2024-01-01', '1月份房租'),
(2, 'utilities', 1200.00, '2024-01-05', '水电费'),
(3, 'maintenance', 500.00, '2024-01-10', '麻将机维护费用'),
(4, 'labor', 3000.00, '2024-01-15', '员工工资'),
(5, 'marketing', 800.00, '2024-01-20', '广告推广费用'),
(6, 'other', 300.00, '2024-01-25', '办公用品采购');

-- 插入测试平台订单数据
INSERT OR IGNORE INTO platform_orders (id, platform_order_id, platform_type, room_id, user_id, customer_name, customer_phone, original_amount, discount_amount, paid_amount, order_status, verification_status, verification_code, order_items, created_at, updated_at) VALUES
(1, 'MT202507260001', 'meituan', 1, 1, '张三', '13800138001', 85.00, 5.00, 80.00, 'paid', 'pending', 'VF001', '{"items":[{"name":"可乐","quantity":2,"price":5.00},{"name":"薯片","quantity":1,"price":15.00},{"name":"炒饭","quantity":2,"price":25.00}]}', '2025-07-26 12:30:00', '2025-07-26 12:30:00'),
(2, 'EL202507260002', 'eleme', 2, 2, '李四', '13800138002', 120.00, 10.00, 110.00, 'paid', 'verified', 'VF002', '{"items":[{"name":"奶茶","quantity":3,"price":12.00},{"name":"汉堡","quantity":2,"price":28.00},{"name":"薯条","quantity":2,"price":8.00}]}', '2025-07-26 14:15:00', '2025-07-26 15:20:00'),
(3, 'MT202507260003', 'meituan', 3, 3, '王五', '13800138003', 65.00, 0.00, 65.00, 'paid', 'pending', 'VF003', '{"items":[{"name":"咖啡","quantity":2,"price":15.00},{"name":"蛋糕","quantity":1,"price":35.00}]}', '2025-07-26 16:45:00', '2025-07-26 16:45:00'),
(4, 'EL202507260004', 'eleme', 1, 1, '赵六', '13800138004', 95.00, 5.00, 90.00, 'paid', 'verified', 'VF004', '{"items":[{"name":"麻辣烫","quantity":1,"price":25.00},{"name":"饮料","quantity":4,"price":8.00},{"name":"小食","quantity":3,"price":12.00}]}', '2025-07-26 18:20:00', '2025-07-26 19:10:00'),
(5, 'MT202507260005', 'meituan', 4, 2, '孙七', '13800138005', 150.00, 20.00, 130.00, 'paid', 'pending', 'VF005', '{"items":[{"name":"火锅套餐","quantity":1,"price":120.00},{"name":"啤酒","quantity":6,"price":5.00}]}', '2025-07-26 19:30:00', '2025-07-26 19:30:00'),
(6, 'EL202507270001', 'eleme', 2, 3, '周八', '13800138006', 75.00, 0.00, 75.00, 'paid', 'pending', 'VF006', '{"items":[{"name":"粥","quantity":2,"price":12.00},{"name":"包子","quantity":6,"price":3.00},{"name":"豆浆","quantity":3,"price":5.00}]}', '2025-07-27 09:15:00', '2025-07-27 09:15:00'),
(7, 'MT202507270002', 'meituan', 5, 1, '吴九', '13800138007', 88.00, 8.00, 80.00, 'paid', 'verified', 'VF007', '{"items":[{"name":"面条","quantity":2,"price":18.00},{"name":"小菜","quantity":4,"price":6.00},{"name":"汤","quantity":2,"price":8.00}]}', '2025-07-27 11:40:00', '2025-07-27 12:25:00'),
(8, 'EL202507270003', 'eleme', 3, 2, '郑十', '13800138008', 110.00, 10.00, 100.00, 'paid', 'pending', 'VF008', '{"items":[{"name":"烧烤","quantity":1,"price":80.00},{"name":"啤酒","quantity":4,"price":6.00},{"name":"小食","quantity":2,"price":8.00}]}', '2025-07-27 20:10:00', '2025-07-27 20:10:00');
