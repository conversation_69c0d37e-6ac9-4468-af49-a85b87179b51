-- 数据库性能优化索引（修复版）
-- 执行时间: 2025-07-28
-- 目标: 优化查询性能，特别是用户列表、订单查询、设备管理等高频操作

-- ==================== 用户相关优化 ====================

-- 用户列表查询优化（支持昵称和手机号搜索）
CREATE INDEX IF NOT EXISTS idx_users_nickname_phone ON users(nickname, phone);

-- 用户创建时间范围查询优化
CREATE INDEX IF NOT EXISTS idx_users_created_at_desc ON users(created_at DESC);

-- 用户状态和余额查询优化
CREATE INDEX IF NOT EXISTS idx_users_balance ON users(balance DESC);

-- ==================== 订单相关优化 ====================

-- 订单列表查询优化（用户+状态+时间）
CREATE INDEX IF NOT EXISTS idx_orders_user_status_time ON orders(user_id, status, created_at DESC);

-- 订单房间查询优化（房间+状态+时间）
CREATE INDEX IF NOT EXISTS idx_orders_room_status_time ON orders(room_id, status, created_at DESC);

-- 订单时间范围查询优化
CREATE INDEX IF NOT EXISTS idx_orders_start_end_time ON orders(start_time, end_time);

-- 订单金额统计优化
CREATE INDEX IF NOT EXISTS idx_orders_amount_time ON orders(total_amount, created_at);

-- 活跃订单查询优化（正在使用的订单）
CREATE INDEX IF NOT EXISTS idx_orders_active_status ON orders(status, start_time);

-- ==================== 房间相关优化 ====================

-- 房间状态和计费规则查询优化
CREATE INDEX IF NOT EXISTS idx_rooms_status_pricing ON rooms(status, pricing_rule_id);

-- 可用房间快速查询
CREATE INDEX IF NOT EXISTS idx_rooms_available_status ON rooms(status, created_at);

-- ==================== 设备相关优化 ====================

-- 设备房间和状态查询优化
CREATE INDEX IF NOT EXISTS idx_devices_room_status ON devices(room_id, status);

-- 设备类型和状态查询优化
CREATE INDEX IF NOT EXISTS idx_devices_type_status ON devices(type, status);

-- 设备心跳时间查询优化（用于检测离线设备）
CREATE INDEX IF NOT EXISTS idx_devices_heartbeat_desc ON devices(last_heartbeat DESC);

-- 在线设备快速查询
CREATE INDEX IF NOT EXISTS idx_devices_online_status ON devices(status, last_heartbeat);

-- ==================== 余额记录优化 ====================

-- 用户余额记录查询优化（用户+类型+时间）
CREATE INDEX IF NOT EXISTS idx_balance_records_user_type_time ON balance_records(user_id, type, created_at DESC);

-- 余额记录时间范围查询
CREATE INDEX IF NOT EXISTS idx_balance_records_time_range ON balance_records(created_at DESC, type);

-- ==================== 复合查询优化 ====================

-- 用户订单统计查询优化（避免N+1查询）
CREATE INDEX IF NOT EXISTS idx_orders_user_stats ON orders(user_id, status, total_amount);

-- 房间使用率统计
CREATE INDEX IF NOT EXISTS idx_orders_room_usage ON orders(room_id, start_time, end_time, status);

-- 设备使用统计
CREATE INDEX IF NOT EXISTS idx_devices_usage_stats ON devices(room_id, type, status, last_heartbeat);

-- ==================== 分页查询优化 ====================

-- 用户列表分页优化
CREATE INDEX IF NOT EXISTS idx_users_pagination ON users(id DESC, created_at DESC);

-- 订单列表分页优化
CREATE INDEX IF NOT EXISTS idx_orders_pagination ON orders(id DESC, created_at DESC);

-- 设备列表分页优化
CREATE INDEX IF NOT EXISTS idx_devices_pagination ON devices(id DESC, installed_at DESC);

-- ==================== 外键关联查询优化 ====================

-- 订单关联用户查询优化
CREATE INDEX IF NOT EXISTS idx_orders_user_join ON orders(user_id, id, created_at);

-- 订单关联房间查询优化
CREATE INDEX IF NOT EXISTS idx_orders_room_join ON orders(room_id, id, created_at);

-- 设备关联房间查询优化
CREATE INDEX IF NOT EXISTS idx_devices_room_join ON devices(room_id, id, status);

-- ==================== 搜索优化 ====================

-- 用户昵称模糊搜索优化
CREATE INDEX IF NOT EXISTS idx_users_nickname_prefix ON users(nickname COLLATE NOCASE);

-- 用户手机号搜索优化
CREATE INDEX IF NOT EXISTS idx_users_phone_search ON users(phone);

-- 房间号搜索优化
CREATE INDEX IF NOT EXISTS idx_rooms_number_search ON rooms(room_number COLLATE NOCASE);

-- ==================== 时间范围查询优化 ====================

-- 订单按创建时间分组统计
CREATE INDEX IF NOT EXISTS idx_orders_date_stats ON orders(created_at, status, total_amount);

-- 用户注册趋势分析
CREATE INDEX IF NOT EXISTS idx_users_date_trend ON users(created_at);

-- ==================== 性能监控索引 ====================

-- 慢查询监控（订单相关）
CREATE INDEX IF NOT EXISTS idx_orders_performance ON orders(created_at, user_id, room_id, status);

-- 用户活跃度监控
CREATE INDEX IF NOT EXISTS idx_users_activity ON users(created_at, id);

-- 设备健康监控
CREATE INDEX IF NOT EXISTS idx_devices_health ON devices(last_heartbeat, status, type);

-- ==================== 特殊查询优化 ====================

-- 用户余额排序查询
CREATE INDEX IF NOT EXISTS idx_users_balance_sort ON users(balance DESC, created_at DESC);

-- 订单金额排序查询
CREATE INDEX IF NOT EXISTS idx_orders_amount_sort ON orders(total_amount DESC, created_at DESC);

-- 房间使用频率统计
CREATE INDEX IF NOT EXISTS idx_orders_room_frequency ON orders(room_id, created_at DESC);

-- 设备故障率统计
CREATE INDEX IF NOT EXISTS idx_devices_failure_rate ON devices(status, type, last_heartbeat);

-- ==================== 管理端查询优化 ====================

-- 管理端用户列表查询（包含统计信息）
CREATE INDEX IF NOT EXISTS idx_users_admin_list ON users(created_at DESC, id, nickname, phone);

-- 管理端订单列表查询
CREATE INDEX IF NOT EXISTS idx_orders_admin_list ON orders(created_at DESC, id, user_id, room_id, status);

-- 管理端设备列表查询
CREATE INDEX IF NOT EXISTS idx_devices_admin_list ON devices(installed_at DESC, id, type, room_id, status);

-- ==================== 统计报表优化 ====================

-- 收入统计查询
CREATE INDEX IF NOT EXISTS idx_orders_income_stats ON orders(status, total_amount, created_at);

-- 用户消费统计
CREATE INDEX IF NOT EXISTS idx_orders_user_consumption ON orders(user_id, total_amount, created_at);

-- 房间收入统计
CREATE INDEX IF NOT EXISTS idx_orders_room_income ON orders(room_id, total_amount, created_at);

-- 设备使用时长统计
CREATE INDEX IF NOT EXISTS idx_orders_device_usage ON orders(room_id, start_time, end_time);

-- ==================== 缓存相关优化 ====================

-- 热点数据查询优化
CREATE INDEX IF NOT EXISTS idx_rooms_hot_data ON rooms(status, id, name, room_number);

-- 用户基本信息快速查询
CREATE INDEX IF NOT EXISTS idx_users_basic_info ON users(id, nickname, phone, balance);

-- 订单基本信息快速查询
CREATE INDEX IF NOT EXISTS idx_orders_basic_info ON orders(id, user_id, room_id, status, total_amount);

-- ==================== 分析和统计 ====================

-- 执行ANALYZE命令更新统计信息
ANALYZE;

-- ==================== 索引效果验证查询 ====================

-- 以下查询可用于验证索引效果，但不会在脚本中执行
/*
-- 验证用户列表查询性能
EXPLAIN QUERY PLAN SELECT * FROM users ORDER BY created_at DESC LIMIT 20;

-- 验证订单统计查询性能
EXPLAIN QUERY PLAN 
SELECT user_id, COUNT(*) as order_count, SUM(total_amount) as total_consumption 
FROM orders 
GROUP BY user_id;

-- 验证房间设备查询性能
EXPLAIN QUERY PLAN 
SELECT r.*, d.* 
FROM rooms r 
LEFT JOIN devices d ON r.id = d.room_id 
WHERE r.status = 'available';

-- 验证用户搜索查询性能
EXPLAIN QUERY PLAN 
SELECT * FROM users 
WHERE nickname LIKE '%测试%' OR phone LIKE '%138%' 
ORDER BY created_at DESC;
*/
