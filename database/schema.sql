-- 自助麻将室系统数据库表结构
-- SQLite 数据库

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    openid TEXT UNIQUE NOT NULL,
    nickname TEXT,
    avatar_url TEXT,
    phone TEXT,
    balance DECIMAL(10,2) DEFAULT 0.00,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 计费规则表
CREATE TABLE IF NOT EXISTS pricing_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    price_per_hour DECIMAL(10,2) NOT NULL,
    overnight_price DECIMAL(10,2),
    start_time TEXT,
    end_time TEXT,
    is_weekend BOOLEAN DEFAULT FALSE,
    is_holiday BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 房间表
CREATE TABLE IF NOT EXISTS rooms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    room_number TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'available', -- available, occupied, maintenance
    pricing_rule_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pricing_rule_id) REFERENCES pricing_rules(id)
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0.00,
    status TEXT DEFAULT 'pending', -- pending, paid, completed, cancelled
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (room_id) REFERENCES rooms(id)
);

-- 外卖平台订单表（已在下方重新定义，此处删除重复定义）

-- 设备表
CREATE TABLE IF NOT EXISTS devices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL, -- lock, socket, sensor
    room_id INTEGER,
    mac_address TEXT UNIQUE,
    status TEXT DEFAULT 'online', -- online, offline, maintenance
    last_heartbeat DATETIME,
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id)
);

-- 预约表
CREATE TABLE IF NOT EXISTS reservations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    room_id INTEGER NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    status TEXT DEFAULT 'confirmed', -- confirmed, cancelled, completed
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (room_id) REFERENCES rooms(id)
);

-- 优惠活动表
CREATE TABLE IF NOT EXISTS promotions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    discount DECIMAL(3,2) NOT NULL, -- 0.00 - 1.00
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 余额变动记录表
CREATE TABLE IF NOT EXISTS balance_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type TEXT NOT NULL, -- recharge, consume, refund
    amount DECIMAL(10,2) NOT NULL,
    balance DECIMAL(10,2) NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 支出记录表
CREATE TABLE IF NOT EXISTS expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL, -- rent, utilities, maintenance, labor, marketing, other
    amount DECIMAL(10,2) NOT NULL,
    date DATE NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 平台订单表
CREATE TABLE IF NOT EXISTS platform_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform_order_id TEXT UNIQUE NOT NULL,
    platform_type TEXT NOT NULL, -- meituan, eleme
    room_id INTEGER,
    user_id INTEGER,
    customer_name TEXT,
    customer_phone TEXT,
    original_amount DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    paid_amount DECIMAL(10,2) NOT NULL,
    order_status TEXT NOT NULL, -- pending, paid, completed, cancelled
    verification_status TEXT DEFAULT 'pending', -- pending, verified, refunded
    verification_code TEXT,
    verified_at DATETIME,
    verified_by TEXT,
    verification_method TEXT,
    order_items TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_openid ON users(openid);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

CREATE INDEX IF NOT EXISTS idx_rooms_room_number ON rooms(room_number);
CREATE INDEX IF NOT EXISTS idx_rooms_status ON rooms(status);
CREATE INDEX IF NOT EXISTS idx_rooms_pricing_rule_id ON rooms(pricing_rule_id);

CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_room_id ON orders(room_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

CREATE INDEX IF NOT EXISTS idx_platform_orders_platform_order_id ON platform_orders(platform_order_id);
CREATE INDEX IF NOT EXISTS idx_platform_orders_platform_type ON platform_orders(platform_type);
CREATE INDEX IF NOT EXISTS idx_platform_orders_room_id ON platform_orders(room_id);
CREATE INDEX IF NOT EXISTS idx_platform_orders_user_id ON platform_orders(user_id);

CREATE INDEX IF NOT EXISTS idx_devices_mac_address ON devices(mac_address);
CREATE INDEX IF NOT EXISTS idx_devices_type ON devices(type);
CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_room_id ON devices(room_id);

CREATE INDEX IF NOT EXISTS idx_reservations_user_id ON reservations(user_id);
CREATE INDEX IF NOT EXISTS idx_reservations_room_id ON reservations(room_id);
CREATE INDEX IF NOT EXISTS idx_reservations_status ON reservations(status);
CREATE INDEX IF NOT EXISTS idx_reservations_start_time ON reservations(start_time);

CREATE INDEX IF NOT EXISTS idx_promotions_is_active ON promotions(is_active);
CREATE INDEX IF NOT EXISTS idx_promotions_start_time ON promotions(start_time);
CREATE INDEX IF NOT EXISTS idx_promotions_end_time ON promotions(end_time);

CREATE INDEX IF NOT EXISTS idx_balance_records_user_id ON balance_records(user_id);
CREATE INDEX IF NOT EXISTS idx_balance_records_type ON balance_records(type);
CREATE INDEX IF NOT EXISTS idx_balance_records_created_at ON balance_records(created_at);

CREATE INDEX IF NOT EXISTS idx_expenses_type ON expenses(type);
CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);
CREATE INDEX IF NOT EXISTS idx_expenses_created_at ON expenses(created_at);

CREATE INDEX IF NOT EXISTS idx_platform_orders_platform_order_id ON platform_orders(platform_order_id);
CREATE INDEX IF NOT EXISTS idx_platform_orders_platform_type ON platform_orders(platform_type);
CREATE INDEX IF NOT EXISTS idx_platform_orders_room_id ON platform_orders(room_id);
CREATE INDEX IF NOT EXISTS idx_platform_orders_order_status ON platform_orders(order_status);
CREATE INDEX IF NOT EXISTS idx_platform_orders_verification_status ON platform_orders(verification_status);
CREATE INDEX IF NOT EXISTS idx_platform_orders_created_at ON platform_orders(created_at);
