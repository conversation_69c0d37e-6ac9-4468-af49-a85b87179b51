const { test, expect } = require('@playwright/test');

test('简单套餐功能验证', async ({ page }) => {
  console.log('=== 简单套餐功能验证 ===');
  
  const backendURL = 'http://localhost:8080';
  
  // 1. 测试后端API是否正常
  console.log('1. 测试后端套餐API...');
  
  const packagesResponse = await page.request.get(`${backendURL}/api/v1/packages`);
  expect(packagesResponse.status()).toBe(200);
  
  const packagesData = await packagesResponse.json();
  console.log('套餐API响应:', packagesData);
  
  expect(packagesData.code).toBe(200);
  expect(packagesData.data).toBeInstanceOf(Array);
  expect(packagesData.data.length).toBeGreaterThan(0);
  
  console.log(`✅ 后端API正常，返回${packagesData.data.length}个套餐`);
  
  // 2. 测试管理端API
  console.log('2. 测试管理端套餐API...');
  
  const adminPackagesResponse = await page.request.get(`${backendURL}/api/v1/admin/packages`);
  expect(adminPackagesResponse.status()).toBe(200);
  
  const adminPackagesData = await adminPackagesResponse.json();
  console.log('管理端API响应:', adminPackagesData);
  
  expect(adminPackagesData.code).toBe(200);
  expect(adminPackagesData.data).toHaveProperty('list');
  expect(adminPackagesData.data).toHaveProperty('total');
  
  console.log(`✅ 管理端API正常，总计${adminPackagesData.data.total}个套餐`);
  
  // 3. 测试套餐统计API
  console.log('3. 测试套餐统计API...');
  
  const statsResponse = await page.request.get(`${backendURL}/api/v1/admin/packages/stats`);
  expect(statsResponse.status()).toBe(200);
  
  const statsData = await statsResponse.json();
  console.log('统计API响应:', statsData);
  
  expect(statsData.code).toBe(200);
  expect(statsData.data).toHaveProperty('total');
  expect(statsData.data).toHaveProperty('active');
  
  console.log(`✅ 统计API正常，总计${statsData.data.total}个套餐，${statsData.data.active}个启用`);
  
  // 4. 验证套餐数据结构
  console.log('4. 验证套餐数据结构...');
  
  const packages = packagesData.data;
  packages.forEach((pkg, index) => {
    console.log(`套餐${index + 1}: ${pkg.name}`);
    console.log(`  - ID: ${pkg.id}`);
    console.log(`  - 类型: ${pkg.type}`);
    console.log(`  - 原价: ¥${pkg.original_price}`);
    console.log(`  - 售价: ¥${pkg.sale_price}`);
    console.log(`  - 折扣: ${pkg.discount_rate}%`);
    console.log(`  - 状态: ${pkg.is_active ? '启用' : '禁用'}`);
    
    // 验证必要字段
    expect(pkg).toHaveProperty('id');
    expect(pkg).toHaveProperty('name');
    expect(pkg).toHaveProperty('type');
    expect(pkg).toHaveProperty('original_price');
    expect(pkg).toHaveProperty('sale_price');
    expect(pkg).toHaveProperty('features');
    expect(pkg).toHaveProperty('is_active');
    
    // 验证价格逻辑
    expect(pkg.sale_price).toBeLessThanOrEqual(pkg.original_price);
    expect(pkg.discount_rate).toBeGreaterThanOrEqual(0);
    
    // 验证特色功能
    expect(pkg.features).toBeInstanceOf(Array);
    expect(pkg.features.length).toBeGreaterThan(0);
  });
  
  console.log('✅ 套餐数据结构验证通过');
  
  // 5. 测试套餐类型筛选
  console.log('5. 测试套餐类型筛选...');
  
  // 测试固定时长套餐
  const fixedPackagesResponse = await page.request.get(`${backendURL}/api/v1/packages?type=fixed_duration`);
  expect(fixedPackagesResponse.status()).toBe(200);
  
  const fixedPackagesData = await fixedPackagesResponse.json();
  const fixedPackages = fixedPackagesData.data;
  
  console.log(`固定时长套餐: ${fixedPackages.length}个`);
  fixedPackages.forEach(pkg => {
    expect(pkg.type).toBe('fixed_duration');
    expect(pkg.duration_hours).toBeGreaterThan(0);
    console.log(`  - ${pkg.name}: ${pkg.duration_hours}小时, ¥${pkg.sale_price}`);
  });
  
  // 测试灵活续费套餐
  const flexiblePackagesResponse = await page.request.get(`${backendURL}/api/v1/packages?type=flexible_recharge`);
  expect(flexiblePackagesResponse.status()).toBe(200);
  
  const flexiblePackagesData = await flexiblePackagesResponse.json();
  const flexiblePackages = flexiblePackagesData.data;
  
  console.log(`灵活续费套餐: ${flexiblePackages.length}个`);
  flexiblePackages.forEach(pkg => {
    expect(pkg.type).toBe('flexible_recharge');
    expect(pkg.duration_hours).toBeNull();
    console.log(`  - ${pkg.name}: ${pkg.min_recharge_hours}-${pkg.max_recharge_hours}小时, ¥${pkg.sale_price}/小时`);
  });
  
  console.log('✅ 套餐类型筛选功能正常');
  
  // 6. 测试套餐详情API
  console.log('6. 测试套餐详情API...');
  
  if (packages.length > 0) {
    const firstPackage = packages[0];
    const detailResponse = await page.request.get(`${backendURL}/api/v1/packages/${firstPackage.id}`);
    expect(detailResponse.status()).toBe(200);
    
    const detailData = await detailResponse.json();
    expect(detailData.code).toBe(200);
    expect(detailData.data.id).toBe(firstPackage.id);
    expect(detailData.data.name).toBe(firstPackage.name);
    
    console.log(`✅ 套餐详情API正常，获取到套餐: ${detailData.data.name}`);
  }
  
  // 7. 验证业务逻辑
  console.log('7. 验证业务逻辑...');
  
  // 验证价格计算
  packages.forEach(pkg => {
    const expectedDiscount = ((pkg.original_price - pkg.sale_price) / pkg.original_price * 100);
    const actualDiscount = pkg.discount_rate;
    
    // 允许小数点误差
    expect(Math.abs(expectedDiscount - actualDiscount)).toBeLessThan(0.1);
  });
  
  console.log('✅ 价格计算逻辑正确');
  
  // 8. 测试错误处理
  console.log('8. 测试错误处理...');
  
  // 测试不存在的套餐ID
  const notFoundResponse = await page.request.get(`${backendURL}/api/v1/packages/99999`);
  expect(notFoundResponse.status()).toBe(500); // 应该返回错误
  
  console.log('✅ 错误处理正常');
  
  console.log('=== 简单套餐功能验证完成 ===');
  console.log('🎉 所有套餐功能都正常工作！');
  
  // 总结测试结果
  console.log('\n📊 功能验证总结:');
  console.log(`- 套餐总数: ${packages.length}`);
  console.log(`- 固定时长套餐: ${fixedPackages.length}`);
  console.log(`- 灵活续费套餐: ${flexiblePackages.length}`);
  console.log(`- 统计数据: 总计${statsData.data.total}个套餐，${statsData.data.active}个启用`);
  console.log(`- 总销量: ${statsData.data.total_sales}`);
  console.log(`- 总收入: ¥${statsData.data.total_revenue}`);
  
  // 显示所有套餐信息
  console.log('\n📋 套餐列表:');
  packages.forEach((pkg, index) => {
    console.log(`${index + 1}. ${pkg.name}`);
    console.log(`   类型: ${pkg.type === 'fixed_duration' ? '固定时长' : '灵活续费'}`);
    if (pkg.type === 'fixed_duration') {
      console.log(`   时长: ${pkg.duration_hours}小时`);
    } else {
      console.log(`   续费范围: ${pkg.min_recharge_hours}-${pkg.max_recharge_hours}小时`);
    }
    console.log(`   价格: ¥${pkg.sale_price} (原价¥${pkg.original_price}, ${pkg.discount_rate}%折扣)`);
    console.log(`   特色: ${pkg.features.join(', ')}`);
    console.log(`   状态: ${pkg.is_active ? '启用' : '禁用'}`);
    console.log('');
  });
});
