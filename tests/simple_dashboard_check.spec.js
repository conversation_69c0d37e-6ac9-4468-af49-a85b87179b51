const { test, expect } = require('@playwright/test');

test('简单检查Dashboard页面', async ({ page }) => {
  // 监听控制台消息
  const consoleMessages = [];
  page.on('console', msg => {
    consoleMessages.push(`${msg.type()}: ${msg.text()}`);
  });

  // 监听网络请求
  const networkRequests = [];
  page.on('request', request => {
    if (request.url().includes('/api/')) {
      networkRequests.push(request.url());
    }
  });

  // 监听网络响应
  const networkResponses = [];
  page.on('response', response => {
    if (response.url().includes('/api/')) {
      networkResponses.push({
        url: response.url(),
        status: response.status()
      });
    }
  });

  try {
    console.log('正在访问Dashboard页面...');
    await page.goto('http://localhost:3001', { timeout: 10000 });
    
    console.log('等待页面加载...');
    await page.waitForLoadState('domcontentloaded');
    
    // 等待一段时间让Vue应用初始化
    await page.waitForTimeout(5000);
    
    console.log('页面标题:', await page.title());
    
    // 检查页面基本结构
    const appElement = await page.locator('#app').count();
    console.log('Vue应用容器存在:', appElement > 0);
    
    // 检查是否有基本的文本内容
    const bodyText = await page.locator('body').textContent();
    console.log('页面包含"今日收入":', bodyText.includes('今日收入'));
    console.log('页面包含"总房间数":', bodyText.includes('总房间数'));
    
    // 截图
    await page.screenshot({ path: 'dashboard-debug.png', fullPage: true });
    
    console.log('控制台消息:');
    consoleMessages.forEach(msg => console.log('  ', msg));
    
    console.log('网络请求:');
    networkRequests.forEach(url => console.log('  ', url));
    
    console.log('网络响应:');
    networkResponses.forEach(resp => console.log('  ', resp.url, '->', resp.status));
    
  } catch (error) {
    console.error('页面访问失败:', error.message);
    
    // 即使失败也要输出日志
    console.log('控制台消息:');
    consoleMessages.forEach(msg => console.log('  ', msg));
    
    console.log('网络请求:');
    networkRequests.forEach(url => console.log('  ', url));
    
    console.log('网络响应:');
    networkResponses.forEach(resp => console.log('  ', resp.url, '->', resp.status));
  }
});
