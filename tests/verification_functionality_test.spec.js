const { test, expect } = require('@playwright/test');

// 测试配置
const FRONTEND_URL = 'http://localhost:3000';
const API_BASE = 'http://localhost:8080/api/v1';

test.describe('订单核销功能测试', () => {
  
  test('核销API端点测试', async ({ request }) => {
    console.log('🔍 测试核销API端点');
    
    // 测试核销统计API
    const statsResponse = await request.get(`${API_BASE}/admin/platform/verification/stats`);
    expect(statsResponse.status()).toBe(200);
    
    const statsData = await statsResponse.json();
    expect(statsData.code).toBe(200);
    expect(statsData.data).toHaveProperty('today');
    expect(statsData.data).toHaveProperty('pending');
    expect(statsData.data).toHaveProperty('efficiency');
    
    console.log('✅ 核销统计API正常');
    
    // 测试待核销订单API
    const pendingResponse = await request.get(`${API_BASE}/admin/platform/orders/pending`);
    expect(pendingResponse.status()).toBe(200);
    
    const pendingData = await pendingResponse.json();
    expect(pendingData.code).toBe(200);
    // API可能返回null（无数据）或数组，都是正常的
    expect(pendingData.data === null || Array.isArray(pendingData.data)).toBe(true);
    
    console.log('✅ 待核销订单API正常');
    
    // 测试核销历史API
    const historyResponse = await request.get(`${API_BASE}/admin/platform/verification/history`);
    expect(historyResponse.status()).toBe(200);
    
    const historyData = await historyResponse.json();
    expect(historyData.code).toBe(200);
    expect(historyData.data).toHaveProperty('data');
    expect(historyData.data).toHaveProperty('total');
    
    console.log('✅ 核销历史API正常');
  });

  test('核销表单填写和验证测试', async ({ page }) => {
    console.log('🔍 测试核销表单填写和验证');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 测试表单验证 - 空表单提交
    await page.click('button:has-text("立即核销")');
    await page.waitForTimeout(1000);
    
    // 检查验证错误
    const errorMessages = await page.locator('.el-form-item__error').count();
    expect(errorMessages).toBeGreaterThan(0);
    
    console.log('✅ 空表单验证正常');
    
    // 填写表单 - Element Plus select组件需要特殊处理
    await page.click('.el-select'); // 点击选择器
    await page.waitForTimeout(500);
    const meituanOption = await page.locator('.el-option:has-text("美团")');
    if (await meituanOption.count() > 0) {
      await meituanOption.click();
    }
    await page.fill('input[placeholder="输入平台订单号"]', 'TEST_ORDER_123');
    await page.fill('input[placeholder="输入核销码（可选）"]', 'VERIFY_123');
    
    // 选择房间（如果有房间选项）
    const roomSelect = await page.locator('label:has-text("房间号")').count();
    if (roomSelect > 0) {
      // 尝试选择第一个房间
      await page.click('label:has-text("房间号") + .el-form-item__content .el-select');
      await page.waitForTimeout(500);
      const firstOption = await page.locator('.el-select-dropdown .el-option').first();
      if (await firstOption.isVisible()) {
        await firstOption.click();
      }
    }
    
    console.log('✅ 表单填写完成');
    
    // 测试核销提交（预期会失败，因为是测试订单）
    await page.click('button:has-text("立即核销")');
    await page.waitForTimeout(2000);
    
    // 检查是否有响应（成功或失败消息）
    const messageExists = await page.locator('.el-message').count() > 0;
    console.log(`✅ 核销提交响应: ${messageExists ? '有响应' : '无响应'}`);
  });

  test('重置表单功能测试', async ({ page }) => {
    console.log('🔍 测试重置表单功能');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 填写表单 - Element Plus select组件需要特殊处理
    await page.click('.el-select'); // 点击选择器
    await page.waitForTimeout(500);
    const meituanOption = await page.locator('.el-option:has-text("美团")');
    if (await meituanOption.count() > 0) {
      await meituanOption.click();
    }
    await page.fill('input[placeholder="输入平台订单号"]', 'TEST_ORDER_123');
    await page.fill('input[placeholder="输入核销码（可选）"]', 'VERIFY_123');
    
    // 点击重置按钮
    await page.click('button:has-text("重置")');
    await page.waitForTimeout(500);
    
    // 检查表单是否被重置 - 主要检查输入框
    const orderIdValue = await page.inputValue('input[placeholder="输入平台订单号"]');
    const verifyCodeValue = await page.inputValue('input[placeholder="输入核销码（可选）"]');

    // 重置后输入框应该为空
    expect(orderIdValue).toBe('');
    expect(verifyCodeValue).toBe('');

    // 检查重置按钮是否存在并可用
    const resetButtonExists = await page.locator('button:has-text("重置")').count();
    expect(resetButtonExists).toBe(1);
    
    console.log('✅ 重置表单功能正常');
  });

  test('刷新功能测试', async ({ page }) => {
    console.log('🔍 测试刷新功能');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待初始数据加载
    await page.waitForTimeout(2000);
    
    // 记录初始统计数据
    const initialStats = await page.locator('.stat-value').allTextContents();
    
    // 点击刷新按钮
    await page.click('button:has-text("刷新待核销")');
    await page.waitForTimeout(2000);
    
    // 检查数据是否重新加载
    const refreshedStats = await page.locator('.stat-value').allTextContents();
    
    // 数据应该保持一致（因为没有新的变化）
    expect(refreshedStats.length).toBe(initialStats.length);
    
    console.log('✅ 刷新功能正常');
  });

  test('历史记录筛选测试', async ({ page }) => {
    console.log('🔍 测试历史记录筛选');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待数据加载
    await page.waitForTimeout(2000);
    
    // 测试时间筛选器
    const timeFilters = ['today', 'week', 'month'];
    
    for (const filter of timeFilters) {
      // 选择筛选器 - Element Plus select组件
      await page.click('.history-actions .el-select');
      await page.waitForTimeout(500);
      const filterOption = await page.locator(`.el-option[value="${filter}"]`);
      if (await filterOption.count() > 0) {
        await filterOption.click();
        await page.waitForTimeout(1000);

        // 检查表格是否重新加载
        const tableExists = await page.locator('.el-table').count() > 0;
        expect(tableExists).toBe(true);

        console.log(`✅ ${filter} 筛选器正常`);
      }
    }
  });

  test('扫码核销对话框测试', async ({ page }) => {
    console.log('🔍 测试扫码核销对话框');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 点击扫码核销按钮
    await page.click('button:has-text("扫码核销")');
    
    // 等待对话框出现
    await page.waitForSelector('.el-dialog', { timeout: 5000 });
    
    // 检查对话框标题
    const dialogTitle = await page.locator('.el-dialog__title').textContent();
    expect(dialogTitle).toContain('扫码核销');
    
    // 检查对话框内容
    const scanContent = await page.locator('.scan-content').count();
    expect(scanContent).toBe(1);
    
    // 检查摄像头容器
    const cameraContainer = await page.locator('.camera-container').count();
    expect(cameraContainer).toBe(1);
    
    // 检查操作按钮
    const closeButton = await page.locator('.el-dialog .el-button:has-text("关闭")').count();
    const startButton = await page.locator('.el-dialog .el-button:has-text("开始扫描")').count();
    
    expect(closeButton).toBe(1);
    expect(startButton).toBe(1);
    
    // 关闭对话框
    await page.click('.el-dialog .el-button:has-text("关闭")');
    await page.waitForTimeout(500);
    
    // 检查对话框是否关闭
    const dialogVisible = await page.locator('.el-dialog').isVisible();
    expect(dialogVisible).toBe(false);
    
    console.log('✅ 扫码核销对话框功能正常');
  });

  test('数据实时更新测试', async ({ page }) => {
    console.log('🔍 测试数据实时更新');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待初始数据加载
    await page.waitForTimeout(2000);
    
    // 记录初始数据
    const initialTodayValue = await page.locator('.stat-card.today .stat-value').textContent();
    const initialPendingValue = await page.locator('.stat-card.pending .stat-value').textContent();
    
    // 模拟数据更新（通过刷新）
    await page.click('button:has-text("刷新待核销")');
    await page.waitForTimeout(2000);
    
    // 检查数据是否更新
    const updatedTodayValue = await page.locator('.stat-card.today .stat-value').textContent();
    const updatedPendingValue = await page.locator('.stat-card.pending .stat-value').textContent();
    
    // 数据应该保持一致或有合理变化
    expect(typeof parseInt(updatedTodayValue)).toBe('number');
    expect(typeof parseInt(updatedPendingValue)).toBe('number');
    
    console.log('✅ 数据实时更新功能正常');
  });

  test('错误处理和用户反馈测试', async ({ page }) => {
    console.log('🔍 测试错误处理和用户反馈');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 测试无效订单号核销
    await page.click('.el-select'); // 点击选择器
    await page.waitForTimeout(500);
    const meituanOption = await page.locator('.el-option:has-text("美团")');
    if (await meituanOption.count() > 0) {
      await meituanOption.click();
    }
    await page.fill('input[placeholder="输入平台订单号"]', 'INVALID_ORDER_12345');
    
    // 尝试核销
    await page.click('button:has-text("立即核销")');
    await page.waitForTimeout(3000);
    
    // 检查是否有错误提示
    const errorMessage = await page.locator('.el-message--error').count();
    const anyMessage = await page.locator('.el-message').count();
    
    // 应该有某种形式的反馈
    expect(errorMessage + anyMessage).toBeGreaterThan(0);
    
    console.log('✅ 错误处理和用户反馈正常');
  });

  test('界面交互流畅性测试', async ({ page }) => {
    console.log('🔍 测试界面交互流畅性');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 测试多次快速操作
    for (let i = 0; i < 3; i++) {
      // 快速填写和清空表单
      await page.fill('input[placeholder="输入平台订单号"]', `TEST_${i}`);
      await page.click('button:has-text("重置")');
      await page.waitForTimeout(200);
    }
    
    // 测试快速切换筛选器 - Element Plus select组件
    const filters = ['today', 'week', 'month'];
    for (const filter of filters) {
      // 检查筛选器是否存在
      const selectExists = await page.locator('.history-actions .el-select').count();
      if (selectExists > 0) {
        await page.click('.history-actions .el-select');
        await page.waitForTimeout(300);
        const filterOption = await page.locator(`.el-option[value="${filter}"]`);
        if (await filterOption.count() > 0) {
          await filterOption.click();
        }
      }
      await page.waitForTimeout(300);
    }
    
    // 测试快速刷新
    for (let i = 0; i < 2; i++) {
      await page.click('button:has-text("刷新待核销")');
      await page.waitForTimeout(500);
    }
    
    // 检查页面是否仍然响应
    const pageResponsive = await page.locator('.page-header').isVisible();
    expect(pageResponsive).toBe(true);
    
    console.log('✅ 界面交互流畅性正常');
  });

  test('核销权限和安全性测试', async ({ page }) => {
    console.log('🔍 测试核销权限和安全性');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 检查是否有适当的权限控制
    const verifyButton = await page.locator('button:has-text("立即核销")').count();
    expect(verifyButton).toBe(1);
    
    // 检查是否有输入验证
    await page.fill('input[placeholder="输入平台订单号"]', '<script>alert("xss")</script>');
    await page.click('button:has-text("立即核销")');
    await page.waitForTimeout(1000);
    
    // 页面应该仍然正常工作，没有执行脚本
    const pageTitle = await page.locator('h2:has-text("订单核销中心")').count();
    expect(pageTitle).toBe(1);
    
    console.log('✅ 核销权限和安全性正常');
  });
});
