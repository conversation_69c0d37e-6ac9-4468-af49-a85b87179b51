// 房间删除功能（包含历史订单）验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🎯 房间删除功能（包含历史订单）验证', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始房间删除功能（包含历史订单）验证');
    console.log('🎯 验证目标: 即使有历史订单记录也能成功删除房间');
  });

  // ==================== 测试1: 删除有历史订单的房间 ====================
  test('✅ 删除有历史订单的房间', async ({ request }) => {
    console.log('🔧 测试删除有历史订单的房间');
    
    // 1. 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'history_test_user_' + Date.now(),
        nickname: '历史订单测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const userId = userData.data.id;
    
    // 2. 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'HISTORY_TEST_' + Date.now(),
        name: '历史订单测试房间',
        description: '用于测试有历史订单时的删除',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const roomId = roomData.data.id;
    
    console.log(`📝 创建测试房间成功，ID: ${roomId}`);
    
    // 3. 创建订单
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': userId.toString() },
      data: {
        room_id: roomId,
        total_amount: 50.00
      }
    });
    expect(orderResponse.status()).toBe(200);
    const orderData = await orderResponse.json();
    const orderId = orderData.data.id;
    
    console.log(`📝 创建订单成功，ID: ${orderId}`);
    
    // 4. 取消订单（变成历史订单）
    const cancelResponse = await request.post(`${API_BASE}/orders/${orderId}/cancel`);
    expect(cancelResponse.status()).toBe(200);
    
    console.log(`📝 订单已取消，变成历史订单`);
    
    // 5. 现在尝试删除房间（应该成功）
    const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteResponse.status()).toBe(200);
    
    const deleteData = await deleteResponse.json();
    expect(deleteData.code).toBe(200);
    expect(deleteData.data.message).toBe('删除成功');
    
    console.log(`✅ 房间删除成功，即使有历史订单记录`);
    
    // 6. 验证房间确实被删除了
    const getResponse = await request.get(`${API_BASE}/admin/rooms/${roomId}`);
    expect(getResponse.status()).toBe(404);
    
    console.log(`✅ 房间已从系统中删除`);
    
    // 7. 验证订单仍然存在但 room_id 为 null（如果需要的话）
    const orderCheckResponse = await request.get(`${API_BASE}/orders/${orderId}`);
    if (orderCheckResponse.status() === 200) {
      const orderCheckData = await orderCheckResponse.json();
      console.log(`📝 订单仍然存在，room_id: ${orderCheckData.data.room_id || 'null'}`);
    }
  });

  // ==================== 测试2: 删除有多个历史订单的房间 ====================
  test('✅ 删除有多个历史订单的房间', async ({ request }) => {
    console.log('🔧 测试删除有多个历史订单的房间');
    
    // 1. 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'multi_history_test_user_' + Date.now(),
        nickname: '多历史订单测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const userId = userData.data.id;
    
    // 2. 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'MULTI_HISTORY_TEST_' + Date.now(),
        name: '多历史订单测试房间',
        description: '用于测试有多个历史订单时的删除',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const roomId = roomData.data.id;
    
    // 3. 创建多个订单并取消
    const orderIds = [];
    for (let i = 0; i < 3; i++) {
      const orderResponse = await request.post(`${API_BASE}/orders`, {
        headers: { 'X-User-ID': userId.toString() },
        data: {
          room_id: roomId,
          total_amount: 30.00 + i * 10
        }
      });
      expect(orderResponse.status()).toBe(200);
      const orderData = await orderResponse.json();
      orderIds.push(orderData.data.id);
      
      // 取消订单
      const cancelResponse = await request.post(`${API_BASE}/orders/${orderData.data.id}/cancel`);
      expect(cancelResponse.status()).toBe(200);
    }
    
    console.log(`📝 创建并取消了${orderIds.length}个订单`);
    
    // 4. 删除房间（应该成功）
    const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteResponse.status()).toBe(200);
    
    const deleteData = await deleteResponse.json();
    expect(deleteData.code).toBe(200);
    expect(deleteData.data.message).toBe('删除成功');
    
    console.log(`✅ 房间删除成功，即使有多个历史订单记录`);
  });

  // ==================== 测试3: 仍然阻止删除有活跃订单的房间 ====================
  test('✅ 仍然阻止删除有活跃订单的房间', async ({ request }) => {
    console.log('🔧 验证仍然阻止删除有活跃订单的房间');
    
    // 1. 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'active_test_user_' + Date.now(),
        nickname: '活跃订单测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const userId = userData.data.id;
    
    // 2. 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'ACTIVE_TEST_' + Date.now(),
        name: '活跃订单测试房间',
        description: '用于测试有活跃订单时的删除阻止',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const roomId = roomData.data.id;
    
    // 3. 创建活跃订单（不取消）
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': userId.toString() },
      data: {
        room_id: roomId,
        total_amount: 50.00
      }
    });
    expect(orderResponse.status()).toBe(200);
    const orderData = await orderResponse.json();
    const orderId = orderData.data.id;
    
    console.log(`📝 创建活跃订单，ID: ${orderId}`);
    
    // 4. 尝试删除房间（应该失败）
    const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteResponse.status()).toBe(409);
    
    const deleteData = await deleteResponse.json();
    expect(deleteData.message).toBe('房间有活跃订单，无法删除。请先完成或取消房间内的订单');
    
    console.log(`✅ 正确阻止删除有活跃订单的房间`);
    
    // 5. 清理：取消订单
    await request.post(`${API_BASE}/orders/${orderId}/cancel`);
    
    // 6. 现在应该可以删除房间了
    const deleteAfterCancelResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteAfterCancelResponse.status()).toBe(200);
    
    console.log(`✅ 订单取消后成功删除房间`);
  });

  test.afterAll(async () => {
    console.log('🏁 房间删除功能（包含历史订单）验证完成');
    console.log('📋 验证总结:');
    console.log('  ✅ 有历史订单的房间 - 可以删除');
    console.log('  ✅ 有多个历史订单的房间 - 可以删除');
    console.log('  ✅ 有活跃订单的房间 - 正确阻止删除');
    console.log('🎉 房间删除功能现在完全符合业务需求！');
    console.log('💡 历史订单不再阻止房间删除，但活跃订单仍然会阻止');
  });

});
