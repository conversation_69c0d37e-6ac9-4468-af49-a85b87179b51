const { test, expect } = require('@playwright/test');

test('简单图表数据验证', async ({ page }) => {
  console.log('=== 简单图表数据验证 ===');
  
  // 获取后端数据
  const apiResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const apiData = await apiResponse.json();
  const backendData = apiData.data;
  
  console.log('后端数据:', {
    activeOrders: backendData.activeOrders,
    todayIncome: backendData.todayIncome,
    onlineDevices: backendData.onlineDevices
  });
  
  // 访问前端
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  // 检查实时监控数据
  const activeOrdersElement = await page.locator('.realtime-chart .metric-item').filter({ hasText: '活跃订单' });
  if (await activeOrdersElement.count() > 0) {
    const value = await activeOrdersElement.locator('.metric-value').textContent();
    console.log(`前端显示活跃订单: ${value}, 后端: ${backendData.activeOrders}`);
    expect(value.trim()).toBe(backendData.activeOrders.toString());
  }
  
  const onlineDevicesElement = await page.locator('.realtime-chart .metric-item').filter({ hasText: '在线设备' });
  if (await onlineDevicesElement.count() > 0) {
    const value = await onlineDevicesElement.locator('.metric-value').textContent();
    console.log(`前端显示在线设备: ${value}, 后端: ${backendData.onlineDevices}`);
    expect(value.trim()).toBe(backendData.onlineDevices.toString());
  }
  
  const todayIncomeElement = await page.locator('.realtime-chart .metric-item').filter({ hasText: '今日收入' });
  if (await todayIncomeElement.count() > 0) {
    const value = await todayIncomeElement.locator('.metric-value').textContent();
    console.log(`前端显示今日收入: ${value}, 后端: ¥${backendData.todayIncome.toFixed(2)}`);
    expect(value).toContain(backendData.todayIncome.toFixed(2));
  }
  
  console.log('✅ 实时监控栏数据验证通过');
  
  // 检查图表容器是否存在
  const chartContainer = await page.locator('.realtime-chart .chart-container');
  const chartExists = await chartContainer.count();
  console.log(`图表容器存在: ${chartExists > 0 ? '是' : '否'}`);
  
  if (chartExists > 0) {
    // 检查图表是否有内容
    const chartContent = await page.evaluate(() => {
      const container = document.querySelector('.realtime-chart .chart-container');
      return {
        hasContainer: !!container,
        hasCanvas: !!container?.querySelector('canvas'),
        hasSvg: !!container?.querySelector('svg'),
        innerHTML: container?.innerHTML?.length || 0
      };
    });
    
    console.log('图表内容:', chartContent);
    
    if (chartContent.hasCanvas || chartContent.hasSvg || chartContent.innerHTML > 0) {
      console.log('✅ 图表已正确渲染');
    } else {
      console.log('⚠️ 图表容器存在但可能未正确渲染');
    }
  }
  
  console.log('🎉 图表数据验证完成');
});
