const { test, expect } = require('@playwright/test');

// 测试配置
const FRONTEND_URL = 'http://localhost:3000';
const API_BASE = 'http://localhost:8080/api/v1';

test.describe('订单核销界面测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 设置网络监听
    page.on('response', response => {
      if (response.status() >= 400) {
        console.log(`❌ 网络错误: ${response.url()} - ${response.status()}`);
      }
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ 控制台错误: ${msg.text()}`);
      }
    });
  });

  test('页面基本加载测试', async ({ page }) => {
    console.log('🔍 测试订单核销页面基本加载');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题
    const pageTitle = await page.locator('h2:has-text("订单核销中心")').count();
    expect(pageTitle).toBe(1);
    
    // 检查页面描述
    const pageDescription = await page.locator('.page-description').count();
    expect(pageDescription).toBe(1);
    
    // 检查操作按钮
    const refreshButton = await page.locator('button:has-text("刷新待核销")').count();
    expect(refreshButton).toBe(1);
    
    const scanButton = await page.locator('button:has-text("扫码核销")').count();
    expect(scanButton).toBe(1);
    
    console.log('✅ 页面基本加载测试通过');
  });

  test('统计卡片显示测试', async ({ page }) => {
    console.log('🔍 测试统计卡片显示');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待统计数据加载
    await page.waitForTimeout(2000);
    
    // 检查统计卡片数量
    const statCards = await page.locator('.stat-card').count();
    expect(statCards).toBe(3);
    
    // 检查今日已核销卡片
    const todayCard = await page.locator('.stat-card.today').count();
    expect(todayCard).toBe(1);
    
    // 检查待核销订单卡片
    const pendingCard = await page.locator('.stat-card.pending').count();
    expect(pendingCard).toBe(1);
    
    // 检查核销效率卡片
    const efficiencyCard = await page.locator('.stat-card.efficiency').count();
    expect(efficiencyCard).toBe(1);
    
    // 检查统计数值是否显示
    const statValues = await page.locator('.stat-value').count();
    expect(statValues).toBeGreaterThanOrEqual(3);
    
    console.log('✅ 统计卡片显示测试通过');
  });

  test('快速核销表单测试', async ({ page }) => {
    console.log('🔍 测试快速核销表单');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 检查快速核销表单
    const verifyForm = await page.locator('.quick-verify-card').count();
    expect(verifyForm).toBe(1);
    
    // 检查平台类型选择
    const platformSelect = await page.locator('label:has-text("平台类型")').count();
    expect(platformSelect).toBe(1);
    
    // 检查订单号输入
    const orderIdInput = await page.locator('label:has-text("订单号")').count();
    expect(orderIdInput).toBe(1);
    
    // 检查核销码输入
    const verifyCodeInput = await page.locator('label:has-text("核销码")').count();
    expect(verifyCodeInput).toBe(1);
    
    // 检查房间号选择
    const roomSelect = await page.locator('label:has-text("房间号")').count();
    expect(roomSelect).toBe(1);
    
    // 检查核销按钮
    const verifyButton = await page.locator('button:has-text("立即核销")').count();
    expect(verifyButton).toBe(1);
    
    // 检查重置按钮
    const resetButton = await page.locator('button:has-text("重置")').count();
    expect(resetButton).toBe(1);
    
    console.log('✅ 快速核销表单测试通过');
  });

  test('待核销订单列表测试', async ({ page }) => {
    console.log('🔍 测试待核销订单列表');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待数据加载
    await page.waitForTimeout(2000);
    
    // 检查待核销订单区域
    const pendingSection = await page.locator('.pending-orders-card').count();
    expect(pendingSection).toBe(1);

    // 检查是否有订单列表或空状态
    const ordersList = await page.locator('.pending-orders-list').count();
    expect(ordersList).toBe(1);
    
    // 检查空状态或订单项
    const emptyState = await page.locator('.empty-state').count();
    const orderItems = await page.locator('.order-item').count();
    
    expect(emptyState + orderItems).toBeGreaterThan(0);
    
    if (orderItems > 0) {
      console.log(`✅ 找到 ${orderItems} 个待核销订单`);
    } else {
      console.log('✅ 显示空状态（暂无待核销订单）');
    }
    
    console.log('✅ 待核销订单列表测试通过');
  });

  test('核销历史记录测试', async ({ page }) => {
    console.log('🔍 测试核销历史记录');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待数据加载
    await page.waitForTimeout(2000);
    
    // 检查核销历史区域
    const historySection = await page.locator('.history-card').count();
    expect(historySection).toBe(1);

    // 检查时间筛选器
    const timeFilter = await page.locator('.history-actions').count();
    expect(timeFilter).toBe(1);
    
    // 检查刷新按钮
    const refreshHistoryButton = await page.locator('.history-actions button').count();
    expect(refreshHistoryButton).toBeGreaterThanOrEqual(1);
    
    // 检查历史记录表格或空状态
    const historyTable = await page.locator('.el-table').count();
    const historyEmpty = await page.locator('.el-empty').count();

    expect(historyTable + historyEmpty).toBeGreaterThan(0);
    
    console.log('✅ 核销历史记录测试通过');
  });

  test('扫码核销功能测试', async ({ page }) => {
    console.log('🔍 测试扫码核销功能');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 点击扫码核销按钮
    await page.click('button:has-text("扫码核销")');
    
    // 等待对话框出现
    await page.waitForSelector('.el-dialog', { timeout: 5000 });

    // 检查扫码对话框
    const scanDialog = await page.locator('.el-dialog:has-text("扫码核销")').count();
    expect(scanDialog).toBe(1);
    
    // 检查摄像头容器
    const cameraContainer = await page.locator('.camera-container').count();
    expect(cameraContainer).toBe(1);
    
    // 检查扫码提示
    const scanTips = await page.locator('.scan-tips').count();
    expect(scanTips).toBe(1);
    
    // 关闭对话框
    await page.click('.el-dialog .el-button:has-text("关闭")');
    
    console.log('✅ 扫码核销功能测试通过');
  });

  test('表单验证测试', async ({ page }) => {
    console.log('🔍 测试表单验证');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 直接点击核销按钮（不填写表单）
    await page.click('button:has-text("立即核销")');
    
    // 等待验证错误提示
    await page.waitForTimeout(1000);
    
    // 检查是否有验证错误提示
    const errorMessages = await page.locator('.el-form-item__error').count();
    expect(errorMessages).toBeGreaterThan(0);
    
    console.log('✅ 表单验证测试通过');
  });

  test('刷新功能测试', async ({ page }) => {
    console.log('🔍 测试刷新功能');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待初始数据加载
    await page.waitForTimeout(2000);
    
    // 记录初始统计数据
    const initialTodayValue = await page.locator('.stat-card.today .stat-value').textContent();
    
    // 点击刷新按钮
    await page.click('button:has-text("刷新待核销")');
    
    // 等待刷新完成
    await page.waitForTimeout(2000);
    
    // 检查数据是否重新加载（值应该保持一致）
    const refreshedTodayValue = await page.locator('.stat-card.today .stat-value').textContent();
    expect(refreshedTodayValue).toBe(initialTodayValue);
    
    console.log('✅ 刷新功能测试通过');
  });

  test('响应式设计测试', async ({ page }) => {
    console.log('🔍 测试响应式设计');
    
    const viewports = [
      { width: 1920, height: 1080, name: '桌面大屏' },
      { width: 1366, height: 768, name: '桌面标准' },
      { width: 768, height: 1024, name: '平板' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto(`${FRONTEND_URL}/platform/verification`);
      await page.waitForLoadState('networkidle');
      
      // 检查主要元素是否可见
      const pageHeader = await page.locator('.page-header').isVisible();
      const statsRow = await page.locator('.stats-row').isVisible();
      const verifyForm = await page.locator('.quick-verify-card').isVisible();
      
      expect(pageHeader).toBe(true);
      expect(statsRow).toBe(true);
      expect(verifyForm).toBe(true);
      
      console.log(`✅ ${viewport.name} 响应式布局正常`);
    }
  });

  test('API连接测试', async ({ request }) => {
    console.log('🔍 测试核销相关API连接');
    
    const apis = [
      { url: `${API_BASE}/admin/platform/verification/stats`, name: '核销统计' },
      { url: `${API_BASE}/admin/platform/orders/pending`, name: '待核销订单' },
      { url: `${API_BASE}/admin/platform/verification/history`, name: '核销历史' },
      { url: `${API_BASE}/admin/rooms`, name: '房间列表' }
    ];

    for (const api of apis) {
      try {
        const response = await request.get(api.url);
        
        if (response.status() === 200) {
          console.log(`✅ ${api.name} API连接正常`);
        } else {
          console.log(`⚠️ ${api.name} API状态码: ${response.status()}`);
        }
        
        // 允许200或404状态码（某些API可能还未实现）
        expect([200, 404]).toContain(response.status());
      } catch (error) {
        console.log(`❌ ${api.name} API连接失败: ${error.message}`);
      }
    }
  });

  test('数据格式验证测试', async ({ page }) => {
    console.log('🔍 测试数据格式验证');
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待数据加载
    await page.waitForTimeout(3000);
    
    // 检查统计数值格式
    const statValues = await page.locator('.stat-value').allTextContents();
    
    for (const value of statValues) {
      // 统计数值应该是数字或包含%的字符串
      expect(value).toMatch(/^\d+(\.\d+)?%?$/);
    }
    
    // 检查金额格式（如果有）
    const amountElements = await page.locator('.stat-amount').allTextContents();
    
    for (const amount of amountElements) {
      // 金额应该以¥开头
      expect(amount).toMatch(/^¥\d+(\.\d{2})?$/);
    }
    
    console.log('✅ 数据格式验证测试通过');
  });

  test('错误处理测试', async ({ page }) => {
    console.log('🔍 测试错误处理');
    
    // 监听控制台错误
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');
    
    // 等待页面完全加载
    await page.waitForTimeout(3000);
    
    // 检查是否有严重错误
    const criticalErrors = errors.filter(error => 
      error.includes('TypeError') || 
      error.includes('ReferenceError') ||
      error.includes('SyntaxError')
    );
    
    expect(criticalErrors.length).toBe(0);
    
    console.log(`✅ 错误处理测试通过，发现 ${errors.length} 个控制台消息，${criticalErrors.length} 个严重错误`);
  });
});
