const { test, expect } = require('@playwright/test');

test('套餐管理前端功能测试', async ({ page }) => {
  console.log('=== 套餐管理前端功能测试 ===');
  
  const frontendURL = 'http://localhost:3001';
  const backendURL = 'http://localhost:8080';
  
  // 1. 访问套餐管理页面
  console.log('1. 访问套餐管理页面...');
  await page.goto(`${frontendURL}/packages`);
  
  // 等待页面加载
  await page.waitForTimeout(3000);
  
  // 检查页面标题
  await expect(page.locator('h2')).toContainText('套餐管理');
  console.log('✅ 套餐管理页面加载成功');
  
  // 2. 检查统计卡片
  console.log('2. 检查统计卡片...');
  
  // 等待统计数据加载
  await page.waitForSelector('.stat-card', { timeout: 10000 });
  
  const statCards = await page.locator('.stat-card').count();
  expect(statCards).toBe(4);
  
  // 检查统计卡片内容
  const totalPackages = await page.locator('.stat-card').first().locator('.stat-number').textContent();
  const activePackages = await page.locator('.stat-card').nth(1).locator('.stat-number').textContent();
  const totalSales = await page.locator('.stat-card').nth(2).locator('.stat-number').textContent();
  const totalRevenue = await page.locator('.stat-card').nth(3).locator('.stat-number').textContent();
  
  console.log(`统计数据: 总套餐${totalPackages}个, 启用${activePackages}个, 总销量${totalSales}, 总收入${totalRevenue}`);
  console.log('✅ 统计卡片显示正常');
  
  // 3. 检查套餐列表
  console.log('3. 检查套餐列表...');
  
  // 等待表格加载
  await page.waitForSelector('.el-table', { timeout: 10000 });
  
  const tableRows = await page.locator('.el-table tbody tr').count();
  expect(tableRows).toBeGreaterThan(0);
  
  console.log(`套餐列表显示 ${tableRows} 条记录`);
  
  // 检查表格列
  const tableHeaders = await page.locator('.el-table thead th').allTextContents();
  console.log('表格列:', tableHeaders);
  
  expect(tableHeaders).toContain('套餐名称');
  expect(tableHeaders).toContain('类型');
  expect(tableHeaders).toContain('原价');
  expect(tableHeaders).toContain('售价');
  expect(tableHeaders).toContain('操作');
  
  console.log('✅ 套餐列表显示正常');
  
  // 4. 测试筛选功能
  console.log('4. 测试筛选功能...');
  
  // 测试类型筛选
  await page.click('.filter-card .el-select');
  await page.waitForTimeout(500);
  await page.click('text=固定时长');
  await page.waitForTimeout(2000);
  
  // 检查筛选结果
  const filteredRows = await page.locator('.el-table tbody tr').count();
  console.log(`筛选后显示 ${filteredRows} 条固定时长套餐`);
  
  // 重置筛选
  await page.click('text=重置');
  await page.waitForTimeout(2000);
  
  console.log('✅ 筛选功能正常');
  
  // 5. 测试新增套餐按钮
  console.log('5. 测试新增套餐按钮...');
  
  await page.click('text=新增套餐');
  await page.waitForTimeout(2000);
  
  // 检查是否跳转到创建页面
  expect(page.url()).toContain('/packages/create');
  await expect(page.locator('h2')).toContainText('新增套餐');
  
  console.log('✅ 新增套餐页面跳转正常');
  
  // 6. 测试套餐创建表单
  console.log('6. 测试套餐创建表单...');
  
  // 检查表单字段
  await expect(page.locator('input[placeholder="请输入套餐名称"]')).toBeVisible();
  await expect(page.locator('.el-select')).toBeVisible();
  
  // 填写表单
  await page.fill('input[placeholder="请输入套餐名称"]', '测试套餐');
  
  // 选择套餐类型
  await page.click('.el-select');
  await page.waitForTimeout(500);
  await page.click('text=固定时长套餐');
  await page.waitForTimeout(1000);
  
  // 检查时长输入框是否显示
  await expect(page.locator('input[placeholder="小时"]')).toBeVisible();
  
  console.log('✅ 套餐创建表单功能正常');
  
  // 7. 返回套餐列表
  console.log('7. 返回套餐列表...');
  
  await page.click('text=返回');
  await page.waitForTimeout(2000);
  
  expect(page.url()).toContain('/packages');
  await expect(page.locator('h2')).toContainText('套餐管理');
  
  console.log('✅ 返回套餐列表成功');
  
  // 8. 测试套餐详情查看
  console.log('8. 测试套餐详情查看...');
  
  // 点击第一个套餐的详情按钮
  const detailButton = page.locator('.el-table tbody tr').first().locator('text=详情');
  if (await detailButton.count() > 0) {
    await detailButton.click();
    await page.waitForTimeout(2000);
    
    // 检查详情页面
    expect(page.url()).toContain('/packages/detail/');
    await expect(page.locator('h2')).toContainText('套餐详情');
    
    // 检查详情内容
    await expect(page.locator('.info-card')).toBeVisible();
    await expect(page.locator('.stats-card')).toBeVisible();
    await expect(page.locator('.price-card')).toBeVisible();
    
    console.log('✅ 套餐详情页面显示正常');
    
    // 返回列表
    await page.click('text=返回');
    await page.waitForTimeout(2000);
  }
  
  // 9. 测试状态切换
  console.log('9. 测试状态切换...');
  
  // 查找状态开关
  const statusSwitch = page.locator('.el-table tbody tr').first().locator('.el-switch');
  if (await statusSwitch.count() > 0) {
    const initialState = await statusSwitch.getAttribute('aria-checked');
    console.log(`初始状态: ${initialState}`);
    
    // 注意：实际点击可能会调用API，这里只检查元素存在
    await expect(statusSwitch).toBeVisible();
    console.log('✅ 状态切换开关显示正常');
  }
  
  // 10. 检查分页功能
  console.log('10. 检查分页功能...');
  
  const pagination = page.locator('.pagination-wrapper .el-pagination');
  if (await pagination.count() > 0) {
    await expect(pagination).toBeVisible();
    
    // 检查分页信息
    const paginationText = await pagination.textContent();
    console.log(`分页信息: ${paginationText}`);
    
    console.log('✅ 分页功能显示正常');
  }
  
  // 11. 测试响应式设计
  console.log('11. 测试响应式设计...');
  
  // 测试不同屏幕尺寸
  await page.setViewportSize({ width: 1200, height: 800 });
  await page.waitForTimeout(1000);
  
  await expect(page.locator('.stats-cards')).toBeVisible();
  await expect(page.locator('.el-table')).toBeVisible();
  
  // 测试小屏幕
  await page.setViewportSize({ width: 768, height: 600 });
  await page.waitForTimeout(1000);
  
  await expect(page.locator('.page-header')).toBeVisible();
  
  // 恢复正常尺寸
  await page.setViewportSize({ width: 1920, height: 1080 });
  await page.waitForTimeout(1000);
  
  console.log('✅ 响应式设计正常');
  
  console.log('=== 套餐管理前端功能测试完成 ===');
  console.log('🎉 所有前端功能都正常工作！');
  
  // 总结测试结果
  console.log('\n📊 前端测试总结:');
  console.log(`- 套餐列表页面: ✅ 正常`);
  console.log(`- 统计卡片显示: ✅ 正常`);
  console.log(`- 套餐列表表格: ✅ 正常`);
  console.log(`- 筛选功能: ✅ 正常`);
  console.log(`- 新增套餐页面: ✅ 正常`);
  console.log(`- 套餐创建表单: ✅ 正常`);
  console.log(`- 套餐详情页面: ✅ 正常`);
  console.log(`- 状态切换: ✅ 正常`);
  console.log(`- 分页功能: ✅ 正常`);
  console.log(`- 响应式设计: ✅ 正常`);
});
