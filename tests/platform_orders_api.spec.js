const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:8080';
const API_BASE = `${BASE_URL}/api/v1/admin/platform`;

test.describe('外卖平台订单管理API测试', () => {
  
  test('获取平台订单列表 - 基础功能', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.message).toBe('success');
    expect(data.data).toHaveProperty('data');
    expect(data.data).toHaveProperty('total');
    expect(data.data).toHaveProperty('stats');
    
    // 验证订单数据结构
    const orders = data.data.data;
    expect(Array.isArray(orders)).toBe(true);
    
    if (orders.length > 0) {
      const order = orders[0];
      expect(order).toHaveProperty('id');
      expect(order).toHaveProperty('platformType');
      expect(order).toHaveProperty('platformOrderId');
      expect(order).toHaveProperty('roomNumber');
      expect(order).toHaveProperty('customerName');
      expect(order).toHaveProperty('paidAmount');
      expect(order).toHaveProperty('verificationStatus');
    }
    
    // 验证统计数据结构
    const stats = data.data.stats;
    expect(stats).toHaveProperty('meituan');
    expect(stats).toHaveProperty('eleme');
    expect(stats).toHaveProperty('pending');
    expect(stats).toHaveProperty('verified');
    
    expect(stats.meituan).toHaveProperty('count');
    expect(stats.meituan).toHaveProperty('amount');
  });

  test('获取平台订单列表 - 分页功能', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders?page=1&page_size=2`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.data.page).toBe(1);
    expect(data.data.page_size).toBe(2);
    expect(data.data.data.length).toBeLessThanOrEqual(2);
  });

  test('获取平台订单列表 - 平台类型筛选', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders?platformType=meituan`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const orders = data.data.data;
    
    // 验证所有订单都是美团订单
    orders.forEach(order => {
      expect(order.platformType).toBe('meituan');
    });
  });

  test('获取平台订单列表 - 核销状态筛选', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders?verificationStatus=pending`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const orders = data.data.data;
    
    // 验证所有订单都是待核销状态
    if (orders && orders.length > 0) {
      orders.forEach(order => {
        expect(order.verificationStatus).toBe('pending');
      });
    }
  });

  test('获取平台订单列表 - 房间号筛选', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders?roomNumber=A01`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const orders = data.data.data;
    
    // 验证所有订单都是A01房间的
    orders.forEach(order => {
      expect(order.roomNumber).toBe('A01');
    });
  });

  test('获取平台订单详情', async ({ request }) => {
    // 首先获取订单列表，取第一个订单的ID
    const listResponse = await request.get(`${API_BASE}/orders`);
    const listData = await listResponse.json();
    
    if (listData.data.data.length > 0) {
      const orderId = listData.data.data[0].id;
      
      const response = await request.get(`${API_BASE}/orders/${orderId}`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.code).toBe(200);
      expect(data.data).toHaveProperty('id');
      expect(data.data.id).toBe(orderId);
    }
  });

  test('获取待核销订单列表', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders/pending`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    // API可能返回null（无数据）或数组，都是正常的
    expect(data.data === null || Array.isArray(data.data)).toBe(true);
    
    // 验证所有订单都是待核销状态
    if (data.data && data.data.length > 0) {
      data.data.forEach(order => {
        expect(order.verificationStatus).toBe('pending');
      });
    }
  });

  test('核销订单功能', async ({ request }) => {
    // 首先获取一个待核销订单
    const pendingResponse = await request.get(`${API_BASE}/orders/pending`);
    const pendingData = await pendingResponse.json();
    
    if (pendingData.data && pendingData.data.length > 0) {
      const order = pendingData.data[0];

      const verifyResponse = await request.post(`${API_BASE}/verify`, {
        data: {
          platformOrderId: order.platformOrderId,
          platformType: order.platformType,
          verificationMethod: 'manual'
        }
      });
      
      expect(verifyResponse.status()).toBe(200);
      
      const verifyData = await verifyResponse.json();
      expect(verifyData.code).toBe(200);
      expect(verifyData.data.message).toContain('核销成功');
    }
  });

  test('获取核销统计数据', async ({ request }) => {
    const response = await request.get(`${API_BASE}/verification/stats`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('today');
    expect(data.data).toHaveProperty('pending');
    expect(data.data).toHaveProperty('efficiency');
    
    expect(data.data.today).toHaveProperty('verified');
    expect(data.data.today).toHaveProperty('trend');
    expect(data.data.pending).toHaveProperty('count');
    expect(data.data.pending).toHaveProperty('amount');
  });

  test('获取核销历史', async ({ request }) => {
    const response = await request.get(`${API_BASE}/verification/history?filter=today`);

    expect(response.status()).toBe(200);

    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    expect(data.data).toHaveProperty('total');

    // 处理空数据的情况
    if (data.data.data !== null) {
      expect(Array.isArray(data.data.data)).toBe(true);
    } else {
      expect(data.data.total).toBe(0);
    }
  });

  test('获取平台分析数据', async ({ request }) => {
    const response = await request.get(`${API_BASE}/analytics`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('summary');
    expect(data.data).toHaveProperty('revenueChart');
    expect(data.data).toHaveProperty('platformComparison');
    
    const summary = data.data.summary;
    expect(summary).toHaveProperty('totalRevenue');
    expect(summary).toHaveProperty('totalOrders');
    expect(summary).toHaveProperty('avgOrderValue');
    expect(summary).toHaveProperty('conversionRate');
  });

  test('获取房间关联分析', async ({ request }) => {
    const response = await request.get(`${API_BASE}/room-analysis`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(Array.isArray(data.data)).toBe(true);
    
    if (data.data.length > 0) {
      const roomAnalysis = data.data[0];
      expect(roomAnalysis).toHaveProperty('roomNumber');
      expect(roomAnalysis).toHaveProperty('roomName');
      expect(roomAnalysis).toHaveProperty('totalOrders');
      expect(roomAnalysis).toHaveProperty('totalRevenue');
      expect(roomAnalysis).toHaveProperty('avgOrderValue');
    }
  });

  test('错误处理 - 无效订单ID', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders/99999`);
    
    // 应该返回错误或空结果
    expect([200, 404, 500]).toContain(response.status());
  });

  test('错误处理 - 无效核销请求', async ({ request }) => {
    const response = await request.post(`${API_BASE}/verify`, {
      data: {
        platformOrderId: 'INVALID_ORDER_ID',
        platformType: 'meituan'
      }
    });
    
    // 应该返回错误
    expect([400, 404, 500]).toContain(response.status());
  });

  test('数据一致性 - 统计数据与实际数据匹配', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders`);
    const data = await response.json();
    
    const orders = data.data.data;
    const stats = data.data.stats;
    
    // 计算实际的美团订单数量和金额
    const meituanOrders = orders.filter(order => order.platformType === 'meituan');
    const meituanAmount = meituanOrders.reduce((sum, order) => sum + order.paidAmount, 0);
    
    // 验证统计数据的准确性（允许一定误差，因为可能有分页）
    if (data.data.total <= data.data.page_size) {
      expect(stats.meituan.count).toBe(meituanOrders.length);
      expect(Math.abs(stats.meituan.amount - meituanAmount)).toBeLessThan(0.01);
    }
  });

  test('房间关联数据完整性', async ({ request }) => {
    const response = await request.get(`${API_BASE}/orders`);
    const data = await response.json();
    
    const orders = data.data.data;
    
    // 验证所有订单都有房间号
    orders.forEach(order => {
      if (order.roomId) {
        expect(order.roomNumber).toBeTruthy();
        expect(typeof order.roomNumber).toBe('string');
        expect(order.roomNumber.length).toBeGreaterThan(0);
      }
    });
  });
});
