// 房间删除功能修复验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🔧 房间删除功能修复验证', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始房间删除功能修复验证');
    console.log('🎯 验证目标: 房间删除功能正常工作，错误提示友好');
  });

  // ==================== 测试1: 删除不存在的房间 ====================
  test('✅ 测试1: 删除不存在的房间', async ({ request }) => {
    console.log('🔧 测试删除不存在的房间');
    
    const response = await request.delete(`${API_BASE}/admin/rooms/99999`);
    expect(response.status()).toBe(500);
    
    const data = await response.json();
    expect(data.message).toBe('房间不存在');
    
    console.log('✅ 测试1通过: 正确处理不存在的房间');
  });

  // ==================== 测试2: 删除有关联数据的房间 ====================
  test('✅ 测试2: 删除有关联数据的房间', async ({ request }) => {
    console.log('🔧 测试删除有关联数据的房间');
    
    // 尝试删除房间5（有设备、预约、外卖订单关联）
    const response = await request.delete(`${API_BASE}/admin/rooms/5`);
    expect(response.status()).toBe(500);
    
    const data = await response.json();
    expect(data.message).toBe('无法删除房间：房间仍有关联数据。请先删除相关的设备、预约或订单记录');
    
    console.log('✅ 测试2通过: 正确阻止删除有关联数据的房间，错误信息友好');
  });

  // ==================== 测试3: 成功删除无关联数据的房间 ====================
  test('✅ 测试3: 成功删除无关联数据的房间', async ({ request }) => {
    console.log('🔧 测试成功删除无关联数据的房间');
    
    // 先创建一个测试房间
    const createResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'DELETE_TEST_' + Date.now(),
        name: '删除测试房间',
        description: '用于测试删除功能',
        pricing_rule_id: 1
      }
    });
    
    expect(createResponse.status()).toBe(200);
    const createData = await createResponse.json();
    const roomId = createData.data.id;
    
    console.log(`📝 创建测试房间成功，ID: ${roomId}`);
    
    // 删除刚创建的房间
    const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteResponse.status()).toBe(200);
    
    const deleteData = await deleteResponse.json();
    expect(deleteData.code).toBe(200);
    expect(deleteData.data.message).toBe('删除成功');
    
    // 验证房间确实被删除了
    const getResponse = await request.get(`${API_BASE}/admin/rooms/${roomId}`);
    expect(getResponse.status()).toBe(500);
    
    console.log('✅ 测试3通过: 成功删除无关联数据的房间');
  });

  // ==================== 测试4: 删除有活跃订单的房间 ====================
  test('✅ 测试4: 删除有活跃订单的房间', async ({ request }) => {
    console.log('🔧 测试删除有活跃订单的房间');
    
    // 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'delete_test_user_' + Date.now(),
        nickname: '删除测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const userId = userData.data.id;
    
    // 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'ACTIVE_ORDER_TEST_' + Date.now(),
        name: '活跃订单测试房间',
        description: '用于测试有活跃订单时的删除',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const roomId = roomData.data.id;
    
    // 创建活跃订单
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': userId.toString() },
      data: {
        room_id: roomId,
        total_amount: 50.00
      }
    });
    expect(orderResponse.status()).toBe(200);
    const orderData = await orderResponse.json();
    const orderId = orderData.data.id;
    
    console.log(`📝 创建活跃订单成功，ID: ${orderId}`);
    
    // 尝试删除有活跃订单的房间
    const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteResponse.status()).toBe(500);
    
    const deleteData = await deleteResponse.json();
    expect(deleteData.message).toBe('房间有活跃订单，无法删除。请先完成或取消房间内的订单');
    
    // 清理：结束订单
    await request.post(`${API_BASE}/orders/${orderId}/complete`);
    
    // 现在应该可以删除房间了
    const deleteAfterEndResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteAfterEndResponse.status()).toBe(200);
    
    console.log('✅ 测试4通过: 正确阻止删除有活跃订单的房间，订单结束后可以删除');
  });

  // ==================== 测试5: 验证错误处理增强 ====================
  test('✅ 测试5: 验证错误处理增强', async ({ request }) => {
    console.log('🔧 验证错误处理增强');
    
    // 测试无效的房间ID格式
    const invalidIdResponse = await request.delete(`${API_BASE}/admin/rooms/invalid`);
    expect(invalidIdResponse.status()).toBe(400);
    
    const invalidIdData = await invalidIdResponse.json();
    expect(invalidIdData.message).toBe('房间ID格式错误');
    
    // 测试负数房间ID
    const negativeIdResponse = await request.delete(`${API_BASE}/admin/rooms/-1`);
    expect(negativeIdResponse.status()).toBe(500);
    
    console.log('✅ 测试5通过: 错误处理机制完善');
  });

  // ==================== 测试6: 批量操作场景 ====================
  test('✅ 测试6: 批量操作场景', async ({ request }) => {
    console.log('🔧 测试批量操作场景');
    
    // 创建多个测试房间
    const roomIds = [];
    for (let i = 0; i < 3; i++) {
      const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
        data: {
          room_number: `BATCH_TEST_${Date.now()}_${i}`,
          name: `批量测试房间${i + 1}`,
          description: '用于批量删除测试',
          pricing_rule_id: 1
        }
      });
      expect(roomResponse.status()).toBe(200);
      const roomData = await roomResponse.json();
      roomIds.push(roomData.data.id);
    }
    
    console.log(`📝 创建${roomIds.length}个测试房间: ${roomIds.join(', ')}`);
    
    // 逐个删除房间
    for (const roomId of roomIds) {
      const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
      expect(deleteResponse.status()).toBe(200);
      
      const deleteData = await deleteResponse.json();
      expect(deleteData.data.message).toBe('删除成功');
    }
    
    console.log('✅ 测试6通过: 批量删除操作正常');
  });

  test.afterAll(async () => {
    console.log('🏁 房间删除功能修复验证完成');
    console.log('📋 修复验证总结:');
    console.log('  ✅ 测试1: 删除不存在的房间 - 正确处理');
    console.log('  ✅ 测试2: 删除有关联数据的房间 - 正确阻止，错误信息友好');
    console.log('  ✅ 测试3: 删除无关联数据的房间 - 成功删除');
    console.log('  ✅ 测试4: 删除有活跃订单的房间 - 正确阻止，订单结束后可删除');
    console.log('  ✅ 测试5: 错误处理增强 - 机制完善');
    console.log('  ✅ 测试6: 批量操作场景 - 正常工作');
    console.log('🎉 房间删除功能已完全修复！');
  });

});
