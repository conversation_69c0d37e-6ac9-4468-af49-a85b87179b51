const { test, expect } = require('@playwright/test');

test('套餐管理API功能测试', async ({ page }) => {
  console.log('=== 套餐管理API功能测试 ===');
  
  const baseURL = 'http://localhost:8080';
  
  // 1. 测试获取套餐列表（用户端）
  console.log('1. 测试获取套餐列表（用户端）...');
  const packagesResponse = await page.request.get(`${baseURL}/api/v1/packages`);
  expect(packagesResponse.status()).toBe(200);
  
  const packagesData = await packagesResponse.json();
  console.log('套餐列表响应:', packagesData);
  
  expect(packagesData.code).toBe(200);
  expect(packagesData.data).toBeInstanceOf(Array);
  expect(packagesData.data.length).toBeGreaterThan(0);
  
  // 验证套餐数据结构
  const firstPackage = packagesData.data[0];
  expect(firstPackage).toHaveProperty('id');
  expect(firstPackage).toHaveProperty('name');
  expect(firstPackage).toHaveProperty('type');
  expect(firstPackage).toHaveProperty('original_price');
  expect(firstPackage).toHaveProperty('sale_price');
  expect(firstPackage).toHaveProperty('features');
  
  console.log('✅ 用户端套餐列表获取成功');
  
  // 2. 测试获取套餐详情
  console.log('2. 测试获取套餐详情...');
  const packageDetailResponse = await page.request.get(`${baseURL}/api/v1/packages/${firstPackage.id}`);
  expect(packageDetailResponse.status()).toBe(200);
  
  const packageDetailData = await packageDetailResponse.json();
  console.log('套餐详情响应:', packageDetailData);
  
  expect(packageDetailData.code).toBe(200);
  expect(packageDetailData.data.id).toBe(firstPackage.id);
  expect(packageDetailData.data.name).toBe(firstPackage.name);
  
  console.log('✅ 套餐详情获取成功');
  
  // 3. 测试管理端套餐列表
  console.log('3. 测试管理端套餐列表...');
  const adminPackagesResponse = await page.request.get(`${baseURL}/api/v1/admin/packages`);
  expect(adminPackagesResponse.status()).toBe(200);
  
  const adminPackagesData = await adminPackagesResponse.json();
  console.log('管理端套餐列表响应:', adminPackagesData);
  
  expect(adminPackagesData.code).toBe(200);
  expect(adminPackagesData.data).toHaveProperty('list');
  expect(adminPackagesData.data).toHaveProperty('total');
  expect(adminPackagesData.data).toHaveProperty('page');
  expect(adminPackagesData.data).toHaveProperty('page_size');
  
  console.log('✅ 管理端套餐列表获取成功');
  
  // 4. 测试套餐统计
  console.log('4. 测试套餐统计...');
  const statsResponse = await page.request.get(`${baseURL}/api/v1/admin/packages/stats`);
  expect(statsResponse.status()).toBe(200);
  
  const statsData = await statsResponse.json();
  console.log('套餐统计响应:', statsData);
  
  expect(statsData.code).toBe(200);
  expect(statsData.data).toHaveProperty('total');
  expect(statsData.data).toHaveProperty('active');
  expect(statsData.data).toHaveProperty('total_sales');
  expect(statsData.data).toHaveProperty('total_revenue');
  
  console.log('✅ 套餐统计获取成功');
  
  // 5. 测试套餐类型筛选
  console.log('5. 测试套餐类型筛选...');
  const fixedPackagesResponse = await page.request.get(`${baseURL}/api/v1/packages?type=fixed_duration`);
  expect(fixedPackagesResponse.status()).toBe(200);
  
  const fixedPackagesData = await fixedPackagesResponse.json();
  console.log('固定时长套餐响应:', fixedPackagesData);
  
  expect(fixedPackagesData.code).toBe(200);
  expect(fixedPackagesData.data).toBeInstanceOf(Array);
  
  // 验证所有返回的套餐都是固定时长类型
  fixedPackagesData.data.forEach(pkg => {
    expect(pkg.type).toBe('fixed_duration');
    expect(pkg.duration_hours).toBeGreaterThan(0);
  });
  
  console.log('✅ 固定时长套餐筛选成功');
  
  // 6. 测试灵活续费套餐筛选
  console.log('6. 测试灵活续费套餐筛选...');
  const flexiblePackagesResponse = await page.request.get(`${baseURL}/api/v1/packages?type=flexible_recharge`);
  expect(flexiblePackagesResponse.status()).toBe(200);
  
  const flexiblePackagesData = await flexiblePackagesResponse.json();
  console.log('灵活续费套餐响应:', flexiblePackagesData);
  
  expect(flexiblePackagesData.code).toBe(200);
  expect(flexiblePackagesData.data).toBeInstanceOf(Array);
  
  // 验证所有返回的套餐都是灵活续费类型
  flexiblePackagesData.data.forEach(pkg => {
    expect(pkg.type).toBe('flexible_recharge');
    expect(pkg.duration_hours).toBeNull();
    expect(pkg.min_recharge_hours).toBeGreaterThan(0);
    expect(pkg.max_recharge_hours).toBeGreaterThan(pkg.min_recharge_hours);
  });
  
  console.log('✅ 灵活续费套餐筛选成功');
  
  // 7. 测试套餐数据完整性
  console.log('7. 测试套餐数据完整性...');
  
  const allPackages = packagesData.data;
  console.log(`总共有 ${allPackages.length} 个套餐`);
  
  allPackages.forEach((pkg, index) => {
    console.log(`套餐 ${index + 1}: ${pkg.name}`);
    console.log(`  - 类型: ${pkg.type}`);
    console.log(`  - 原价: ¥${pkg.original_price}`);
    console.log(`  - 售价: ¥${pkg.sale_price}`);
    console.log(`  - 折扣: ${pkg.discount_rate}%`);
    console.log(`  - 特色: ${pkg.features.join(', ')}`);
    console.log(`  - 有效期: ${pkg.valid_days}天`);
    
    if (pkg.type === 'fixed_duration') {
      console.log(`  - 时长: ${pkg.duration_hours}小时`);
    } else {
      console.log(`  - 续费范围: ${pkg.min_recharge_hours}-${pkg.max_recharge_hours}小时`);
    }
    
    // 验证价格逻辑
    expect(pkg.sale_price).toBeLessThanOrEqual(pkg.original_price);
    expect(pkg.discount_rate).toBeGreaterThanOrEqual(0);
    expect(pkg.discount_rate).toBeLessThanOrEqual(100);
    
    // 验证特色功能
    expect(pkg.features).toBeInstanceOf(Array);
    expect(pkg.features.length).toBeGreaterThan(0);
    
    // 验证有效期
    expect(pkg.valid_days).toBeGreaterThan(0);
  });
  
  console.log('✅ 套餐数据完整性验证通过');
  
  // 8. 测试错误处理
  console.log('8. 测试错误处理...');
  
  // 测试不存在的套餐ID
  const notFoundResponse = await page.request.get(`${baseURL}/api/v1/packages/99999`);
  expect(notFoundResponse.status()).toBe(500); // 应该返回错误
  
  console.log('✅ 错误处理测试通过');
  
  console.log('=== 套餐管理API功能测试完成 ===');
  console.log('🎉 所有测试都通过了！套餐管理功能正常工作！');
  
  // 总结测试结果
  console.log('\n📊 测试总结:');
  console.log(`- 套餐总数: ${allPackages.length}`);
  console.log(`- 固定时长套餐: ${fixedPackagesData.data.length}`);
  console.log(`- 灵活续费套餐: ${flexiblePackagesData.data.length}`);
  console.log(`- 统计数据: 总计${statsData.data.total}个套餐，${statsData.data.active}个启用，总销量${statsData.data.total_sales}，总收入¥${statsData.data.total_revenue}`);
});
