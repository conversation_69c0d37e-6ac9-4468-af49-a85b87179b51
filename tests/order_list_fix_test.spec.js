// 订单列表修复验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🎯 订单列表修复验证', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始订单列表修复验证');
    console.log('🎯 验证目标: 订单列表API正常工作，支持可空的room_id');
  });

  // ==================== 测试1: 基本订单列表查询 ====================
  test('✅ 基本订单列表查询', async ({ request }) => {
    console.log('🔧 测试基本订单列表查询');
    
    const response = await request.get(`${API_BASE}/orders`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    expect(data.data).toHaveProperty('total');
    expect(data.data).toHaveProperty('page');
    expect(data.data).toHaveProperty('page_size');
    
    console.log(`📊 订单总数: ${data.data.total}`);
    console.log(`📄 当前页: ${data.data.page}/${data.data.total_pages}`);
    console.log(`✅ 基本查询正常`);
  });

  // ==================== 测试2: 分页查询 ====================
  test('✅ 分页查询', async ({ request }) => {
    console.log('🔧 测试分页查询');
    
    const response = await request.get(`${API_BASE}/orders?page=1&page_size=5`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.data.data.length).toBeLessThanOrEqual(5);
    
    console.log(`📄 返回${data.data.data.length}条记录`);
    console.log(`✅ 分页查询正常`);
  });

  // ==================== 测试3: 处理可空room_id的订单 ====================
  test('✅ 处理可空room_id的订单', async ({ request }) => {
    console.log('🔧 测试处理可空room_id的订单');
    
    const response = await request.get(`${API_BASE}/orders?page=1&page_size=20`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const orders = data.data.data;
    
    // 查找有null room_id的订单
    const nullRoomOrders = orders.filter(order => order.room_id === null);
    
    if (nullRoomOrders.length > 0) {
      console.log(`📝 找到${nullRoomOrders.length}个room_id为null的订单`);
      
      // 验证这些订单的其他字段正常
      nullRoomOrders.forEach(order => {
        expect(order.id).toBeDefined();
        expect(order.user_id).toBeDefined();
        expect(order.status).toBeDefined();
        expect(order.total_amount).toBeDefined();
        expect(order.room_id).toBeNull();
        
        // 验证用户信息正常关联
        if (order.user) {
          expect(order.user.id).toBe(order.user_id);
          expect(order.user.nickname).toBeDefined();
        }
        
        // room字段应该为空或不存在
        expect(order.room).toBeUndefined();
      });
      
      console.log(`✅ 可空room_id的订单处理正常`);
    } else {
      console.log(`📝 当前没有room_id为null的订单`);
    }
  });

  // ==================== 测试4: 创建订单并删除房间后验证 ====================
  test('✅ 创建订单并删除房间后验证', async ({ request }) => {
    console.log('🔧 测试创建订单并删除房间后的订单列表');
    
    // 1. 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'order_list_test_user_' + Date.now(),
        nickname: '订单列表测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const userId = userData.data.id;
    
    // 2. 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'ORDER_LIST_TEST_' + Date.now(),
        name: '订单列表测试房间',
        description: '用于测试订单列表功能',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const roomId = roomData.data.id;
    
    // 3. 创建订单
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': userId.toString() },
      data: {
        room_id: roomId,
        total_amount: 60.00
      }
    });
    expect(orderResponse.status()).toBe(200);
    const orderData = await orderResponse.json();
    const orderId = orderData.data.id;
    
    console.log(`📝 创建订单成功，ID: ${orderId}`);
    
    // 4. 取消订单（变成历史订单）
    const cancelResponse = await request.post(`${API_BASE}/orders/${orderId}/cancel`);
    expect(cancelResponse.status()).toBe(200);
    
    // 5. 删除房间
    const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteResponse.status()).toBe(200);
    
    console.log(`📝 房间删除成功`);
    
    // 6. 验证订单列表中该订单的room_id为null
    const listResponse = await request.get(`${API_BASE}/orders?page=1&page_size=50`);
    expect(listResponse.status()).toBe(200);
    
    const listData = await listResponse.json();
    const targetOrder = listData.data.data.find(order => order.id === orderId);
    
    expect(targetOrder).toBeDefined();
    expect(targetOrder.room_id).toBeNull();
    expect(targetOrder.user_id).toBe(userId);
    expect(targetOrder.status).toBe('cancelled');
    
    console.log(`✅ 删除房间后订单列表正常，room_id为null`);
  });

  // ==================== 测试5: 管理端订单列表 ====================
  test('✅ 管理端订单列表', async ({ request }) => {
    console.log('🔧 测试管理端订单列表');
    
    const response = await request.get(`${API_BASE}/admin/orders?page=1&page_size=10`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    
    console.log(`📊 管理端订单总数: ${data.data.total}`);
    console.log(`✅ 管理端订单列表正常`);
  });

  // ==================== 测试6: 订单状态过滤 ====================
  test('✅ 订单状态过滤', async ({ request }) => {
    console.log('🔧 测试订单状态过滤');
    
    // 测试取消状态的订单
    const cancelledResponse = await request.get(`${API_BASE}/orders?status=cancelled&page_size=5`);
    expect(cancelledResponse.status()).toBe(200);
    
    const cancelledData = await cancelledResponse.json();
    const cancelledOrders = cancelledData.data.data;
    
    // 验证所有返回的订单都是cancelled状态
    cancelledOrders.forEach(order => {
      expect(order.status).toBe('cancelled');
    });
    
    console.log(`📝 找到${cancelledOrders.length}个已取消的订单`);
    console.log(`✅ 状态过滤正常`);
  });

  test.afterAll(async () => {
    console.log('🏁 订单列表修复验证完成');
    console.log('📋 验证总结:');
    console.log('  ✅ 基本订单列表查询 - 正常');
    console.log('  ✅ 分页查询 - 正常');
    console.log('  ✅ 可空room_id处理 - 正常');
    console.log('  ✅ 删除房间后订单列表 - 正常');
    console.log('  ✅ 管理端订单列表 - 正常');
    console.log('  ✅ 订单状态过滤 - 正常');
    console.log('🎉 订单列表功能完全修复！');
    console.log('💡 现在支持可空的room_id，删除房间不会影响订单列表显示');
  });

});
