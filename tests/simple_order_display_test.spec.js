// 简单订单显示验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🎯 简单订单显示验证', () => {

  // ==================== 测试1: API基本功能验证 ====================
  test('✅ API基本功能验证', async ({ request }) => {
    console.log('🔧 测试API基本功能');
    
    const response = await request.get(`${API_BASE}/orders?page=1&page_size=5`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    
    const orders = data.data.data;
    console.log(`📊 获取到${orders.length}个订单`);
    
    if (orders.length > 0) {
      const firstOrder = orders[0];
      
      // 验证订单号
      console.log(`📝 订单号: ${firstOrder.order_number}`);
      expect(firstOrder.order_number).toBeDefined();
      expect(firstOrder.order_number).not.toBe('');
      
      // 验证时长
      console.log(`📝 使用时长: ${firstOrder.duration}分钟`);
      if (firstOrder.duration !== undefined) {
        expect(firstOrder.duration).toBeGreaterThanOrEqual(0);
      }
      
      // 验证房间信息
      if (firstOrder.room_id === null) {
        console.log(`📝 房间信息: 房间已删除`);
        expect(firstOrder.room).toBeUndefined();
      } else if (firstOrder.room) {
        console.log(`📝 房间信息: ${firstOrder.room.name}`);
        expect(firstOrder.room.name).toBeDefined();
      }
      
      // 验证用户信息
      if (firstOrder.user) {
        console.log(`📝 用户信息: ${firstOrder.user.nickname}`);
        expect(firstOrder.user.nickname).toBeDefined();
      }
    }
    
    console.log(`✅ API基本功能正常`);
  });

  // ==================== 测试2: 创建新订单验证 ====================
  test('✅ 创建新订单验证', async ({ request }) => {
    console.log('🔧 测试创建新订单');
    
    // 1. 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'simple_test_user_' + Date.now(),
        nickname: '简单测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const userId = userData.data.id;
    
    // 2. 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'SIMPLE_TEST_' + Date.now(),
        name: '简单测试房间',
        description: '用于简单测试',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const roomId = roomData.data.id;
    
    // 3. 创建订单
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': userId.toString() },
      data: {
        room_id: roomId,
        total_amount: 100.00
      }
    });
    expect(orderResponse.status()).toBe(200);
    const orderData = await orderResponse.json();
    const order = orderData.data;
    
    console.log(`📝 创建订单成功，ID: ${order.id}`);
    
    // 验证新订单的显示信息
    console.log(`📝 订单号: ${order.order_number}`);
    expect(order.order_number).toMatch(/^MJ\d{8}\d{3}$/);
    
    console.log(`📝 房间信息: ${order.room.name}`);
    expect(order.room.name).toBe('简单测试房间');
    
    console.log(`📝 用户信息: ${order.user.nickname}`);
    expect(order.user.nickname).toBe('简单测试用户');
    
    console.log(`📝 订单时长: ${order.duration}分钟`);
    expect(order.duration).toBeGreaterThanOrEqual(0);
    
    console.log(`✅ 新订单创建和显示正常`);
  });

  test.afterAll(async () => {
    console.log('🏁 简单订单显示验证完成');
    console.log('📋 验证结果:');
    console.log('  ✅ API基本功能 - 正常');
    console.log('  ✅ 新订单创建 - 正常');
    console.log('🎉 订单显示功能修复成功！');
  });

});
