const { test, expect } = require('@playwright/test');

test.describe('Dashboard数据验证测试', () => {
  test('验证Dashboard页面显示真实数据', async ({ page }) => {
    // 访问Dashboard页面
    await page.goto('http://localhost:3001');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 等待数据加载
    await page.waitForTimeout(3000);
    
    // 截图查看当前状态
    await page.screenshot({ path: 'dashboard-current-state.png', fullPage: true });
    
    // 检查统计卡片数据
    console.log('=== 检查统计卡片数据 ===');
    
    // 检查今日收入
    const todayIncomeElements = await page.locator('text=/今日收入/').count();
    if (todayIncomeElements > 0) {
      const todayIncomeCard = await page.locator('text=/今日收入/').first().locator('..').locator('..');
      const incomeValue = await todayIncomeCard.locator('.stat-number').textContent();
      console.log('今日收入显示值:', incomeValue);
    }
    
    // 检查总房间数
    const totalRoomsElements = await page.locator('text=/总房间数/').count();
    if (totalRoomsElements > 0) {
      const totalRoomsCard = await page.locator('text=/总房间数/').first().locator('..').locator('..');
      const roomsValue = await totalRoomsCard.locator('.stat-number').textContent();
      console.log('总房间数显示值:', roomsValue);
    }
    
    // 检查今日订单
    const todayOrdersElements = await page.locator('text=/今日订单/').count();
    if (todayOrdersElements > 0) {
      const todayOrdersCard = await page.locator('text=/今日订单/').first().locator('..').locator('..');
      const ordersValue = await todayOrdersCard.locator('.stat-number').textContent();
      console.log('今日订单显示值:', ordersValue);
    }
    
    // 检查在线设备
    const onlineDevicesElements = await page.locator('text=/在线设备/').count();
    if (onlineDevicesElements > 0) {
      const onlineDevicesCard = await page.locator('text=/在线设备/').first().locator('..').locator('..');
      const devicesValue = await onlineDevicesCard.locator('.stat-number').textContent();
      console.log('在线设备显示值:', devicesValue);
    }
    
    // 检查API调用
    console.log('=== 检查API调用 ===');
    
    // 监听API调用
    const apiCalls = [];
    page.on('response', response => {
      if (response.url().includes('/api/v1/admin/')) {
        apiCalls.push({
          url: response.url(),
          status: response.status()
        });
      }
    });
    
    // 刷新页面触发API调用
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    console.log('API调用记录:', apiCalls);
    
    // 检查控制台错误
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleMessages.push(msg.text());
      }
    });
    
    await page.waitForTimeout(1000);
    
    if (consoleMessages.length > 0) {
      console.log('控制台错误:', consoleMessages);
    } else {
      console.log('没有控制台错误');
    }
    
    // 检查网络错误
    const networkErrors = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        networkErrors.push({
          url: response.url(),
          status: response.status()
        });
      }
    });
    
    await page.waitForTimeout(1000);
    
    if (networkErrors.length > 0) {
      console.log('网络错误:', networkErrors);
    } else {
      console.log('没有网络错误');
    }
    
    // 检查页面内容
    console.log('=== 检查页面内容 ===');
    
    const pageTitle = await page.title();
    console.log('页面标题:', pageTitle);
    
    const bodyText = await page.locator('body').textContent();
    const hasData = bodyText.includes('今日收入') && bodyText.includes('总房间数');
    console.log('页面包含基本数据:', hasData);
    
    // 检查Vue应用是否正常加载
    const vueApp = await page.locator('#app').count();
    console.log('Vue应用容器存在:', vueApp > 0);
    
    // 最终截图
    await page.screenshot({ path: 'dashboard-final-state.png', fullPage: true });
    
    console.log('=== 数据验证完成 ===');
  });
});
