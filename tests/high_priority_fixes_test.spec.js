// 高优先级问题修复验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

// 测试数据
const testData = {
  user: {
    openid: 'fix_test_user_' + Date.now(),
    nickname: '修复测试用户',
    avatar_url: 'https://example.com/avatar.jpg',
    phone: '13900139000'
  },
  room: {
    room_number: 'FIX_TEST_' + Date.now(),
    name: '修复测试房间',
    description: '用于验证修复效果的测试房间',
    pricing_rule_id: 1
  }
};

let testUserId = null;
let testRoomId = null;
let testOrderId = null;

test.describe('🔧 高优先级问题修复验证', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始高优先级问题修复验证');
    console.log('🎯 验证目标: 订单API数据结构、外键约束、唯一约束、参数验证');
  });

  // ==================== 问题1: 订单API数据结构修复验证 ====================
  test('✅ 问题1: 订单API数据结构修复验证', async ({ request }) => {
    console.log('🔧 验证订单API返回数据结构修复');
    
    // 先创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: testData.user
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    testUserId = userData.data.id;
    console.log(`✅ 测试用户创建成功，ID: ${testUserId}`);
    
    // 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: testData.room
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    testRoomId = roomData.data.id;
    console.log(`✅ 测试房间创建成功，ID: ${testRoomId}`);
    
    // 创建订单并验证返回数据结构
    const orderData = {
      room_id: testRoomId,
      total_amount: 50.00
    };
    
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: orderData
    });
    
    expect(orderResponse.status()).toBe(200);
    const orderResult = await orderResponse.json();
    expect(orderResult.code).toBe(200);
    
    // 验证修复后的数据结构包含user_id和room_id字段
    expect(orderResult.data).toHaveProperty('user_id');
    expect(orderResult.data).toHaveProperty('room_id');
    expect(orderResult.data.user_id).toBe(testUserId);
    expect(orderResult.data.room_id).toBe(testRoomId);
    
    testOrderId = orderResult.data.id;
    console.log('✅ 问题1修复验证成功: 订单API现在正确返回user_id和room_id字段');
  });

  // ==================== 问题2: 外键约束验证修复 ====================
  test('✅ 问题2: 外键约束验证修复', async ({ request }) => {
    console.log('🔧 验证外键约束修复');
    
    // 尝试创建订单时使用不存在的用户ID（应该失败）
    const invalidOrderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': '99999' }, // 不存在的用户ID
      data: {
        room_id: testRoomId,
        total_amount: 50.00
      }
    });
    
    // 应该返回错误，因为用户不存在
    expect(invalidOrderResponse.status()).toBe(500);
    const errorData = await invalidOrderResponse.json();
    expect(errorData.message).toContain('用户不存在');
    
    // 尝试创建订单时使用不存在的房间ID（应该失败）
    const invalidRoomOrderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        room_id: 99999, // 不存在的房间ID
        total_amount: 50.00
      }
    });
    
    // 应该返回错误，因为房间不存在
    expect(invalidRoomOrderResponse.status()).toBe(500);
    const roomErrorData = await invalidRoomOrderResponse.json();
    expect(roomErrorData.message).toContain('房间不存在');
    
    console.log('✅ 问题2修复验证成功: 外键约束现在正确工作');
  });

  // ==================== 问题3: 唯一约束处理修复 ====================
  test('✅ 问题3: 唯一约束处理修复', async ({ request }) => {
    console.log('🔧 验证唯一约束处理修复');
    
    // 尝试创建重复房间号的房间
    const duplicateRoomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: testData.room.room_number, // 使用相同的房间号
        name: '重复房间测试',
        description: '这是重复的房间',
        pricing_rule_id: 1
      }
    });
    
    // 现在应该返回409 Conflict状态码
    expect(duplicateRoomResponse.status()).toBe(409);
    const duplicateData = await duplicateRoomResponse.json();
    expect(duplicateData.message).toBe('房间号已存在');
    
    console.log('✅ 问题3修复验证成功: 唯一约束现在返回正确的错误状态码和消息');
  });

  // ==================== 问题4: API参数验证加强 ====================
  test('✅ 问题4: API参数验证加强', async ({ request }) => {
    console.log('🔧 验证API参数验证加强');
    
    // 测试充值金额验证
    console.log('💰 测试充值金额验证');
    
    // 测试负数金额
    const negativeAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: -50.00,
        payment_method: 'wechat'
      }
    });
    expect(negativeAmountResponse.status()).toBe(400);
    
    // 测试超大金额
    const largeAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 15000.00, // 超过10000限制
        payment_method: 'wechat'
      }
    });
    expect(largeAmountResponse.status()).toBe(400);
    
    // 测试无效支付方式
    const invalidPaymentResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 100.00,
        payment_method: 'invalid_method'
      }
    });
    expect(invalidPaymentResponse.status()).toBe(400);
    
    // 测试订单金额验证
    console.log('📋 测试订单金额验证');
    
    // 测试超大订单金额
    const largeOrderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        room_id: testRoomId,
        total_amount: 8000.00 // 超过5000限制
      }
    });
    expect(largeOrderResponse.status()).toBe(400);
    
    // 测试无效房间ID
    const invalidRoomIdResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        room_id: 0, // 无效的房间ID
        total_amount: 50.00
      }
    });
    expect(invalidRoomIdResponse.status()).toBe(400);
    
    console.log('✅ 问题4修复验证成功: API参数验证现在更加严格');
  });

  // ==================== 数据一致性验证 ====================
  test('✅ 数据一致性验证', async ({ request }) => {
    console.log('🔄 验证数据一致性');
    
    // 验证订单详情包含完整的关联数据
    const orderDetailResponse = await request.get(`${API_BASE}/orders/${testOrderId}`);
    expect(orderDetailResponse.status()).toBe(200);
    const orderDetail = await orderDetailResponse.json();
    
    // 验证订单包含用户和房间信息
    expect(orderDetail.data).toHaveProperty('user');
    expect(orderDetail.data).toHaveProperty('room');
    expect(orderDetail.data.user.id).toBe(testUserId);
    expect(orderDetail.data.room.id).toBe(testRoomId);
    
    console.log('✅ 数据一致性验证成功: 订单详情包含完整的关联数据');
  });

  // ==================== 边界条件测试 ====================
  test('✅ 边界条件测试', async ({ request }) => {
    console.log('🎯 测试边界条件');
    
    // 测试最小有效金额
    const minAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 0.01, // 最小金额
        payment_method: 'wechat'
      }
    });
    expect(minAmountResponse.status()).toBe(200);
    
    // 测试最大有效金额
    const maxAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 10000.00, // 最大金额
        payment_method: 'alipay'
      }
    });
    expect(maxAmountResponse.status()).toBe(200);
    
    console.log('✅ 边界条件测试成功: 最小和最大有效值正确处理');
  });

  // ==================== 清理测试数据 ====================
  test('🧹 清理测试数据', async ({ request }) => {
    console.log('🗑️ 清理测试数据');
    
    // 结束订单
    if (testOrderId) {
      await request.put(`${API_BASE}/orders/${testOrderId}/end`);
      console.log('✅ 测试订单已结束');
    }
    
    // 删除测试房间
    if (testRoomId) {
      await request.delete(`${API_BASE}/admin/rooms/${testRoomId}`);
      console.log('✅ 测试房间已删除');
    }
    
    console.log('🎉 测试数据清理完成');
  });

  test.afterAll(async () => {
    console.log('🏁 高优先级问题修复验证完成');
    console.log('📋 修复验证总结:');
    console.log('  ✅ 问题1: 订单API数据结构 - 已修复');
    console.log('  ✅ 问题2: 外键约束验证 - 已修复');
    console.log('  ✅ 问题3: 唯一约束处理 - 已修复');
    console.log('  ✅ 问题4: API参数验证 - 已加强');
    console.log('  ✅ 数据一致性 - 正常');
    console.log('  ✅ 边界条件 - 正确处理');
    console.log('🎉 所有高优先级问题已成功修复！');
  });

});
