const { test, expect } = require('@playwright/test');

test('检查Dashboard具体数据值', async ({ page }) => {
  // 访问Dashboard页面
  await page.goto('http://localhost:3001');
  await page.waitForLoadState('networkidle');
  
  // 等待数据加载
  await page.waitForTimeout(3000);
  
  console.log('=== 检查具体数据值 ===');
  
  // 使用更精确的选择器来获取数据
  try {
    // 检查今日收入
    const incomeElements = await page.locator('.stat-card').filter({ hasText: '今日收入' });
    if (await incomeElements.count() > 0) {
      const incomeValue = await incomeElements.locator('.stat-number').textContent();
      console.log('✅ 今日收入:', incomeValue);
      
      // 验证是否显示真实数据（不是模拟数据）
      if (incomeValue && !incomeValue.includes('2580.50')) {
        console.log('✅ 今日收入显示真实数据，不是模拟数据');
      }
    }
    
    // 检查总房间数
    const roomsElements = await page.locator('.stat-card').filter({ hasText: '总房间数' });
    if (await roomsElements.count() > 0) {
      const roomsValue = await roomsElements.locator('.stat-number').textContent();
      console.log('✅ 总房间数:', roomsValue);
    }
    
    // 检查今日订单
    const ordersElements = await page.locator('.stat-card').filter({ hasText: '今日订单' });
    if (await ordersElements.count() > 0) {
      const ordersValue = await ordersElements.locator('.stat-number').textContent();
      console.log('✅ 今日订单:', ordersValue);
    }
    
    // 检查在线设备
    const devicesElements = await page.locator('.stat-card').filter({ hasText: '在线设备' });
    if (await devicesElements.count() > 0) {
      const devicesValue = await devicesElements.locator('.stat-number').textContent();
      console.log('✅ 在线设备:', devicesValue);
    }
    
  } catch (error) {
    console.log('❌ 获取数据值时出错:', error.message);
  }
  
  // 检查API响应数据
  console.log('=== 检查API响应数据 ===');
  
  // 直接调用API检查数据
  const dashboardResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const dashboardData = await dashboardResponse.json();
  
  console.log('Dashboard API响应:');
  console.log('  今日收入:', dashboardData.data.todayIncome);
  console.log('  总房间数:', dashboardData.data.totalRooms);
  console.log('  今日订单:', dashboardData.data.todayOrders);
  console.log('  在线设备:', dashboardData.data.onlineDevices);
  console.log('  离线设备:', dashboardData.data.offlineDevices);
  
  // 检查房间统计API
  const roomStatsResponse = await page.request.get('http://localhost:8080/api/v1/admin/rooms/statistics');
  const roomStatsData = await roomStatsResponse.json();
  
  console.log('房间统计API响应:');
  console.log('  总房间:', roomStatsData.data.total);
  console.log('  空闲房间:', roomStatsData.data.available);
  console.log('  占用房间:', roomStatsData.data.occupied);
  console.log('  维护房间:', roomStatsData.data.maintenance);
  
  // 验证前端显示的数据是否与API数据一致
  console.log('=== 验证数据一致性 ===');
  
  // 等待页面数据更新
  await page.waitForTimeout(2000);
  
  // 再次获取前端显示的数据进行对比
  try {
    const frontendIncome = await page.locator('.stat-card').filter({ hasText: '今日收入' }).locator('.stat-number').textContent();
    const frontendRooms = await page.locator('.stat-card').filter({ hasText: '总房间数' }).locator('.stat-number').textContent();
    const frontendOrders = await page.locator('.stat-card').filter({ hasText: '今日订单' }).locator('.stat-number').textContent();
    const frontendDevices = await page.locator('.stat-card').filter({ hasText: '在线设备' }).locator('.stat-number').textContent();
    
    console.log('前端显示 vs API数据对比:');
    console.log(`  今日收入: ${frontendIncome} vs ${dashboardData.data.todayIncome}`);
    console.log(`  总房间数: ${frontendRooms} vs ${dashboardData.data.totalRooms}`);
    console.log(`  今日订单: ${frontendOrders} vs ${dashboardData.data.todayOrders}`);
    console.log(`  在线设备: ${frontendDevices} vs ${dashboardData.data.onlineDevices}`);
    
    // 检查数据是否一致
    const incomeMatch = frontendIncome.includes(dashboardData.data.todayIncome.toString());
    const roomsMatch = frontendRooms === dashboardData.data.totalRooms.toString();
    const ordersMatch = frontendOrders === dashboardData.data.todayOrders.toString();
    const devicesMatch = frontendDevices === dashboardData.data.onlineDevices.toString();
    
    console.log('数据一致性检查:');
    console.log(`  今日收入一致: ${incomeMatch ? '✅' : '❌'}`);
    console.log(`  总房间数一致: ${roomsMatch ? '✅' : '❌'}`);
    console.log(`  今日订单一致: ${ordersMatch ? '✅' : '❌'}`);
    console.log(`  在线设备一致: ${devicesMatch ? '✅' : '❌'}`);
    
    if (incomeMatch && roomsMatch && ordersMatch && devicesMatch) {
      console.log('🎉 所有数据都正确显示！');
    } else {
      console.log('⚠️ 部分数据显示不一致');
    }
    
  } catch (error) {
    console.log('❌ 数据一致性检查失败:', error.message);
  }
  
  // 最终截图
  await page.screenshot({ path: 'dashboard-data-check.png', fullPage: true });
  
  console.log('=== 检查完成 ===');
});
