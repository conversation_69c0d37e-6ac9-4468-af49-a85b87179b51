const { test, expect } = require('@playwright/test');

// 测试配置
const FRONTEND_URL = 'http://localhost:3000';
const API_BASE = 'http://localhost:8080/api/v1/admin';

test.describe('用户订单统计数据验证测试', () => {
  
  test('验证用户列表中的订单统计数据准确性', async ({ page, request }) => {
    // 首先通过API获取真实数据
    const apiResponse = await request.get(`${API_BASE}/users`);
    expect(apiResponse.status()).toBe(200);
    
    const apiData = await apiResponse.json();
    const apiUsers = apiData.data.data;
    
    // 导航到前端用户列表页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');
    
    // 验证每个用户的订单统计数据
    for (let i = 0; i < apiUsers.length; i++) {
      const apiUser = apiUsers[i];
      const tableRow = page.locator('.el-table__body tr').nth(i);
      
      // 验证昵称匹配（确保是同一个用户）
      const frontendNickname = await tableRow.locator('td').nth(1).textContent();
      expect(frontendNickname.trim()).toBe(apiUser.nickname);
      
      // 验证订单数
      const frontendOrderCount = await tableRow.locator('td').nth(3).textContent();
      expect(parseInt(frontendOrderCount)).toBe(apiUser.order_count);
      
      // 验证总消费
      const frontendTotalConsumption = await tableRow.locator('td').nth(4).textContent();
      const consumptionValue = parseFloat(frontendTotalConsumption.replace('¥', '').trim());
      expect(consumptionValue).toBe(apiUser.total_consumption);
      
      console.log(`✅ 用户 ${apiUser.nickname}: 订单数 ${apiUser.order_count}, 总消费 ¥${apiUser.total_consumption}`);
    }
  });

  test('验证具体的订单统计数据与数据库一致', async ({ page }) => {
    // 导航到用户列表页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');
    
    // 预期的数据（基于数据库中的实际订单数据）
    const expectedUsers = [
      { nickname: '测试用户3', orderCount: 1, totalConsumption: 20 },
      { nickname: '测试用户2', orderCount: 1, totalConsumption: 60 },
      { nickname: '测试用户1', orderCount: 1, totalConsumption: 40 }
    ];
    
    // 验证每个用户的数据
    for (let i = 0; i < expectedUsers.length; i++) {
      const expected = expectedUsers[i];
      const tableRow = page.locator('.el-table__body tr').nth(i);
      
      // 验证昵称
      const nickname = await tableRow.locator('td').nth(1).textContent();
      expect(nickname.trim()).toBe(expected.nickname);
      
      // 验证订单数
      const orderCount = await tableRow.locator('td').nth(3).textContent();
      expect(parseInt(orderCount)).toBe(expected.orderCount);
      
      // 验证总消费
      const totalConsumption = await tableRow.locator('td').nth(4).textContent();
      const consumptionValue = parseFloat(totalConsumption.replace('¥', '').trim());
      expect(consumptionValue).toBe(expected.totalConsumption);
      
      console.log(`✅ 验证通过 - ${expected.nickname}: ${expected.orderCount}个订单, ¥${expected.totalConsumption}总消费`);
    }
  });

  test('验证用户详情对话框中的订单统计信息', async ({ page }) => {
    // 导航到用户列表页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');
    
    // 点击第一个用户的详情按钮
    await page.click('.el-table__body tr:first-child .el-button:has-text("详情")');
    
    // 等待对话框出现
    await page.waitForSelector('.el-dialog');
    
    // 验证对话框中的订单统计信息
    const dialogContent = await page.locator('.el-dialog .el-descriptions').textContent();
    
    // 验证包含订单数量和总消费信息
    expect(dialogContent).toContain('订单数量');
    expect(dialogContent).toContain('总消费');
    
    // 关闭对话框
    await page.click('.el-dialog__footer .el-button');
  });

  test('验证统计卡片中的数据准确性', async ({ page, request }) => {
    // 通过API获取统计数据
    const apiResponse = await request.get(`${API_BASE}/users`);
    const apiData = await apiResponse.json();
    const apiStats = apiData.data.stats;
    
    // 导航到前端页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 等待统计卡片加载
    await page.waitForSelector('.stats-card');
    
    // 验证总用户数
    const totalUsersText = await page.locator('.stats-card').first().locator('.stats-value').textContent();
    const totalUsers = parseInt(totalUsersText);
    expect(totalUsers).toBe(apiStats.total);
    
    // 验证今日新增用户数
    const todayNewText = await page.locator('.stats-card').nth(1).locator('.stats-value').textContent();
    const todayNew = parseInt(todayNewText);
    expect(todayNew).toBe(apiStats.todayNew);
    
    // 验证活跃用户数
    const activeUsersText = await page.locator('.stats-card').nth(2).locator('.stats-value').textContent();
    const activeUsers = parseInt(activeUsersText);
    expect(activeUsers).toBe(apiStats.activeUsers);
    
    console.log(`✅ 统计数据验证通过 - 总用户: ${totalUsers}, 今日新增: ${todayNew}, 活跃用户: ${activeUsers}`);
  });

  test('验证订单数据的实时性', async ({ page }) => {
    // 导航到用户列表页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 记录初始的订单统计数据
    await page.waitForSelector('.el-table__body tr');
    const initialOrderCount = await page.locator('.el-table__body tr').first().locator('td').nth(3).textContent();
    const initialTotalConsumption = await page.locator('.el-table__body tr').first().locator('td').nth(4).textContent();
    
    // 刷新页面
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.el-table__body tr');
    
    // 验证数据保持一致
    const refreshedOrderCount = await page.locator('.el-table__body tr').first().locator('td').nth(3).textContent();
    const refreshedTotalConsumption = await page.locator('.el-table__body tr').first().locator('td').nth(4).textContent();
    
    expect(refreshedOrderCount).toBe(initialOrderCount);
    expect(refreshedTotalConsumption).toBe(initialTotalConsumption);
    
    console.log(`✅ 数据一致性验证通过 - 订单数: ${initialOrderCount}, 总消费: ${initialTotalConsumption}`);
  });

  test('验证搜索功能不影响订单统计数据', async ({ page }) => {
    // 导航到用户列表页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');
    
    // 获取第一个用户的昵称和统计数据
    const firstUserNickname = await page.locator('.el-table__body tr').first().locator('td').nth(1).textContent();
    const firstUserOrderCount = await page.locator('.el-table__body tr').first().locator('td').nth(3).textContent();
    const firstUserTotalConsumption = await page.locator('.el-table__body tr').first().locator('td').nth(4).textContent();
    
    // 搜索该用户
    await page.fill('input[placeholder="请输入用户昵称"]', firstUserNickname.trim());
    await page.click('.filter-card .el-button:has-text("搜索")');
    
    // 等待搜索结果
    await page.waitForTimeout(1000);
    
    // 验证搜索结果中的统计数据保持一致
    const searchResultOrderCount = await page.locator('.el-table__body tr').first().locator('td').nth(3).textContent();
    const searchResultTotalConsumption = await page.locator('.el-table__body tr').first().locator('td').nth(4).textContent();
    
    expect(searchResultOrderCount).toBe(firstUserOrderCount);
    expect(searchResultTotalConsumption).toBe(firstUserTotalConsumption);
    
    console.log(`✅ 搜索功能验证通过 - ${firstUserNickname.trim()}: ${firstUserOrderCount}个订单, ${firstUserTotalConsumption}总消费`);
  });

  test('验证数据格式的正确性', async ({ page }) => {
    // 导航到用户列表页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');
    
    const tableRows = await page.locator('.el-table__body tr').count();
    
    for (let i = 0; i < tableRows; i++) {
      const row = page.locator('.el-table__body tr').nth(i);
      
      // 验证订单数格式（应该是整数）
      const orderCountText = await row.locator('td').nth(3).textContent();
      const orderCount = parseInt(orderCountText);
      expect(orderCount).toBeGreaterThanOrEqual(0);
      expect(Number.isInteger(orderCount)).toBe(true);
      
      // 验证总消费格式（应该包含¥符号，是有效的数字）
      const totalConsumptionText = await row.locator('td').nth(4).textContent();
      expect(totalConsumptionText.trim()).toMatch(/^¥\d+(\.\d{1,2})?$/);

      const consumptionValue = parseFloat(totalConsumptionText.replace('¥', '').trim());
      expect(consumptionValue).toBeGreaterThanOrEqual(0);
      
      console.log(`✅ 格式验证通过 - 行${i + 1}: ${orderCountText}个订单, ${totalConsumptionText}总消费`);
    }
  });
});
