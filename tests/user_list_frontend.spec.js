const { test, expect } = require('@playwright/test');

// 测试配置
const FRONTEND_URL = 'http://localhost:3000';

test.describe('用户列表前端页面测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 导航到用户列表页面
    await page.goto(`${FRONTEND_URL}/users/list`);
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
  });

  test('页面加载测试', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('h2')).toContainText('用户管理');

    // 验证页面描述
    await expect(page.locator('p')).toContainText('管理所有用户信息和统计');

    // 验证导出按钮存在
    await expect(page.locator('text=导出用户')).toBeVisible();

    // 验证搜索表单存在（简化验证）
    await expect(page.locator('input[placeholder="请输入用户昵称"]')).toBeVisible();
    await expect(page.locator('input[placeholder="请输入手机号"]')).toBeVisible();
  });

  test('统计卡片显示测试', async ({ page }) => {
    // 验证统计卡片存在
    await expect(page.locator('text=总用户数')).toBeVisible();
    await expect(page.locator('text=今日新增')).toBeVisible();
    await expect(page.locator('text=活跃用户')).toBeVisible();
    await expect(page.locator('text=平均消费')).toBeVisible();
    
    // 验证统计数据是数字
    const totalUsers = await page.locator('.stats-card').first().locator('.stats-value').textContent();
    expect(parseInt(totalUsers)).toBeGreaterThanOrEqual(0);
  });

  test('用户列表表格测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table');
    
    // 验证表格列标题
    await expect(page.locator('thead .cell:has-text("头像")')).toBeVisible();
    await expect(page.locator('thead .cell:has-text("昵称")')).toBeVisible();
    await expect(page.locator('thead .cell:has-text("手机号")')).toBeVisible();
    await expect(page.locator('thead .cell:has-text("订单数")')).toBeVisible();
    await expect(page.locator('thead .cell:has-text("总消费")')).toBeVisible();
    await expect(page.locator('thead .cell:has-text("最后登录")')).toBeVisible();
    await expect(page.locator('thead .cell:has-text("注册时间")')).toBeVisible();
    await expect(page.locator('thead .cell:has-text("操作")')).toBeVisible();
    
    // 验证表格有数据行
    const tableRowsCount = await page.locator('.el-table__body tr').count();
    expect(tableRowsCount).toBeGreaterThan(0);
  });

  test('用户数量一致性测试', async ({ page }) => {
    // 等待数据加载完成
    await page.waitForSelector('.stats-value');
    await page.waitForSelector('.el-table__body tr');
    
    // 获取统计卡片中的总用户数
    const totalUsersText = await page.locator('.stats-card').first().locator('.stats-value').textContent();
    const totalUsers = parseInt(totalUsersText);

    // 获取表格中的用户行数
    const tableRows = await page.locator('.el-table__body tr').count();

    // 获取分页信息中的总数
    const paginationTotal = await page.locator('.el-pagination__total').textContent();
    const totalFromPagination = parseInt(paginationTotal.match(/\d+/)[0]);

    // 验证数量一致性（如果是第一页且用户数少于页面大小）
    if (tableRows <= 10) { // 默认页面大小是10
      expect(tableRows).toBe(totalFromPagination);
      // 注意：统计卡片的总数可能与当前页面显示的数量不同，这是正常的
      expect(totalUsers).toBeGreaterThanOrEqual(tableRows);
    }
  });

  test('用户信息字段完整性测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');
    
    const firstRow = page.locator('.el-table__body tr').first();
    
    // 验证头像列
    await expect(firstRow.locator('td').nth(0).locator('.el-avatar')).toBeVisible();
    
    // 验证昵称列
    const nickname = await firstRow.locator('td').nth(1).textContent();
    expect(nickname.trim()).toBeTruthy();
    
    // 验证手机号列（可能为空，但应该有内容或"-"）
    const phone = await firstRow.locator('td').nth(2).textContent();
    expect(phone.trim()).toBeTruthy();
    
    // 验证订单数列
    const orderCount = await firstRow.locator('td').nth(3).textContent();
    expect(parseInt(orderCount)).toBeGreaterThanOrEqual(0);
    
    // 验证总消费列
    const totalConsumption = await firstRow.locator('td').nth(4).textContent();
    expect(totalConsumption).toContain('¥');
    
    // 验证注册时间列
    const createdAt = await firstRow.locator('td').nth(6).textContent();
    expect(createdAt.trim()).toBeTruthy();
    expect(createdAt).not.toBe('-');
    
    // 验证操作列
    await expect(firstRow.locator('text=详情')).toBeVisible();
    await expect(firstRow.locator('text=订单')).toBeVisible();
  });

  test('搜索功能测试', async ({ page }) => {
    // 等待页面加载
    await page.waitForSelector('.el-table__body tr');
    
    // 获取第一个用户的昵称
    const firstUserNickname = await page.locator('.el-table__body tr').first().locator('td').nth(1).textContent();
    
    if (firstUserNickname && firstUserNickname.trim() !== '-') {
      // 在昵称搜索框中输入
      await page.fill('input[placeholder="请输入用户昵称"]', firstUserNickname.trim());
      
      // 点击搜索按钮（使用更精确的选择器）
      await page.click('.filter-card .el-button:has-text("搜索")');
      
      // 等待搜索结果
      await page.waitForTimeout(1000);
      
      // 验证搜索结果包含搜索的昵称
      const searchResults = await page.locator('.el-table__body tr').count();
      expect(searchResults).toBeGreaterThan(0);
      
      // 验证第一行仍然包含搜索的昵称
      const resultNickname = await page.locator('.el-table__body tr').first().locator('td').nth(1).textContent();
      expect(resultNickname).toContain(firstUserNickname.trim());
    }
  });

  test('重置功能测试', async ({ page }) => {
    // 在搜索框中输入内容
    await page.fill('input[placeholder="请输入用户昵称"]', '测试');
    
    // 点击重置按钮
    await page.click('.filter-card .el-button:has-text("重置")');
    
    // 验证搜索框已清空
    const nicknameInput = await page.locator('input[placeholder="请输入用户昵称"]').inputValue();
    expect(nicknameInput).toBe('');
    
    // 验证表格重新加载了所有数据
    await page.waitForTimeout(1000);
    const tableRows = await page.locator('.el-table__body tr').count();
    expect(tableRows).toBeGreaterThan(0);
  });

  test('分页功能测试', async ({ page }) => {
    // 等待分页组件加载
    await page.waitForSelector('.el-pagination');
    
    // 检查是否有分页
    const paginationExists = await page.locator('.el-pagination .el-pager li').count();
    
    if (paginationExists > 1) {
      // 记录第一页的第一个用户
      const firstPageFirstUser = await page.locator('.el-table__body tr').first().locator('td').nth(1).textContent();
      
      // 点击第二页
      await page.click('.el-pagination .el-pager li:nth-child(2)');
      
      // 等待页面更新
      await page.waitForTimeout(1000);
      
      // 验证页面已切换
      const secondPageFirstUser = await page.locator('.el-table__body tr').first().locator('td').nth(1).textContent();
      expect(secondPageFirstUser).not.toBe(firstPageFirstUser);
    }
  });

  test('用户详情对话框测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');

    // 点击第一个用户的详情按钮
    await page.click('.el-table__body tr:first-child .el-button:has-text("详情")');

    // 等待对话框出现
    await page.waitForSelector('.el-dialog');

    // 验证对话框标题
    await expect(page.locator('.el-dialog__title')).toContainText('用户详情');

    // 验证用户详情内容（简化验证）
    await expect(page.locator('.el-descriptions')).toBeVisible();

    // 关闭对话框
    await page.click('.el-dialog__footer .el-button');

    // 验证对话框已关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible();
  });

  test('导出功能测试', async ({ page }) => {
    // 点击导出按钮
    await page.click('text=导出用户');
    
    // 验证提示消息出现
    await expect(page.locator('.el-message')).toBeVisible();
    await expect(page.locator('.el-message')).toContainText('导出功能开发中');
  });

  test('响应式设计测试', async ({ page }) => {
    // 测试移动端视口
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 等待页面重新渲染
    await page.waitForTimeout(500);
    
    // 验证页面在小屏幕下仍然可用
    await expect(page.locator('h2')).toBeVisible();
    await expect(page.locator('.el-table')).toBeVisible();
    
    // 恢复桌面视口
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('加载状态测试', async ({ page }) => {
    // 重新加载页面以观察加载状态
    await page.reload();
    
    // 检查是否有加载指示器
    const loadingExists = await page.locator('.el-loading-mask').isVisible().catch(() => false);
    
    // 等待加载完成
    await page.waitForLoadState('networkidle');
    
    // 验证最终状态
    const finalTableRows = await page.locator('.el-table__body tr').count();
    expect(finalTableRows).toBeGreaterThan(0);
  });

  test('错误处理测试', async ({ page }) => {
    // 模拟网络错误（通过拦截请求）
    await page.route('**/api/v1/admin/users*', route => {
      route.abort();
    });
    
    // 重新加载页面
    await page.reload();
    
    // 等待错误处理
    await page.waitForTimeout(2000);
    
    // 验证错误状态（表格应该为空或显示错误信息）
    const tableRows = await page.locator('.el-table__body tr').count();
    expect(tableRows).toBe(0);
  });
});
