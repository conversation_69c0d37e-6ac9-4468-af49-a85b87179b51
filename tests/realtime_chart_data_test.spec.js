const { test, expect } = require('@playwright/test');

test('实时折线图数据验证', async ({ page }) => {
  console.log('=== 实时折线图数据验证测试 ===');
  
  // 1. 获取后端API基准数据
  console.log('1. 获取后端API基准数据...');
  const apiResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const apiData = await apiResponse.json();
  
  expect(apiResponse.status()).toBe(200);
  expect(apiData.code).toBe(200);
  
  const backendData = apiData.data;
  console.log('后端API数据:', {
    activeOrders: backendData.activeOrders,
    todayIncome: backendData.todayIncome,
    onlineDevices: backendData.onlineDevices
  });
  
  // 2. 访问前端页面
  console.log('2. 访问前端Dashboard页面...');
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('networkidle');
  
  // 等待数据加载和图表初始化
  await page.waitForTimeout(8000);
  
  // 3. 检查实时折线图是否存在
  console.log('3. 检查实时折线图组件...');
  await page.waitForSelector('.realtime-chart', { timeout: 10000 });
  await page.waitForSelector('.chart-container', { timeout: 10000 });
  
  const chartExists = await page.locator('.realtime-chart .chart-container').count();
  console.log(`实时折线图组件存在: ${chartExists > 0 ? '是' : '否'}`);
  expect(chartExists).toBeGreaterThan(0);
  
  // 4. 验证实时监控指标显示真实数据
  console.log('4. 验证实时监控指标数据...');
  
  const metrics = await page.locator('.realtime-chart .metric-item');
  const metricsCount = await metrics.count();
  console.log(`实时监控指标数量: ${metricsCount}`);
  
  for (let i = 0; i < metricsCount; i++) {
    const metric = metrics.nth(i);
    const value = await metric.locator('.metric-value').textContent();
    const label = await metric.locator('.metric-label').textContent();
    console.log(`实时监控 - ${label}: ${value}`);
    
    // 验证数据与后端API一致
    if (label.includes('活跃订单')) {
      expect(value.trim()).toBe(backendData.activeOrders.toString());
    } else if (label.includes('在线设备')) {
      expect(value.trim()).toBe(backendData.onlineDevices.toString());
    } else if (label.includes('今日收入')) {
      expect(value).toContain(backendData.todayIncome.toFixed(2));
    }
  }
  
  // 5. 测试手动刷新功能
  console.log('5. 测试手动刷新功能...');
  
  const refreshButton = await page.locator('.realtime-chart .el-button');
  if (await refreshButton.count() > 0) {
    console.log('点击手动刷新按钮...');
    await refreshButton.click();
    await page.waitForTimeout(3000);
    
    // 再次验证数据
    const updatedMetrics = await page.locator('.realtime-chart .metric-item');
    for (let i = 0; i < await updatedMetrics.count(); i++) {
      const metric = updatedMetrics.nth(i);
      const value = await metric.locator('.metric-value').textContent();
      const label = await metric.locator('.metric-label').textContent();
      console.log(`刷新后 - ${label}: ${value}`);
      
      // 验证刷新后数据仍然正确
      if (label.includes('活跃订单')) {
        expect(value.trim()).toBe(backendData.activeOrders.toString());
      } else if (label.includes('在线设备')) {
        expect(value.trim()).toBe(backendData.onlineDevices.toString());
      }
    }
    
    console.log('✅ 手动刷新后数据仍然正确');
  }
  
  // 6. 验证自动刷新功能
  console.log('6. 验证自动刷新功能...');
  
  const autoRefreshSwitch = await page.locator('.realtime-chart .el-switch input');
  if (await autoRefreshSwitch.count() > 0) {
    const isChecked = await autoRefreshSwitch.isChecked();
    console.log(`自动刷新开关状态: ${isChecked ? '开启' : '关闭'}`);
    
    if (isChecked) {
      console.log('等待自动刷新...');
      await page.waitForTimeout(4000); // 等待一次自动刷新（3秒间隔）
      
      // 验证自动刷新后数据
      const autoRefreshedMetrics = await page.locator('.realtime-chart .metric-item');
      for (let i = 0; i < await autoRefreshedMetrics.count(); i++) {
        const metric = autoRefreshedMetrics.nth(i);
        const value = await metric.locator('.metric-value').textContent();
        const label = await metric.locator('.metric-label').textContent();
        
        if (label.includes('活跃订单')) {
          expect(value.trim()).toBe(backendData.activeOrders.toString());
        } else if (label.includes('在线设备')) {
          expect(value.trim()).toBe(backendData.onlineDevices.toString());
        }
      }
      
      console.log('✅ 自动刷新后数据仍然正确');
    }
  }
  
  // 7. 检查图表数据是否使用真实数据
  console.log('7. 检查图表数据逻辑...');
  
  // 通过JavaScript检查图表实例的数据
  const chartData = await page.evaluate(() => {
    // 查找ECharts实例
    const chartContainer = document.querySelector('.realtime-chart .chart-container');
    if (chartContainer && chartContainer._echarts_instance_) {
      const chart = chartContainer._echarts_instance_;
      const option = chart.getOption();
      
      if (option && option.series && option.series.length > 0) {
        return {
          hasChart: true,
          seriesCount: option.series.length,
          orderData: option.series[0] ? option.series[0].data : null,
          incomeData: option.series[1] ? option.series[1].data : null,
          deviceData: option.series[2] ? option.series[2].data : null,
          xAxisData: option.xAxis && option.xAxis[0] ? option.xAxis[0].data : null
        };
      }
    }
    return { hasChart: false };
  });
  
  console.log('图表数据信息:', chartData);
  
  if (chartData.hasChart) {
    expect(chartData.seriesCount).toBe(3); // 应该有3个数据系列
    console.log('✅ 图表包含3个数据系列（订单、收入、设备）');
    
    if (chartData.orderData && chartData.orderData.length > 0) {
      const latestOrderData = chartData.orderData[chartData.orderData.length - 1];
      console.log(`图表最新订单数据: ${latestOrderData}`);
      expect(latestOrderData).toBe(backendData.activeOrders);
    }
    
    if (chartData.deviceData && chartData.deviceData.length > 0) {
      const latestDeviceData = chartData.deviceData[chartData.deviceData.length - 1];
      console.log(`图表最新设备数据: ${latestDeviceData}`);
      expect(latestDeviceData).toBe(backendData.onlineDevices);
    }
    
    if (chartData.incomeData && chartData.incomeData.length > 0) {
      const latestIncomeData = chartData.incomeData[chartData.incomeData.length - 1];
      console.log(`图表最新收入数据: ${latestIncomeData}`);
      expect(latestIncomeData).toBe(backendData.todayIncome);
    }
    
    console.log('✅ 图表数据与后端API数据一致');
  }
  
  console.log('=== 实时折线图数据验证完成 ===');
  console.log('🎉 实时折线图现在使用真实数据！');
});
