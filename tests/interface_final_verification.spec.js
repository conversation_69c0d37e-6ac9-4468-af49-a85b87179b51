const { test, expect } = require('@playwright/test');

// 测试配置
const FRONTEND_URL = 'http://localhost:3000';
const API_BASE = 'http://localhost:8080/api/v1';

test.describe('界面修复验证测试', () => {
  
  test('系统配置API修复验证', async ({ request }) => {
    console.log('🔧 测试系统配置API修复');
    
    const response = await request.get(`${API_BASE}/system/config`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('basic');
    expect(data.data).toHaveProperty('pricing');
    expect(data.data).toHaveProperty('notification');
    
    console.log('✅ 系统配置API修复成功');
  });

  test('订单核销页面图标修复验证', async ({ page }) => {
    console.log('🔧 测试订单核销页面图标修复');

    await page.goto(`${FRONTEND_URL}/platform/verification`);
    await page.waitForLoadState('networkidle');

    // 等待页面完全加载
    await page.waitForTimeout(2000);

    // 检查页面标题是否存在
    const pageTitle = await page.locator('h2:has-text("订单核销中心")').count();
    expect(pageTitle).toBeGreaterThan(0);

    // 检查扫码核销按钮是否存在
    const scanButton = await page.locator('button:has-text("扫码核销")').count();
    expect(scanButton).toBeGreaterThan(0);

    // 检查统计卡片是否存在
    const statCards = await page.locator('.stat-card').count();
    expect(statCards).toBeGreaterThanOrEqual(3);

    console.log('✅ 订单核销页面图标修复成功');
  });

  test('404路由处理验证', async ({ page }) => {
    console.log('🔧 测试404路由处理');
    
    await page.goto(`${FRONTEND_URL}/nonexistent-page`);
    await page.waitForLoadState('networkidle');
    
    // 应该重定向到仪表盘
    const currentUrl = page.url();
    expect(currentUrl).toContain('/dashboard');
    
    console.log('✅ 404路由处理修复成功');
  });

  test('主要页面数据加载验证', async ({ page }) => {
    console.log('🔧 测试主要页面数据加载');
    
    const pages = [
      { path: '/dashboard', name: '仪表盘' },
      { path: '/users/list', name: '用户列表' },
      { path: '/rooms/list', name: '房间列表' },
      { path: '/orders/list', name: '订单列表' },
      { path: '/devices/list', name: '设备管理' },
      { path: '/finance/analysis', name: '财务分析' },
      { path: '/platform/orders', name: '平台订单' },
      { path: '/system/pricing', name: '计费规则' }
    ];

    for (const pageInfo of pages) {
      await page.goto(`${FRONTEND_URL}${pageInfo.path}`);
      await page.waitForLoadState('networkidle');
      
      // 检查页面标题
      const pageTitle = await page.title();
      expect(pageTitle).toContain('自助麻将室管理系统');
      
      // 检查是否有数据内容或空状态
      const hasContent = await page.locator('.el-table, .el-card, .el-empty').count() > 0;
      expect(hasContent).toBe(true);
      
      console.log(`✅ ${pageInfo.name} 页面加载正常`);
    }
  });

  test('系统配置页面修复验证', async ({ page }) => {
    console.log('🔧 测试系统配置页面修复');
    
    await page.goto(`${FRONTEND_URL}/system/settings`);
    await page.waitForLoadState('networkidle');
    
    // 等待数据加载
    await page.waitForTimeout(2000);
    
    // 检查是否有配置表单
    const hasForm = await page.locator('.el-form').count() > 0;
    expect(hasForm).toBe(true);
    
    // 检查是否有配置卡片
    const hasCards = await page.locator('.el-card').count() > 0;
    expect(hasCards).toBe(true);
    
    console.log('✅ 系统配置页面修复成功');
  });

  test('用户列表订单统计数据验证', async ({ page }) => {
    console.log('🔧 测试用户列表订单统计数据');
    
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 等待表格加载
    await page.waitForSelector('.el-table__body tr');
    
    // 检查订单数量列
    const orderCountColumn = await page.locator('th:has-text("订单数")').count();
    expect(orderCountColumn).toBeGreaterThan(0);
    
    // 检查总消费列
    const totalConsumptionColumn = await page.locator('th:has-text("总消费")').count();
    expect(totalConsumptionColumn).toBeGreaterThan(0);
    
    // 检查数据格式
    const firstRowOrderCount = await page.locator('.el-table__body tr').first().locator('td').nth(3).textContent();
    expect(parseInt(firstRowOrderCount)).toBeGreaterThanOrEqual(0);
    
    const firstRowTotalConsumption = await page.locator('.el-table__body tr').first().locator('td').nth(4).textContent();
    expect(firstRowTotalConsumption).toMatch(/¥\d+\.\d{2}/);
    
    console.log('✅ 用户列表订单统计数据正常');
  });

  test('API连接状态综合验证', async ({ request }) => {
    console.log('🔧 测试API连接状态');
    
    const criticalAPIs = [
      { url: `${API_BASE}/admin/dashboard`, name: '仪表盘' },
      { url: `${API_BASE}/admin/users`, name: '用户管理' },
      { url: `${API_BASE}/rooms`, name: '房间管理' },
      { url: `${API_BASE}/orders`, name: '订单管理' },
      { url: `${API_BASE}/admin/devices`, name: '设备管理' },
      { url: `${API_BASE}/admin/finance/report`, name: '财务报告' },
      { url: `${API_BASE}/admin/platform/orders`, name: '平台订单' },
      { url: `${API_BASE}/admin/pricing-rules`, name: '计费规则' },
      { url: `${API_BASE}/system/config`, name: '系统配置' }
    ];

    let successCount = 0;
    for (const api of criticalAPIs) {
      try {
        const response = await request.get(api.url);
        if (response.status() === 200) {
          successCount++;
          console.log(`✅ ${api.name} API正常`);
        } else {
          console.log(`⚠️ ${api.name} API状态码: ${response.status()}`);
        }
      } catch (error) {
        console.log(`❌ ${api.name} API连接失败`);
      }
    }
    
    // 至少80%的API应该正常工作
    const successRate = successCount / criticalAPIs.length;
    expect(successRate).toBeGreaterThanOrEqual(0.8);
    
    console.log(`✅ API连接成功率: ${(successRate * 100).toFixed(1)}%`);
  });

  test('响应式设计基础验证', async ({ page }) => {
    console.log('🔧 测试响应式设计');
    
    const viewports = [
      { width: 1920, height: 1080, name: '桌面大屏' },
      { width: 1366, height: 768, name: '桌面标准' },
      { width: 768, height: 1024, name: '平板' }
    ];

    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto(`${FRONTEND_URL}/dashboard`);
      await page.waitForLoadState('networkidle');
      
      // 检查布局是否正常
      const sidebar = await page.locator('.sidebar-container').isVisible();
      const mainContent = await page.locator('.layout-main').isVisible();
      
      expect(sidebar).toBe(true);
      expect(mainContent).toBe(true);
      
      console.log(`✅ ${viewport.name} 响应式布局正常`);
    }
  });

  test('数据刷新和实时性验证', async ({ page }) => {
    console.log('🔧 测试数据刷新功能');
    
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 记录初始数据
    const initialRowCount = await page.locator('.el-table__body tr').count();
    
    // 刷新页面
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // 检查数据是否重新加载
    const refreshedRowCount = await page.locator('.el-table__body tr').count();
    expect(refreshedRowCount).toBe(initialRowCount);
    
    console.log('✅ 数据刷新功能正常');
  });

  test('错误处理机制验证', async ({ page }) => {
    console.log('🔧 测试错误处理机制');
    
    // 监听控制台错误
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // 访问几个主要页面
    const pages = ['/dashboard', '/users/list', '/rooms/list', '/orders/list'];
    
    for (const pagePath of pages) {
      await page.goto(`${FRONTEND_URL}${pagePath}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
    }
    
    // 检查是否有严重错误
    const criticalErrors = errors.filter(error => 
      error.includes('TypeError') || 
      error.includes('ReferenceError') ||
      error.includes('SyntaxError')
    );
    
    expect(criticalErrors.length).toBe(0);
    
    console.log(`✅ 错误处理验证完成，发现 ${errors.length} 个控制台消息，${criticalErrors.length} 个严重错误`);
  });
});
