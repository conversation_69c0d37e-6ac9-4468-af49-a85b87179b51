// 套餐列表修复验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🎯 套餐列表修复验证', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始套餐列表修复验证');
    console.log('🎯 验证目标: 套餐列表正常加载，无网络连接失败错误');
  });

  // ==================== 测试1: 套餐列表API验证 ====================
  test('✅ 套餐列表API验证', async ({ request }) => {
    console.log('🔧 测试套餐列表API');
    
    const response = await request.get(`${API_BASE}/admin/packages`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('list');
    expect(data.data).toHaveProperty('total');
    
    console.log(`📦 套餐总数: ${data.data.total}`);
    console.log(`📄 当前页: ${data.data.list.length}条记录`);
    
    // 验证套餐数据结构
    if (data.data.list.length > 0) {
      const firstPackage = data.data.list[0];
      expect(firstPackage).toHaveProperty('id');
      expect(firstPackage).toHaveProperty('name');
      expect(firstPackage).toHaveProperty('type');
      expect(firstPackage).toHaveProperty('original_price');
      expect(firstPackage).toHaveProperty('sale_price');
      
      console.log(`📝 示例套餐: ${firstPackage.name} - ${firstPackage.type}`);
    }
    
    console.log(`✅ 套餐列表API正常`);
  });

  // ==================== 测试2: 套餐统计API验证 ====================
  test('✅ 套餐统计API验证', async ({ request }) => {
    console.log('🔧 测试套餐统计API');
    
    const response = await request.get(`${API_BASE}/admin/packages/stats`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('total');
    expect(data.data).toHaveProperty('active');
    
    console.log(`📊 统计数据: 总计${data.data.total}个套餐，${data.data.active}个活跃`);
    console.log(`💰 销售数据: ${data.data.total_sales}笔订单，收入${data.data.total_revenue}元`);
    
    console.log(`✅ 套餐统计API正常`);
  });

  // ==================== 测试3: 分页功能验证 ====================
  test('✅ 分页功能验证', async ({ request }) => {
    console.log('🔧 测试分页功能');
    
    // 测试第1页
    const page1Response = await request.get(`${API_BASE}/admin/packages?page=1&page_size=3`);
    expect(page1Response.status()).toBe(200);
    const page1Data = await page1Response.json();
    expect(page1Data.code).toBe(200);
    
    console.log(`📄 第1页: ${page1Data.data.list.length}条记录`);
    
    // 如果有多页，测试第2页
    if (page1Data.data.total > 3) {
      const page2Response = await request.get(`${API_BASE}/admin/packages?page=2&page_size=3`);
      expect(page2Response.status()).toBe(200);
      const page2Data = await page2Response.json();
      expect(page2Data.code).toBe(200);
      
      console.log(`📄 第2页: ${page2Data.data.list.length}条记录`);
    }
    
    console.log(`✅ 分页功能正常`);
  });

  // ==================== 测试4: 筛选功能验证 ====================
  test('✅ 筛选功能验证', async ({ request }) => {
    console.log('🔧 测试筛选功能');
    
    // 测试按类型筛选
    const typeFilterResponse = await request.get(`${API_BASE}/admin/packages?type=fixed_duration`);
    expect(typeFilterResponse.status()).toBe(200);
    const typeFilterData = await typeFilterResponse.json();
    expect(typeFilterData.code).toBe(200);
    
    console.log(`📝 固定时长套餐: ${typeFilterData.data.list.length}个`);
    
    // 验证筛选结果
    typeFilterData.data.list.forEach(pkg => {
      expect(pkg.type).toBe('fixed_duration');
    });
    
    // 测试按状态筛选
    const statusFilterResponse = await request.get(`${API_BASE}/admin/packages?is_active=true`);
    expect(statusFilterResponse.status()).toBe(200);
    const statusFilterData = await statusFilterResponse.json();
    expect(statusFilterData.code).toBe(200);
    
    console.log(`📝 活跃套餐: ${statusFilterData.data.list.length}个`);
    
    // 验证筛选结果
    statusFilterData.data.list.forEach(pkg => {
      expect(pkg.is_active).toBe(true);
    });
    
    console.log(`✅ 筛选功能正常`);
  });

  // ==================== 测试5: 连续请求压力测试 ====================
  test('✅ 连续请求压力测试', async ({ request }) => {
    console.log('🔧 测试连续请求压力');
    
    // 连续发送多个请求，模拟用户快速操作
    const requests = [];
    for (let i = 0; i < 5; i++) {
      requests.push(
        request.get(`${API_BASE}/admin/packages?page=1&page_size=10`)
      );
    }
    
    const responses = await Promise.all(requests);
    
    responses.forEach((response, index) => {
      expect(response.status()).toBe(200);
      console.log(`📝 请求${index + 1}: 状态码 ${response.status()}`);
    });
    
    console.log(`✅ 连续请求处理正常`);
  });

  // ==================== 测试6: 错误恢复能力测试 ====================
  test('✅ 错误恢复能力测试', async ({ request }) => {
    console.log('🔧 测试错误恢复能力');
    
    // 测试无效参数
    const invalidResponse = await request.get(`${API_BASE}/admin/packages?page=0&page_size=-1`);
    expect(invalidResponse.status()).toBe(200); // 后端应该处理无效参数
    
    const invalidData = await invalidResponse.json();
    expect(invalidData.code).toBe(200);
    
    console.log(`📝 无效参数处理: 返回${invalidData.data.list.length}条记录`);
    
    // 测试超大页码
    const largePageResponse = await request.get(`${API_BASE}/admin/packages?page=999&page_size=10`);
    expect(largePageResponse.status()).toBe(200);
    
    const largePageData = await largePageResponse.json();
    expect(largePageData.code).toBe(200);
    expect(largePageData.data.list.length).toBe(0); // 超出范围应该返回空数组
    
    console.log(`📝 超大页码处理: 返回${largePageData.data.list.length}条记录`);
    
    console.log(`✅ 错误恢复能力正常`);
  });

  // ==================== 测试7: 前端页面交互测试 ====================
  test('✅ 前端页面交互测试', async ({ page }) => {
    console.log('🔧 测试前端页面交互');
    
    // 导航到套餐管理页面
    await page.goto('http://localhost:3001');
    
    // 等待页面加载
    await page.waitForTimeout(2000);
    
    // 点击套餐管理菜单
    await page.click('text=套餐管理');
    await page.waitForTimeout(1000);
    
    // 等待套餐列表加载
    await page.waitForSelector('.el-table', { timeout: 10000 });
    
    // 检查是否有错误提示
    const errorMessages = await page.locator('.el-message--error').count();
    expect(errorMessages).toBe(0);
    
    // 检查是否有网络连接失败的提示
    const networkErrors = await page.locator('text=网络连接失败').count();
    expect(networkErrors).toBe(0);
    
    // 检查表格是否有数据
    const tableRows = await page.locator('.el-table__body tr').count();
    expect(tableRows).toBeGreaterThan(0);
    
    console.log(`📄 套餐列表加载成功，显示${tableRows}行数据`);
    
    // 检查统计卡片是否正常显示
    const statsCards = await page.locator('.stats-card').count();
    expect(statsCards).toBeGreaterThan(0);
    
    console.log(`📊 统计卡片显示正常，${statsCards}个卡片`);
    
    console.log(`✅ 前端页面交互正常`);
  });

  test.afterAll(async () => {
    console.log('🏁 套餐列表修复验证完成');
    console.log('📋 验证总结:');
    console.log('  ✅ 套餐列表API - 正常');
    console.log('  ✅ 套餐统计API - 正常');
    console.log('  ✅ 分页功能 - 正常');
    console.log('  ✅ 筛选功能 - 正常');
    console.log('  ✅ 连续请求压力 - 正常');
    console.log('  ✅ 错误恢复能力 - 正常');
    console.log('  ✅ 前端页面交互 - 正常');
    console.log('🎉 套餐列表功能完全修复！');
    console.log('💡 不再有"网络连接失败"的误导性错误提示');
  });

});
