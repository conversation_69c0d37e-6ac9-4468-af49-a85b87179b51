// 订单列表显示修复验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🎯 订单列表显示修复验证', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始订单列表显示修复验证');
    console.log('🎯 验证目标: 订单号规范化、房间信息、结束时间、使用时长正确显示');
  });

  // ==================== 测试1: 订单号格式验证 ====================
  test('✅ 订单号格式验证', async ({ request }) => {
    console.log('🔧 测试订单号格式');
    
    const response = await request.get(`${API_BASE}/orders?page=1&page_size=10`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const orders = data.data.data;
    
    orders.forEach(order => {
      expect(order.order_number).toBeDefined();
      expect(order.order_number).not.toBe('');
      
      // 验证订单号格式：MJ + 8位日期 + 3位序号
      const orderNumberPattern = /^MJ\d{8}\d{3}$/;
      expect(order.order_number).toMatch(orderNumberPattern);
      
      console.log(`📝 订单${order.id}: ${order.order_number} ✅`);
    });
    
    console.log(`✅ 所有订单号格式正确`);
  });

  // ==================== 测试2: 房间信息显示验证 ====================
  test('✅ 房间信息显示验证', async ({ request }) => {
    console.log('🔧 测试房间信息显示');
    
    const response = await request.get(`${API_BASE}/orders?page=1&page_size=20`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const orders = data.data.data;
    
    let hasRoomInfo = 0;
    let hasNullRoom = 0;
    
    orders.forEach(order => {
      if (order.room_id === null) {
        hasNullRoom++;
        expect(order.room).toBeUndefined();
        console.log(`📝 订单${order.id}: room_id为null，room字段为空 ✅`);
      } else if (order.room) {
        hasRoomInfo++;
        expect(order.room.name).toBeDefined();
        expect(order.room.room_number).toBeDefined();
        console.log(`📝 订单${order.id}: 房间信息 ${order.room.name} ✅`);
      }
    });
    
    console.log(`📊 统计: ${hasRoomInfo}个订单有房间信息，${hasNullRoom}个订单房间已删除`);
    console.log(`✅ 房间信息显示正确`);
  });

  // ==================== 测试3: 使用时长计算验证 ====================
  test('✅ 使用时长计算验证', async ({ request }) => {
    console.log('🔧 测试使用时长计算');
    
    const response = await request.get(`${API_BASE}/orders?page=1&page_size=20`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const orders = data.data.data;
    
    orders.forEach(order => {
      console.log(`📝 订单${order.id} (${order.status}):`);
      
      if (order.status === 'cancelled') {
        // 已取消的订单应该有时长计算
        expect(order.duration).toBeDefined();
        expect(order.duration).toBeGreaterThanOrEqual(0);
        console.log(`  - 已取消订单时长: ${order.duration}分钟 ✅`);
      } else if (order.status === 'completed') {
        // 已完成的订单应该有时长计算
        expect(order.duration).toBeDefined();
        expect(order.duration).toBeGreaterThanOrEqual(0);
        console.log(`  - 已完成订单时长: ${order.duration}分钟 ✅`);
      } else if (order.status === 'paid' || order.status === 'in_use') {
        // 进行中的订单应该有当前时长
        expect(order.duration).toBeDefined();
        expect(order.duration).toBeGreaterThanOrEqual(0);
        console.log(`  - 进行中订单时长: ${order.duration}分钟 ✅`);
      }
    });
    
    console.log(`✅ 使用时长计算正确`);
  });

  // ==================== 测试4: 创建完整订单流程验证 ====================
  test('✅ 创建完整订单流程验证', async ({ request }) => {
    console.log('🔧 测试创建完整订单流程');
    
    // 1. 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'display_test_user_' + Date.now(),
        nickname: '显示测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const userId = userData.data.id;
    
    // 2. 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'DISPLAY_TEST_' + Date.now(),
        name: '显示测试房间',
        description: '用于测试订单显示功能',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const roomId = roomData.data.id;
    
    // 3. 创建订单
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': userId.toString() },
      data: {
        room_id: roomId,
        total_amount: 80.00
      }
    });
    expect(orderResponse.status()).toBe(200);
    const orderData = await orderResponse.json();
    const orderId = orderData.data.id;
    
    console.log(`📝 创建订单成功，ID: ${orderId}`);
    
    // 4. 验证新创建订单的显示信息
    const getOrderResponse = await request.get(`${API_BASE}/orders/${orderId}`);
    expect(getOrderResponse.status()).toBe(200);
    const getOrderData = await getOrderResponse.json();
    const order = getOrderData.data;
    
    // 验证订单号格式
    expect(order.order_number).toMatch(/^MJ\d{8}\d{3}$/);
    console.log(`📝 订单号: ${order.order_number} ✅`);
    
    // 验证房间信息
    expect(order.room).toBeDefined();
    expect(order.room.name).toBe('显示测试房间');
    console.log(`📝 房间信息: ${order.room.name} ✅`);
    
    // 验证用户信息
    expect(order.user).toBeDefined();
    expect(order.user.nickname).toBe('显示测试用户');
    console.log(`📝 用户信息: ${order.user.nickname} ✅`);
    
    // 验证时长计算（新订单应该有很短的时长）
    expect(order.duration).toBeDefined();
    expect(order.duration).toBeGreaterThanOrEqual(0);
    console.log(`📝 订单时长: ${order.duration}分钟 ✅`);
    
    // 5. 取消订单
    const cancelResponse = await request.post(`${API_BASE}/orders/${orderId}/cancel`);
    expect(cancelResponse.status()).toBe(200);
    
    // 6. 验证取消后的订单信息
    const cancelledOrderResponse = await request.get(`${API_BASE}/orders/${orderId}`);
    expect(cancelledOrderResponse.status()).toBe(200);
    const cancelledOrderData = await cancelledOrderResponse.json();
    const cancelledOrder = cancelledOrderData.data;
    
    expect(cancelledOrder.status).toBe('cancelled');
    expect(cancelledOrder.duration).toBeDefined();
    expect(cancelledOrder.duration).toBeGreaterThanOrEqual(0);
    console.log(`📝 取消后订单时长: ${cancelledOrder.duration}分钟 ✅`);
    
    // 7. 删除房间
    const deleteResponse = await request.delete(`${API_BASE}/admin/rooms/${roomId}`);
    expect(deleteResponse.status()).toBe(200);
    
    // 8. 验证删除房间后的订单信息
    const deletedRoomOrderResponse = await request.get(`${API_BASE}/orders/${orderId}`);
    expect(deletedRoomOrderResponse.status()).toBe(200);
    const deletedRoomOrderData = await deletedRoomOrderResponse.json();
    const deletedRoomOrder = deletedRoomOrderData.data;
    
    expect(deletedRoomOrder.room_id).toBeNull();
    expect(deletedRoomOrder.room).toBeUndefined();
    console.log(`📝 删除房间后订单信息正确 ✅`);
    
    console.log(`✅ 完整订单流程验证通过`);
  });

  // ==================== 测试5: 前端页面显示验证 ====================
  test('✅ 前端页面显示验证', async ({ page }) => {
    console.log('🔧 测试前端页面显示');
    
    // 导航到订单列表页面
    await page.goto('http://localhost:3001');
    
    // 等待页面加载
    await page.waitForTimeout(2000);
    
    // 导航到订单管理页面
    await page.click('text=订单管理');
    await page.waitForTimeout(1000);
    
    // 检查页面是否正常加载
    const title = await page.textContent('h2');
    expect(title).toContain('订单');
    
    // 检查表格是否存在
    const table = await page.locator('.el-table').first();
    await expect(table).toBeVisible();
    
    // 检查订单号列是否存在
    const orderNumberHeader = await page.locator('text=订单号').first();
    await expect(orderNumberHeader).toBeVisible();
    
    // 检查房间列是否存在
    const roomHeader = await page.locator('text=房间').first();
    await expect(roomHeader).toBeVisible();
    
    // 检查使用时长列是否存在
    const durationHeader = await page.locator('text=使用时长').first();
    await expect(durationHeader).toBeVisible();
    
    console.log(`✅ 前端页面显示正常`);
  });

  test.afterAll(async () => {
    console.log('🏁 订单列表显示修复验证完成');
    console.log('📋 验证总结:');
    console.log('  ✅ 订单号格式 - 规范化为MJ+日期+序号格式');
    console.log('  ✅ 房间信息 - 正确显示房间名称或"房间已删除"');
    console.log('  ✅ 使用时长 - 正确计算各种状态订单的时长');
    console.log('  ✅ 完整流程 - 创建、取消、删除房间后显示正确');
    console.log('  ✅ 前端页面 - 界面正常显示');
    console.log('🎉 订单列表显示功能完全修复！');
    console.log('💡 现在订单信息显示规范、完整、用户友好');
  });

});
