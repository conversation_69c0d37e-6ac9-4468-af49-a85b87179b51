// 棋牌室管理系统 - 全面API测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

// 测试数据
const testData = {
  user: {
    openid: 'test_user_' + Date.now(),
    nickname: '测试用户',
    avatar_url: 'https://example.com/avatar.jpg',
    phone: '13800138000'
  },
  room: {
    room_number: 'TEST_' + Date.now(),
    name: '测试房间',
    description: '这是一个测试房间',
    pricing_rule_id: 1
  }
};

let testUserId = null;
let testRoomId = null;
let testOrderId = null;

test.describe('🧪 棋牌室管理系统 API 全面测试', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始API全面测试');
    console.log('📊 测试目标: 验证所有API端点的功能性、可靠性和安全性');
  });

  // ==================== 系统状态测试 ====================
  test('🔍 系统状态检查', async ({ request }) => {
    console.log('🔧 测试系统状态API');
    
    const response = await request.get(`${API_BASE}/system/status`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('server');
    expect(data.data).toHaveProperty('database');
    expect(data.data.server.status).toBe('running');
    expect(data.data.database.status).toBe('connected');
    
    console.log('✅ 系统状态正常');
  });

  test('🔧 系统配置检查', async ({ request }) => {
    console.log('⚙️ 测试系统配置API');
    
    const response = await request.get(`${API_BASE}/system/config`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('version');
    
    console.log('✅ 系统配置获取成功');
  });

  // ==================== 用户管理测试 ====================
  test('👤 用户注册测试', async ({ request }) => {
    console.log('📝 测试用户注册API');
    
    const response = await request.post(`${API_BASE}/users/register`, {
      data: testData.user
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.openid).toBe(testData.user.openid);
    expect(data.data.nickname).toBe(testData.user.nickname);
    
    testUserId = data.data.id;
    console.log(`✅ 用户注册成功，用户ID: ${testUserId}`);
  });

  test('🔐 用户登录测试', async ({ request }) => {
    console.log('🔑 测试用户登录API');
    
    const response = await request.post(`${API_BASE}/users/login`, {
      data: { openid: testData.user.openid }
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.openid).toBe(testData.user.openid);
    
    console.log('✅ 用户登录成功');
  });

  test('👤 用户资料获取测试', async ({ request }) => {
    console.log('📋 测试用户资料获取API');
    
    const response = await request.get(`${API_BASE}/users/profile`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.id).toBe(testUserId);
    
    console.log('✅ 用户资料获取成功');
  });

  test('💰 用户充值测试', async ({ request }) => {
    console.log('💳 测试用户充值API');
    
    const response = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 100.00,
        payment_method: 'wechat'
      }
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    
    console.log('✅ 用户充值成功');
  });

  test('📊 余额记录查询测试', async ({ request }) => {
    console.log('📈 测试余额记录查询API');
    
    const response = await request.get(`${API_BASE}/users/balance-records`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(Array.isArray(data.data.data)).toBe(true);
    
    console.log('✅ 余额记录查询成功');
  });

  // ==================== 房间管理测试 ====================
  test('🏠 房间列表获取测试', async ({ request }) => {
    console.log('🏘️ 测试房间列表获取API');
    
    const response = await request.get(`${API_BASE}/rooms`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    expect(Array.isArray(data.data.data)).toBe(true);
    expect(data.data.total).toBeGreaterThan(0);
    
    console.log(`✅ 房间列表获取成功，共${data.data.total}个房间`);
  });

  test('🏠 可用房间获取测试', async ({ request }) => {
    console.log('🟢 测试可用房间获取API');
    
    const response = await request.get(`${API_BASE}/rooms/available`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(Array.isArray(data.data)).toBe(true);
    
    console.log(`✅ 可用房间获取成功，共${data.data.length}个可用房间`);
  });

  test('🏠 房间详情获取测试', async ({ request }) => {
    console.log('🔍 测试房间详情获取API');
    
    const response = await request.get(`${API_BASE}/rooms/1`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.id).toBe(1);
    expect(data.data).toHaveProperty('room_number');
    expect(data.data).toHaveProperty('name');
    
    console.log('✅ 房间详情获取成功');
  });

  test('🔧 房间设备信息获取测试', async ({ request }) => {
    console.log('⚙️ 测试房间设备信息获取API');
    
    const response = await request.get(`${API_BASE}/rooms/1/devices`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('room');
    expect(data.data).toHaveProperty('devices');
    
    console.log('✅ 房间设备信息获取成功');
  });

  // ==================== 管理端房间管理测试 ====================
  test('🏠 创建房间测试 (管理端)', async ({ request }) => {
    console.log('➕ 测试创建房间API (管理端)');
    
    const response = await request.post(`${API_BASE}/admin/rooms`, {
      data: testData.room
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.room_number).toBe(testData.room.room_number);
    expect(data.data.name).toBe(testData.room.name);
    
    testRoomId = data.data.id;
    console.log(`✅ 房间创建成功，房间ID: ${testRoomId}`);
  });

  test('🏠 更新房间测试 (管理端)', async ({ request }) => {
    console.log('✏️ 测试更新房间API (管理端)');
    
    const updateData = {
      name: '更新后的测试房间',
      description: '这是更新后的测试房间描述'
    };
    
    const response = await request.put(`${API_BASE}/admin/rooms/${testRoomId}`, {
      data: updateData
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.name).toBe(updateData.name);
    
    console.log('✅ 房间更新成功');
  });

  test('🏠 房间状态更新测试 (管理端)', async ({ request }) => {
    console.log('🔄 测试房间状态更新API (管理端)');
    
    const response = await request.put(`${API_BASE}/admin/rooms/${testRoomId}/status`, {
      data: { status: 'maintenance' }
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    
    console.log('✅ 房间状态更新成功');
  });

  test('📊 房间统计数据测试 (管理端)', async ({ request }) => {
    console.log('📈 测试房间统计数据API (管理端)');
    
    const response = await request.get(`${API_BASE}/admin/rooms/statistics`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('total');
    expect(data.data).toHaveProperty('available');
    expect(data.data).toHaveProperty('occupied');
    expect(data.data).toHaveProperty('maintenance');
    
    console.log('✅ 房间统计数据获取成功');
  });

  // ==================== 订单管理测试 ====================
  test('📋 创建订单测试', async ({ request }) => {
    console.log('📝 测试创建订单API');
    
    // 先将房间状态改回可用
    await request.put(`${API_BASE}/admin/rooms/${testRoomId}/status`, {
      data: { status: 'available' }
    });
    
    const orderData = {
      room_id: testRoomId,
      start_time: new Date().toISOString(),
      total_amount: 50.00
    };
    
    const response = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: orderData
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.room_id).toBe(testRoomId);
    expect(data.data.user_id).toBe(testUserId);
    
    testOrderId = data.data.id;
    console.log(`✅ 订单创建成功，订单ID: ${testOrderId}`);
  });

  test('📋 订单详情获取测试', async ({ request }) => {
    console.log('🔍 测试订单详情获取API');
    
    const response = await request.get(`${API_BASE}/orders/${testOrderId}`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data.id).toBe(testOrderId);
    expect(data.data).toHaveProperty('user');
    expect(data.data).toHaveProperty('room');
    
    console.log('✅ 订单详情获取成功');
  });

  test('📋 用户订单列表获取测试', async ({ request }) => {
    console.log('📊 测试用户订单列表获取API');
    
    const response = await request.get(`${API_BASE}/orders/user`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    expect(Array.isArray(data.data.data)).toBe(true);
    
    console.log('✅ 用户订单列表获取成功');
  });

  test('💳 订单支付测试', async ({ request }) => {
    console.log('💰 测试订单支付API');
    
    const response = await request.post(`${API_BASE}/orders/${testOrderId}/payment`, {
      data: {
        payment_method: 'balance',
        amount: 50.00
      }
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data.code).toBe(200);
    
    console.log('✅ 订单支付成功');
  });

  // ==================== 管理端API测试 ====================
  test('📊 仪表盘数据测试 (管理端)', async ({ request }) => {
    console.log('📈 测试仪表盘数据API (管理端)');
    
    const response = await request.get(`${API_BASE}/admin/dashboard`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('todayIncome');
    expect(data.data).toHaveProperty('totalRooms');
    expect(data.data).toHaveProperty('occupiedRooms');
    expect(data.data).toHaveProperty('availableRooms');
    
    console.log('✅ 仪表盘数据获取成功');
  });

  test('👥 用户列表获取测试 (管理端)', async ({ request }) => {
    console.log('👤 测试用户列表获取API (管理端)');
    
    const response = await request.get(`${API_BASE}/admin/users`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    expect(data.data).toHaveProperty('total');
    expect(Array.isArray(data.data.data)).toBe(true);
    
    console.log('✅ 用户列表获取成功');
  });

  test('📊 用户统计数据测试 (管理端)', async ({ request }) => {
    console.log('📈 测试用户统计数据API (管理端)');
    
    const response = await request.get(`${API_BASE}/admin/users/stats`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('totalUsers');
    expect(data.data).toHaveProperty('newUsersToday');
    expect(data.data).toHaveProperty('activeUsers');
    
    console.log('✅ 用户统计数据获取成功');
  });

  // ==================== 清理测试数据 ====================
  test('🧹 清理测试数据', async ({ request }) => {
    console.log('🗑️ 清理测试数据');
    
    // 结束订单
    if (testOrderId) {
      await request.put(`${API_BASE}/orders/${testOrderId}/end`);
      console.log('✅ 测试订单已结束');
    }
    
    // 删除测试房间
    if (testRoomId) {
      await request.delete(`${API_BASE}/admin/rooms/${testRoomId}`);
      console.log('✅ 测试房间已删除');
    }
    
    console.log('🎉 测试数据清理完成');
  });

  test.afterAll(async () => {
    console.log('🏁 API全面测试完成');
    console.log('📋 测试总结:');
    console.log('  ✅ 系统状态检查');
    console.log('  ✅ 用户管理功能');
    console.log('  ✅ 房间管理功能');
    console.log('  ✅ 订单管理功能');
    console.log('  ✅ 管理端功能');
    console.log('  ✅ 数据清理');
  });

});
