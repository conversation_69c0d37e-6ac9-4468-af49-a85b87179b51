const { test, expect } = require('@playwright/test');

test('Dashboard重构后界面验证', async ({ page }) => {
  console.log('=== Dashboard重构后界面验证测试 ===');
  
  // 1. 获取后端数据作为基准
  console.log('1. 获取后端API数据...');
  const apiResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const apiData = await apiResponse.json();
  const backendData = apiData.data;
  
  console.log('后端数据:', {
    activeOrders: backendData.activeOrders,
    todayIncome: backendData.todayIncome,
    onlineDevices: backendData.onlineDevices,
    totalRooms: backendData.totalRooms,
    availableRooms: backendData.availableRooms
  });
  
  // 2. 访问重构后的Dashboard
  console.log('2. 访问重构后的Dashboard...');
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  // 3. 验证新的布局结构
  console.log('3. 验证新的布局结构...');
  
  // 检查概览区域
  const overviewSection = await page.locator('.overview-section');
  expect(await overviewSection.count()).toBe(1);
  console.log('✅ 概览区域存在');
  
  // 检查指标卡片
  const metricsCard = await page.locator('.metrics-card');
  expect(await metricsCard.count()).toBe(1);
  console.log('✅ 指标卡片存在');
  
  // 检查快捷操作卡片
  const actionsCard = await page.locator('.actions-card');
  expect(await actionsCard.count()).toBe(1);
  console.log('✅ 快捷操作卡片存在');
  
  // 检查监控区域
  const monitoringSection = await page.locator('.monitoring-section');
  expect(await monitoringSection.count()).toBe(1);
  console.log('✅ 监控区域存在');
  
  // 检查分析区域
  const analyticsSection = await page.locator('.analytics-section');
  expect(await analyticsSection.count()).toBe(1);
  console.log('✅ 分析区域存在');
  
  // 4. 验证核心指标数据
  console.log('4. 验证核心指标数据...');
  
  const metricItems = await page.locator('.metric-item');
  const metricCount = await metricItems.count();
  console.log(`指标项数量: ${metricCount}`);
  expect(metricCount).toBe(4);
  
  // 验证今日收入
  const incomeMetric = await page.locator('.metric-item').filter({ hasText: '今日收入' });
  if (await incomeMetric.count() > 0) {
    const incomeValue = await incomeMetric.locator('.metric-value').textContent();
    console.log(`今日收入显示: ${incomeValue}`);
    expect(incomeValue).toContain(backendData.todayIncome.toFixed(2));
  }
  
  // 验证活跃订单
  const ordersMetric = await page.locator('.metric-item').filter({ hasText: '活跃订单' });
  if (await ordersMetric.count() > 0) {
    const ordersValue = await ordersMetric.locator('.metric-value').textContent();
    console.log(`活跃订单显示: ${ordersValue}`);
    expect(ordersValue.trim()).toBe(backendData.activeOrders.toString());
  }
  
  // 验证房间状态
  const roomsMetric = await page.locator('.metric-item').filter({ hasText: '空闲房间' });
  if (await roomsMetric.count() > 0) {
    const roomsValue = await roomsMetric.locator('.metric-value').textContent();
    console.log(`空闲房间显示: ${roomsValue}`);
    expect(roomsValue).toContain(backendData.availableRooms.toString());
    expect(roomsValue).toContain(backendData.totalRooms.toString());
  }
  
  // 验证设备状态
  const devicesMetric = await page.locator('.metric-item').filter({ hasText: '在线设备' });
  if (await devicesMetric.count() > 0) {
    const devicesValue = await devicesMetric.locator('.metric-value').textContent();
    console.log(`在线设备显示: ${devicesValue}`);
    expect(devicesValue).toContain(backendData.onlineDevices.toString());
  }
  
  // 5. 验证快捷操作
  console.log('5. 验证快捷操作...');
  
  const actionItems = await page.locator('.action-item');
  const actionCount = await actionItems.count();
  console.log(`快捷操作数量: ${actionCount}`);
  expect(actionCount).toBe(4);
  
  // 验证操作项文本
  const actionTexts = [];
  for (let i = 0; i < actionCount; i++) {
    const text = await actionItems.nth(i).locator('span').textContent();
    actionTexts.push(text);
  }
  console.log('快捷操作项:', actionTexts);
  expect(actionTexts).toContain('订单管理');
  expect(actionTexts).toContain('房间管理');
  expect(actionTexts).toContain('设备管理');
  expect(actionTexts).toContain('财务报表');
  
  // 6. 验证图表区域
  console.log('6. 验证图表区域...');
  
  // 检查实时图表
  const realtimeChart = await page.locator('.realtime-chart-container .realtime-chart');
  expect(await realtimeChart.count()).toBe(1);
  console.log('✅ 实时图表存在');
  
  // 检查房间状态图表
  const roomChart = await page.locator('.chart-content').first();
  expect(await roomChart.count()).toBe(1);
  console.log('✅ 房间状态图表存在');
  
  // 检查收入趋势图表
  const incomeChart = await page.locator('.chart-content').last();
  expect(await incomeChart.count()).toBe(1);
  console.log('✅ 收入趋势图表存在');
  
  // 7. 验证响应式设计
  console.log('7. 验证响应式设计...');
  
  // 测试移动端视图
  await page.setViewportSize({ width: 375, height: 667 });
  await page.waitForTimeout(1000);
  
  // 检查移动端布局
  const mobileMetrics = await page.locator('.metric-item');
  const mobileMetricCount = await mobileMetrics.count();
  console.log(`移动端指标数量: ${mobileMetricCount}`);
  expect(mobileMetricCount).toBe(4);
  
  // 恢复桌面视图
  await page.setViewportSize({ width: 1280, height: 720 });
  await page.waitForTimeout(1000);
  
  // 8. 验证交互功能
  console.log('8. 验证交互功能...');
  
  // 测试刷新按钮
  const refreshButton = await page.locator('.header-actions .el-button').first();
  if (await refreshButton.count() > 0) {
    console.log('点击刷新按钮...');
    await refreshButton.click();
    await page.waitForTimeout(2000);
    console.log('✅ 刷新功能正常');
  }
  
  // 测试自动刷新开关
  const autoRefreshSwitch = await page.locator('.el-switch');
  if (await autoRefreshSwitch.count() > 0) {
    console.log('自动刷新开关存在');
    const isChecked = await autoRefreshSwitch.locator('input').isChecked();
    console.log(`自动刷新状态: ${isChecked ? '开启' : '关闭'}`);
  }
  
  // 9. 验证无重复内容
  console.log('9. 验证无重复内容...');
  
  // 确保没有重复的快捷操作区域
  const quickActionsCards = await page.locator('.quick-actions, .actions-card');
  const quickActionsCount = await quickActionsCards.count();
  console.log(`快捷操作区域数量: ${quickActionsCount}`);
  expect(quickActionsCount).toBeLessThanOrEqual(1);
  
  // 确保没有重复的指标显示
  const allMetricItems = await page.locator('.metric-item, .stat-card');
  const allMetricCount = await allMetricItems.count();
  console.log(`所有指标项数量: ${allMetricCount}`);
  // 应该只有4个指标项（在metrics-card中）
  expect(allMetricCount).toBe(4);
  
  console.log('=== Dashboard重构验证完成 ===');
  console.log('🎉 新的Dashboard布局清晰、数据准确、无重复内容！');
});
