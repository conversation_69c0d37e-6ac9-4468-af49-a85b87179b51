// 棋牌室管理系统 - 数据库操作验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

// 测试数据
const testData = {
  user: {
    openid: 'db_test_user_' + Date.now(),
    nickname: '数据库测试用户',
    avatar_url: 'https://example.com/avatar.jpg',
    phone: '13900139000'
  },
  room: {
    room_number: 'DB_TEST_' + Date.now(),
    name: '数据库测试房间',
    description: '用于数据库操作验证的测试房间',
    pricing_rule_id: 1
  }
};

let testUserId = null;
let testRoomId = null;
let testOrderId = null;
let initialBalance = 0;

test.describe('🗄️ 数据库操作验证测试', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始数据库操作验证测试');
    console.log('🎯 测试目标: 验证CRUD操作、数据一致性、事务处理、约束条件');
  });

  // ==================== 用户数据CRUD测试 ====================
  test('👤 用户数据CRUD操作测试', async ({ request }) => {
    console.log('📝 测试用户数据CRUD操作');
    
    // CREATE - 创建用户
    console.log('➕ 测试用户创建');
    const createResponse = await request.post(`${API_BASE}/users/register`, {
      data: testData.user
    });
    expect(createResponse.status()).toBe(200);
    const createData = await createResponse.json();
    expect(createData.code).toBe(200);
    expect(createData.data.openid).toBe(testData.user.openid);
    
    testUserId = createData.data.id;
    initialBalance = createData.data.balance;
    console.log(`✅ 用户创建成功，ID: ${testUserId}, 初始余额: ${initialBalance}`);
    
    // READ - 读取用户
    console.log('🔍 测试用户读取');
    const readResponse = await request.get(`${API_BASE}/users/profile`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    expect(readResponse.status()).toBe(200);
    const readData = await readResponse.json();
    expect(readData.code).toBe(200);
    expect(readData.data.id).toBe(testUserId);
    expect(readData.data.openid).toBe(testData.user.openid);
    console.log('✅ 用户读取成功');
    
    // UPDATE - 更新用户
    console.log('✏️ 测试用户更新');
    const updateData = {
      nickname: '更新后的数据库测试用户',
      phone: '13900139001'
    };
    const updateResponse = await request.put(`${API_BASE}/users/profile`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: updateData
    });
    expect(updateResponse.status()).toBe(200);
    const updatedData = await updateResponse.json();
    expect(updatedData.code).toBe(200);
    expect(updatedData.data.nickname).toBe(updateData.nickname);
    expect(updatedData.data.phone).toBe(updateData.phone);
    console.log('✅ 用户更新成功');
    
    // 验证更新后的数据
    const verifyResponse = await request.get(`${API_BASE}/users/profile`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    const verifyData = await verifyResponse.json();
    expect(verifyData.data.nickname).toBe(updateData.nickname);
    console.log('✅ 用户数据更新验证成功');
  });

  // ==================== 房间数据CRUD测试 ====================
  test('🏠 房间数据CRUD操作测试', async ({ request }) => {
    console.log('🏘️ 测试房间数据CRUD操作');
    
    // CREATE - 创建房间
    console.log('➕ 测试房间创建');
    const createResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: testData.room
    });
    expect(createResponse.status()).toBe(200);
    const createData = await createResponse.json();
    expect(createData.code).toBe(200);
    expect(createData.data.room_number).toBe(testData.room.room_number);
    
    testRoomId = createData.data.id;
    console.log(`✅ 房间创建成功，ID: ${testRoomId}`);
    
    // READ - 读取房间
    console.log('🔍 测试房间读取');
    const readResponse = await request.get(`${API_BASE}/rooms/${testRoomId}`);
    expect(readResponse.status()).toBe(200);
    const readData = await readResponse.json();
    expect(readData.code).toBe(200);
    expect(readData.data.id).toBe(testRoomId);
    expect(readData.data.room_number).toBe(testData.room.room_number);
    console.log('✅ 房间读取成功');
    
    // UPDATE - 更新房间
    console.log('✏️ 测试房间更新');
    const updateData = {
      name: '更新后的数据库测试房间',
      description: '这是更新后的房间描述',
      status: 'maintenance'
    };
    const updateResponse = await request.put(`${API_BASE}/admin/rooms/${testRoomId}`, {
      data: updateData
    });
    expect(updateResponse.status()).toBe(200);
    const updatedData = await updateResponse.json();
    expect(updatedData.code).toBe(200);
    expect(updatedData.data.name).toBe(updateData.name);
    console.log('✅ 房间更新成功');
    
    // 验证更新后的数据
    const verifyResponse = await request.get(`${API_BASE}/rooms/${testRoomId}`);
    const verifyData = await verifyResponse.json();
    expect(verifyData.data.name).toBe(updateData.name);
    console.log('✅ 房间数据更新验证成功');
  });

  // ==================== 数据一致性测试 ====================
  test('🔄 数据一致性验证测试', async ({ request }) => {
    console.log('🔄 测试数据一致性');
    
    // 测试用户余额变动的一致性
    console.log('💰 测试余额变动一致性');
    
    // 获取初始余额
    const initialResponse = await request.get(`${API_BASE}/users/profile`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    const initialData = await initialResponse.json();
    const beforeBalance = initialData.data.balance;
    
    // 执行充值操作
    const rechargeAmount = 100.00;
    const rechargeResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: rechargeAmount,
        payment_method: 'wechat'
      }
    });
    expect(rechargeResponse.status()).toBe(200);
    
    // 验证余额更新
    const afterResponse = await request.get(`${API_BASE}/users/profile`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    const afterData = await afterResponse.json();
    const afterBalance = afterData.data.balance;
    
    expect(afterBalance).toBe(beforeBalance + rechargeAmount);
    console.log(`✅ 余额一致性验证成功: ${beforeBalance} + ${rechargeAmount} = ${afterBalance}`);
    
    // 验证余额记录
    const recordsResponse = await request.get(`${API_BASE}/users/balance-records`, {
      headers: { 'X-User-ID': testUserId.toString() }
    });
    const recordsData = await recordsResponse.json();
    expect(recordsData.code).toBe(200);
    expect(recordsData.data.data.length).toBeGreaterThan(0);
    
    // 检查最新的充值记录
    const latestRecord = recordsData.data.data[0];
    expect(latestRecord.type).toBe('recharge');
    expect(latestRecord.amount).toBe(rechargeAmount);
    console.log('✅ 余额记录一致性验证成功');
  });

  // ==================== 外键约束测试 ====================
  test('🔗 外键约束验证测试', async ({ request }) => {
    console.log('🔗 测试外键约束');
    
    // 将房间状态改为可用以便创建订单
    await request.put(`${API_BASE}/admin/rooms/${testRoomId}/status`, {
      data: { status: 'available' }
    });
    
    // 创建订单测试外键约束
    console.log('📋 测试订单外键约束');
    const orderData = {
      room_id: testRoomId,
      start_time: new Date().toISOString(),
      total_amount: 50.00
    };
    
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: orderData
    });
    expect(orderResponse.status()).toBe(200);
    const orderResult = await orderResponse.json();
    expect(orderResult.code).toBe(200);
    expect(orderResult.data.user_id).toBe(testUserId);
    expect(orderResult.data.room_id).toBe(testRoomId);
    
    testOrderId = orderResult.data.id;
    console.log(`✅ 订单外键约束验证成功，订单ID: ${testOrderId}`);
    
    // 验证订单详情包含关联数据
    const orderDetailResponse = await request.get(`${API_BASE}/orders/${testOrderId}`);
    const orderDetailData = await orderDetailResponse.json();
    expect(orderDetailData.code).toBe(200);
    expect(orderDetailData.data).toHaveProperty('user');
    expect(orderDetailData.data).toHaveProperty('room');
    expect(orderDetailData.data.user.id).toBe(testUserId);
    expect(orderDetailData.data.room.id).toBe(testRoomId);
    console.log('✅ 订单关联数据验证成功');
  });

  // ==================== 唯一约束测试 ====================
  test('🔒 唯一约束验证测试', async ({ request }) => {
    console.log('🔒 测试唯一约束');
    
    // 测试用户openid唯一约束
    console.log('👤 测试用户openid唯一约束');
    const duplicateUserResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: testData.user.openid, // 使用相同的openid
        nickname: '重复用户',
        phone: '13900139999'
      }
    });
    
    // 应该返回已存在的用户，而不是创建新用户
    expect(duplicateUserResponse.status()).toBe(200);
    const duplicateData = await duplicateUserResponse.json();
    expect(duplicateData.code).toBe(200);
    expect(duplicateData.data.id).toBe(testUserId); // 应该是同一个用户ID
    console.log('✅ 用户openid唯一约束验证成功');
    
    // 测试房间号唯一约束
    console.log('🏠 测试房间号唯一约束');
    const duplicateRoomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: testData.room.room_number, // 使用相同的房间号
        name: '重复房间',
        description: '这是重复的房间'
      }
    });
    
    // 应该返回错误，因为房间号重复
    expect(duplicateRoomResponse.status()).toBe(500);
    console.log('✅ 房间号唯一约束验证成功');
  });

  // ==================== 数据类型验证测试 ====================
  test('📊 数据类型验证测试', async ({ request }) => {
    console.log('📊 测试数据类型验证');
    
    // 测试无效的金额类型
    console.log('💰 测试无效金额类型');
    const invalidAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 'invalid_amount', // 无效的金额类型
        payment_method: 'wechat'
      }
    });
    
    // 应该返回验证错误
    expect(invalidAmountResponse.status()).toBe(400);
    console.log('✅ 无效金额类型验证成功');
    
    // 测试负数金额
    console.log('💰 测试负数金额');
    const negativeAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: -50.00, // 负数金额
        payment_method: 'wechat'
      }
    });
    
    // 应该返回验证错误
    expect(negativeAmountResponse.status()).toBe(400);
    console.log('✅ 负数金额验证成功');
  });

  // ==================== 分页查询测试 ====================
  test('📄 分页查询验证测试', async ({ request }) => {
    console.log('📄 测试分页查询');
    
    // 测试房间列表分页
    console.log('🏠 测试房间列表分页');
    const page1Response = await request.get(`${API_BASE}/rooms?page=1&page_size=5`);
    expect(page1Response.status()).toBe(200);
    const page1Data = await page1Response.json();
    expect(page1Data.code).toBe(200);
    expect(page1Data.data).toHaveProperty('page');
    expect(page1Data.data).toHaveProperty('page_size');
    expect(page1Data.data).toHaveProperty('total');
    expect(page1Data.data).toHaveProperty('total_pages');
    expect(page1Data.data.page).toBe(1);
    expect(page1Data.data.page_size).toBe(5);
    console.log(`✅ 房间分页查询成功: 第${page1Data.data.page}页，共${page1Data.data.total}条记录`);
    
    // 测试用户列表分页
    console.log('👥 测试用户列表分页');
    const userPageResponse = await request.get(`${API_BASE}/admin/users?page=1&page_size=10`);
    expect(userPageResponse.status()).toBe(200);
    const userPageData = await userPageResponse.json();
    expect(userPageData.code).toBe(200);
    expect(userPageData.data).toHaveProperty('page');
    expect(userPageData.data).toHaveProperty('total');
    console.log(`✅ 用户分页查询成功: 第${userPageData.data.page}页，共${userPageData.data.total}条记录`);
  });

  // ==================== 数据删除测试 ====================
  test('🗑️ 数据删除验证测试', async ({ request }) => {
    console.log('🗑️ 测试数据删除');
    
    // 先结束订单
    if (testOrderId) {
      console.log('📋 结束测试订单');
      const endOrderResponse = await request.put(`${API_BASE}/orders/${testOrderId}/end`);
      expect(endOrderResponse.status()).toBe(200);
      console.log('✅ 订单结束成功');
    }
    
    // 删除测试房间
    if (testRoomId) {
      console.log('🏠 删除测试房间');
      const deleteRoomResponse = await request.delete(`${API_BASE}/admin/rooms/${testRoomId}`);
      expect(deleteRoomResponse.status()).toBe(200);
      console.log('✅ 房间删除成功');
      
      // 验证房间已被删除
      const verifyDeleteResponse = await request.get(`${API_BASE}/rooms/${testRoomId}`);
      expect(verifyDeleteResponse.status()).toBe(404);
      console.log('✅ 房间删除验证成功');
    }
  });

  // ==================== 数据统计验证测试 ====================
  test('📈 数据统计验证测试', async ({ request }) => {
    console.log('📈 测试数据统计');
    
    // 测试用户统计
    console.log('👥 测试用户统计');
    const userStatsResponse = await request.get(`${API_BASE}/admin/users/stats`);
    expect(userStatsResponse.status()).toBe(200);
    const userStatsData = await userStatsResponse.json();
    expect(userStatsData.code).toBe(200);
    expect(userStatsData.data).toHaveProperty('total_users');
    expect(userStatsData.data).toHaveProperty('new_users_today');
    expect(userStatsData.data).toHaveProperty('active_users');
    expect(userStatsData.data.total_users).toBeGreaterThan(0);
    console.log(`✅ 用户统计验证成功: 总用户${userStatsData.data.total_users}人，今日新增${userStatsData.data.new_users_today}人`);
    
    // 测试房间统计
    console.log('🏠 测试房间统计');
    const roomStatsResponse = await request.get(`${API_BASE}/admin/rooms/statistics`);
    expect(roomStatsResponse.status()).toBe(200);
    const roomStatsData = await roomStatsResponse.json();
    expect(roomStatsData.code).toBe(200);
    expect(roomStatsData.data).toHaveProperty('total');
    expect(roomStatsData.data).toHaveProperty('available');
    expect(roomStatsData.data).toHaveProperty('occupied');
    expect(roomStatsData.data).toHaveProperty('maintenance');
    console.log(`✅ 房间统计验证成功: 总房间${roomStatsData.data.total}个`);
  });

  test.afterAll(async () => {
    console.log('🏁 数据库操作验证测试完成');
    console.log('📋 测试总结:');
    console.log('  ✅ 用户数据CRUD操作');
    console.log('  ✅ 房间数据CRUD操作');
    console.log('  ✅ 数据一致性验证');
    console.log('  ✅ 外键约束验证');
    console.log('  ✅ 唯一约束验证');
    console.log('  ✅ 数据类型验证');
    console.log('  ✅ 分页查询验证');
    console.log('  ✅ 数据删除验证');
    console.log('  ✅ 数据统计验证');
  });

});
