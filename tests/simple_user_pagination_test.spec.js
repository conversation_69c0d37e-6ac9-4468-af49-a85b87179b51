// 简单用户分页测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🎯 简单用户分页测试', () => {

  // ==================== 测试1: 第四页API验证 ====================
  test('✅ 第四页API验证', async ({ request }) => {
    console.log('🔧 测试第四页API');
    
    const response = await request.get(`${API_BASE}/admin/users?page=4&page_size=10`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.data).toHaveProperty('data');
    expect(data.data).toHaveProperty('total');
    expect(data.data).toHaveProperty('page');
    expect(data.data.page).toBe(4);
    
    console.log(`📄 第4页: ${data.data.data.length}条记录`);
    console.log(`📊 总计: ${data.data.total}个用户，${data.data.total_pages}页`);
    console.log(`✅ 第四页API正常`);
  });

  // ==================== 测试2: 连续分页请求 ====================
  test('✅ 连续分页请求', async ({ request }) => {
    console.log('🔧 测试连续分页请求');
    
    const pages = [1, 2, 3, 4, 5];
    
    for (const page of pages) {
      const response = await request.get(`${API_BASE}/admin/users?page=${page}&page_size=10`);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.code).toBe(200);
      
      console.log(`📄 第${page}页: ${data.data.data.length}条记录`);
    }
    
    console.log(`✅ 连续分页请求正常`);
  });

  test.afterAll(async () => {
    console.log('🏁 简单用户分页测试完成');
    console.log('📋 测试结果:');
    console.log('  ✅ 第四页API - 正常');
    console.log('  ✅ 连续分页请求 - 正常');
    console.log('🎉 用户列表分页功能正常！');
  });

});
