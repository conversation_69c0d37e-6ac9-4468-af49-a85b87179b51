// 快速修复验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🔧 快速修复验证', () => {

  test('✅ 订单API数据结构修复验证', async ({ request }) => {
    console.log('🔧 验证订单API返回数据结构修复');
    
    // 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'quick_test_' + Date.now(),
        nickname: '快速测试用户'
      }
    });
    expect(userResponse.status()).toBe(200);
    const userData = await userResponse.json();
    const testUserId = userData.data.id;
    
    // 创建测试房间
    const roomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: 'QT' + Date.now(),
        name: '快速测试房间',
        description: '测试房间',
        pricing_rule_id: 1
      }
    });
    expect(roomResponse.status()).toBe(200);
    const roomData = await roomResponse.json();
    const testRoomId = roomData.data.id;
    
    // 创建订单并验证返回数据结构
    const orderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        room_id: testRoomId,
        total_amount: 50.00
      }
    });
    
    expect(orderResponse.status()).toBe(200);
    const orderResult = await orderResponse.json();
    expect(orderResult.code).toBe(200);
    
    // 验证修复后的数据结构包含user_id和room_id字段
    expect(orderResult.data).toHaveProperty('user_id');
    expect(orderResult.data).toHaveProperty('room_id');
    expect(orderResult.data.user_id).toBe(testUserId);
    expect(orderResult.data.room_id).toBe(testRoomId);
    
    console.log('✅ 订单API数据结构修复验证成功');
    
    // 清理
    await request.put(`${API_BASE}/orders/${orderResult.data.id}/end`);
    await request.delete(`${API_BASE}/admin/rooms/${testRoomId}`);
  });

  test('✅ 唯一约束处理修复验证', async ({ request }) => {
    console.log('🔧 验证唯一约束处理修复');
    
    const roomNumber = 'UNIQUE_TEST_' + Date.now();
    
    // 创建第一个房间
    const firstRoomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: roomNumber,
        name: '第一个房间',
        description: '测试房间',
        pricing_rule_id: 1
      }
    });
    expect(firstRoomResponse.status()).toBe(200);
    const firstRoomData = await firstRoomResponse.json();
    const firstRoomId = firstRoomData.data.id;
    
    // 尝试创建重复房间号的房间
    const duplicateRoomResponse = await request.post(`${API_BASE}/admin/rooms`, {
      data: {
        room_number: roomNumber, // 使用相同的房间号
        name: '重复房间',
        description: '这是重复的房间',
        pricing_rule_id: 1
      }
    });
    
    // 现在应该返回409 Conflict状态码
    expect(duplicateRoomResponse.status()).toBe(409);
    const duplicateData = await duplicateRoomResponse.json();
    expect(duplicateData.message).toBe('房间号已存在');
    
    console.log('✅ 唯一约束处理修复验证成功');
    
    // 清理
    await request.delete(`${API_BASE}/admin/rooms/${firstRoomId}`);
  });

  test('✅ API参数验证加强验证', async ({ request }) => {
    console.log('🔧 验证API参数验证加强');
    
    // 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'param_test_' + Date.now(),
        nickname: '参数测试用户'
      }
    });
    const userData = await userResponse.json();
    const testUserId = userData.data.id;
    
    // 测试负数金额验证
    const negativeAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: -50.00,
        payment_method: 'wechat'
      }
    });
    expect(negativeAmountResponse.status()).toBe(400);
    
    // 测试超大金额验证
    const largeAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 15000.00,
        payment_method: 'wechat'
      }
    });
    expect(largeAmountResponse.status()).toBe(400);
    
    // 测试无效支付方式
    const invalidPaymentResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 100.00,
        payment_method: 'invalid_method'
      }
    });
    expect(invalidPaymentResponse.status()).toBe(400);
    
    console.log('✅ API参数验证加强验证成功');
  });

  test('✅ 外键约束验证（简化版）', async ({ request }) => {
    console.log('🔧 验证外键约束（简化版）');
    
    // 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'fk_test_' + Date.now(),
        nickname: '外键测试用户'
      }
    });
    const userData = await userResponse.json();
    const testUserId = userData.data.id;
    
    // 尝试创建订单时使用不存在的房间ID
    const invalidRoomOrderResponse = await request.post(`${API_BASE}/orders`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        room_id: 99999, // 不存在的房间ID
        total_amount: 50.00
      }
    });
    
    // 应该返回错误
    expect(invalidRoomOrderResponse.status()).toBe(500);
    const roomErrorData = await invalidRoomOrderResponse.json();
    expect(roomErrorData.message).toContain('房间不存在');
    
    console.log('✅ 外键约束验证成功');
  });

  test('✅ 边界条件测试', async ({ request }) => {
    console.log('🔧 验证边界条件');
    
    // 创建测试用户
    const userResponse = await request.post(`${API_BASE}/users/register`, {
      data: {
        openid: 'boundary_test_' + Date.now(),
        nickname: '边界测试用户'
      }
    });
    const userData = await userResponse.json();
    const testUserId = userData.data.id;
    
    // 测试最小有效金额
    const minAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 0.01,
        payment_method: 'wechat'
      }
    });
    expect(minAmountResponse.status()).toBe(200);
    
    // 测试最大有效金额
    const maxAmountResponse = await request.post(`${API_BASE}/users/recharge`, {
      headers: { 'X-User-ID': testUserId.toString() },
      data: {
        amount: 10000.00,
        payment_method: 'alipay'
      }
    });
    expect(maxAmountResponse.status()).toBe(200);
    
    console.log('✅ 边界条件测试成功');
  });

});
