const { test, expect } = require('@playwright/test');

test('前端数据同步验证', async ({ page }) => {
  console.log('=== 前端数据同步验证测试 ===');
  
  // 1. 首先获取后端API数据作为基准
  console.log('1. 获取后端API基准数据...');
  const apiResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const apiData = await apiResponse.json();
  
  expect(apiResponse.status()).toBe(200);
  expect(apiData.code).toBe(200);
  
  const backendData = apiData.data;
  console.log('后端API数据:', {
    activeOrders: backendData.activeOrders,
    todayIncome: backendData.todayIncome,
    onlineDevices: backendData.onlineDevices,
    offlineDevices: backendData.offlineDevices,
    totalRooms: backendData.totalRooms,
    availableRooms: backendData.availableRooms
  });
  
  // 2. 访问前端页面
  console.log('2. 访问前端Dashboard页面...');
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('networkidle');
  
  // 等待数据加载
  await page.waitForTimeout(5000);
  
  // 3. 检查前端显示的数据
  console.log('3. 检查前端显示数据...');
  
  // 检查活跃订单
  const activeOrdersElement = await page.locator('[data-testid="active-orders"]');
  if (await activeOrdersElement.count() > 0) {
    const activeOrdersText = await activeOrdersElement.textContent();
    console.log(`前端显示活跃订单: ${activeOrdersText}`);
    console.log(`后端API活跃订单: ${backendData.activeOrders}`);
    expect(activeOrdersText.trim()).toBe(backendData.activeOrders.toString());
  }
  
  // 检查今日收入
  const todayIncomeElement = await page.locator('[data-testid="today-income"]');
  if (await todayIncomeElement.count() > 0) {
    const todayIncomeText = await todayIncomeElement.textContent();
    console.log(`前端显示今日收入: ${todayIncomeText}`);
    console.log(`后端API今日收入: ${backendData.todayIncome}`);
    expect(todayIncomeText).toContain(backendData.todayIncome.toFixed(2));
  }
  
  // 检查在线设备
  const onlineDevicesElement = await page.locator('[data-testid="online-devices"]');
  if (await onlineDevicesElement.count() > 0) {
    const onlineDevicesText = await onlineDevicesElement.textContent();
    console.log(`前端显示在线设备: ${onlineDevicesText}`);
    console.log(`后端API在线设备: ${backendData.onlineDevices}`);
    expect(onlineDevicesText.trim()).toBe(backendData.onlineDevices.toString());
  }
  
  // 检查总房间数
  const totalRoomsElement = await page.locator('[data-testid="total-rooms"]');
  if (await totalRoomsElement.count() > 0) {
    const totalRoomsText = await totalRoomsElement.textContent();
    console.log(`前端显示总房间: ${totalRoomsText}`);
    console.log(`后端API总房间: ${backendData.totalRooms}`);
    expect(totalRoomsText.trim()).toBe(backendData.totalRooms.toString());
  }
  
  // 4. 检查实时数据监控栏
  console.log('4. 检查实时数据监控栏...');
  
  // 等待实时数据组件加载
  await page.waitForSelector('.realtime-chart', { timeout: 10000 });
  
  // 检查实时监控栏中的数据
  const realtimeMetrics = await page.locator('.realtime-chart .metric-item');
  const metricsCount = await realtimeMetrics.count();
  console.log(`实时监控栏指标数量: ${metricsCount}`);
  
  if (metricsCount > 0) {
    for (let i = 0; i < metricsCount; i++) {
      const metric = realtimeMetrics.nth(i);
      const value = await metric.locator('.metric-value').textContent();
      const label = await metric.locator('.metric-label').textContent();
      console.log(`实时监控 - ${label}: ${value}`);
      
      // 验证实时监控数据与后端数据一致
      if (label.includes('活跃订单')) {
        expect(value.trim()).toBe(backendData.activeOrders.toString());
      } else if (label.includes('在线设备')) {
        expect(value.trim()).toBe(backendData.onlineDevices.toString());
      } else if (label.includes('今日收入')) {
        expect(value).toContain(backendData.todayIncome.toFixed(2));
      } else if (label.includes('在线房间')) {
        expect(value.trim()).toBe(backendData.availableRooms.toString());
      }
    }
  }
  
  // 5. 测试自动刷新功能
  console.log('5. 测试自动刷新功能...');
  
  // 检查自动刷新开关
  const autoRefreshSwitch = await page.locator('.realtime-chart .el-switch');
  if (await autoRefreshSwitch.count() > 0) {
    const isChecked = await autoRefreshSwitch.locator('input').isChecked();
    console.log(`自动刷新开关状态: ${isChecked ? '开启' : '关闭'}`);
  }
  
  // 点击手动刷新按钮
  const refreshButton = await page.locator('.realtime-chart .el-button');
  if (await refreshButton.count() > 0) {
    console.log('点击手动刷新按钮...');
    await refreshButton.click();
    await page.waitForTimeout(2000);
    console.log('手动刷新完成');
  }
  
  // 6. 验证网络请求
  console.log('6. 验证网络请求...');
  
  // 监听网络请求
  const requests = [];
  page.on('request', request => {
    if (request.url().includes('/api/v1/admin/dashboard')) {
      requests.push(request.url());
    }
  });
  
  // 刷新页面触发API调用
  await page.reload();
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  console.log(`Dashboard API调用次数: ${requests.length}`);
  expect(requests.length).toBeGreaterThan(0);
  
  console.log('=== 前端数据同步验证完成 ===');
  console.log('🎉 前端与后端数据同步正常！');
});
