const { test, expect } = require('@playwright/test');

test.describe('Dashboard Integration Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 访问Dashboard页面
    await page.goto('http://localhost:3001');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
  });

  test('Dashboard页面应该显示真实的数据库数据', async ({ page }) => {
    // 等待Dashboard数据加载
    await page.waitForSelector('.dashboard-stats', { timeout: 10000 });
    
    // 验证统计卡片是否显示
    const statsCards = await page.locator('.stat-card').count();
    expect(statsCards).toBeGreaterThan(0);
    
    // 验证今日收入数据
    const todayIncomeElement = await page.locator('[data-testid="today-income"]');
    if (await todayIncomeElement.count() > 0) {
      const todayIncomeText = await todayIncomeElement.textContent();
      console.log('今日收入:', todayIncomeText);
      // 验证收入数据不是默认的模拟数据
      expect(todayIncomeText).not.toContain('2580.50');
    }
    
    // 验证房间统计数据
    const totalRoomsElement = await page.locator('[data-testid="total-rooms"]');
    if (await totalRoomsElement.count() > 0) {
      const totalRoomsText = await totalRoomsElement.textContent();
      console.log('总房间数:', totalRoomsText);
      // 验证房间数据是真实的
      expect(totalRoomsText).toMatch(/\d+/);
    }
    
    // 验证今日订单数据
    const todayOrdersElement = await page.locator('[data-testid="today-orders"]');
    if (await todayOrdersElement.count() > 0) {
      const todayOrdersText = await todayOrdersElement.textContent();
      console.log('今日订单数:', todayOrdersText);
      expect(todayOrdersText).toMatch(/\d+/);
    }
  });

  test('房间状态图表应该显示真实数据', async ({ page }) => {
    // 等待图表加载
    await page.waitForSelector('.room-status-chart', { timeout: 10000 });
    
    // 验证图表容器存在
    const chartContainer = await page.locator('.room-status-chart');
    expect(await chartContainer.count()).toBe(1);
    
    // 验证图表是否渲染了内容
    const chartCanvas = await page.locator('.room-status-chart canvas');
    if (await chartCanvas.count() > 0) {
      console.log('房间状态图表已渲染');
    }
  });

  test('收入趋势图表应该显示真实数据', async ({ page }) => {
    // 等待收入图表加载
    await page.waitForSelector('.income-chart', { timeout: 10000 });
    
    // 验证图表容器存在
    const chartContainer = await page.locator('.income-chart');
    expect(await chartContainer.count()).toBe(1);
    
    // 验证图表是否渲染了内容
    const chartCanvas = await page.locator('.income-chart canvas');
    if (await chartCanvas.count() > 0) {
      console.log('收入趋势图表已渲染');
    }
  });

  test('API数据应该正确加载', async ({ page }) => {
    // 监听网络请求
    const dashboardApiCall = page.waitForResponse(response => 
      response.url().includes('/api/v1/admin/dashboard') && response.status() === 200
    );
    
    const roomStatsApiCall = page.waitForResponse(response => 
      response.url().includes('/api/v1/admin/rooms/statistics') && response.status() === 200
    );
    
    // 刷新页面触发API调用
    await page.reload();
    
    // 等待API响应
    const dashboardResponse = await dashboardApiCall;
    const roomStatsResponse = await roomStatsApiCall;
    
    // 验证API响应
    expect(dashboardResponse.status()).toBe(200);
    expect(roomStatsResponse.status()).toBe(200);
    
    // 验证响应数据格式
    const dashboardData = await dashboardResponse.json();
    expect(dashboardData.code).toBe(200);
    expect(dashboardData.data).toBeDefined();
    
    const roomStatsData = await roomStatsResponse.json();
    expect(roomStatsData.code).toBe(200);
    expect(roomStatsData.data).toBeDefined();
    
    console.log('Dashboard API数据:', JSON.stringify(dashboardData.data, null, 2));
    console.log('房间统计API数据:', JSON.stringify(roomStatsData.data, null, 2));
  });

  test('数据加载状态应该正确处理', async ({ page }) => {
    // 验证加载状态
    const loadingIndicator = await page.locator('.loading');
    if (await loadingIndicator.count() > 0) {
      // 等待加载完成
      await page.waitForSelector('.loading', { state: 'hidden', timeout: 10000 });
    }
    
    // 验证错误状态处理
    // 这里可以模拟网络错误来测试错误处理
    console.log('数据加载状态处理正常');
  });

  test('实时数据更新机制应该工作', async ({ page }) => {
    // 等待初始数据加载
    await page.waitForSelector('.dashboard-stats', { timeout: 10000 });
    
    // 记录初始数据
    const initialData = await page.evaluate(() => {
      const todayIncome = document.querySelector('[data-testid="today-income"]')?.textContent;
      const totalRooms = document.querySelector('[data-testid="total-rooms"]')?.textContent;
      return { todayIncome, totalRooms };
    });
    
    console.log('初始数据:', initialData);
    
    // 等待一段时间，看是否有数据更新
    await page.waitForTimeout(5000);
    
    // 检查数据是否保持一致（在没有实际业务变化的情况下）
    const currentData = await page.evaluate(() => {
      const todayIncome = document.querySelector('[data-testid="today-income"]')?.textContent;
      const totalRooms = document.querySelector('[data-testid="total-rooms"]')?.textContent;
      return { todayIncome, totalRooms };
    });
    
    console.log('当前数据:', currentData);
    
    // 验证数据一致性
    if (initialData.todayIncome && currentData.todayIncome) {
      expect(currentData.todayIncome).toBe(initialData.todayIncome);
    }
    if (initialData.totalRooms && currentData.totalRooms) {
      expect(currentData.totalRooms).toBe(initialData.totalRooms);
    }
  });
});
