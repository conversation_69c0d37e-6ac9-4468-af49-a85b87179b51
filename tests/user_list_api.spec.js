const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:8080';
const API_BASE = `${BASE_URL}/api/v1/admin`;

test.describe('用户列表管理API测试', () => {
  
  test('获取用户列表 - 基础功能', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    expect(data.message).toBe('success');
    expect(data.data).toHaveProperty('data');
    expect(data.data).toHaveProperty('total');
    expect(data.data).toHaveProperty('page');
    expect(data.data).toHaveProperty('page_size');
    expect(data.data).toHaveProperty('total_pages');
    
    // 验证用户数据结构
    const users = data.data.data;
    expect(Array.isArray(users)).toBe(true);
    expect(users.length).toBeGreaterThan(0);
    
    if (users.length > 0) {
      const user = users[0];
      expect(user).toHaveProperty('id');
      expect(user).toHaveProperty('openid');
      expect(user).toHaveProperty('nickname');
      expect(user).toHaveProperty('avatar_url');
      expect(user).toHaveProperty('phone');
      expect(user).toHaveProperty('balance');
      expect(user).toHaveProperty('order_count');
      expect(user).toHaveProperty('total_consumption');
      expect(user).toHaveProperty('last_login_at');
      expect(user).toHaveProperty('created_at');
      
      // 验证数据类型
      expect(typeof user.id).toBe('number');
      expect(typeof user.openid).toBe('string');
      expect(typeof user.nickname).toBe('string');
      expect(typeof user.avatar_url).toBe('string');
      expect(typeof user.phone).toBe('string');
      expect(typeof user.balance).toBe('number');
      expect(typeof user.order_count).toBe('number');
      expect(typeof user.total_consumption).toBe('number');
      expect(typeof user.created_at).toBe('string');
    }
  });

  test('用户数量统计准确性测试', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const users = data.data.data;
    const total = data.data.total;
    
    // 验证返回的用户数量与total字段一致
    if (data.data.page === 1 && users.length < data.data.page_size) {
      // 如果是第一页且用户数量小于页面大小，则应该等于总数
      expect(users.length).toBe(total);
    }
    
    // 验证total是正整数
    expect(total).toBeGreaterThanOrEqual(0);
    expect(Number.isInteger(total)).toBe(true);
  });

  test('用户信息字段完整性测试', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const users = data.data.data;
    
    // 验证每个用户的字段完整性
    users.forEach((user, index) => {
      // 必需字段验证
      expect(user.id, `用户${index + 1}缺少id字段`).toBeDefined();
      expect(user.openid, `用户${index + 1}缺少openid字段`).toBeDefined();
      expect(user.created_at, `用户${index + 1}缺少created_at字段`).toBeDefined();
      
      // 可选字段验证（应该存在但可能为空）
      expect(user.hasOwnProperty('nickname'), `用户${index + 1}缺少nickname字段`).toBe(true);
      expect(user.hasOwnProperty('avatar_url'), `用户${index + 1}缺少avatar_url字段`).toBe(true);
      expect(user.hasOwnProperty('phone'), `用户${index + 1}缺少phone字段`).toBe(true);
      expect(user.hasOwnProperty('balance'), `用户${index + 1}缺少balance字段`).toBe(true);
      
      // 数据格式验证
      expect(user.id).toBeGreaterThan(0);
      expect(user.openid.length).toBeGreaterThan(0);
      expect(user.balance).toBeGreaterThanOrEqual(0);
      
      // 时间格式验证
      expect(new Date(user.created_at).toString()).not.toBe('Invalid Date');
    });
  });

  test('分页功能测试', async ({ request }) => {
    // 测试第一页
    const page1Response = await request.get(`${API_BASE}/users?page=1&page_size=2`);
    expect(page1Response.status()).toBe(200);
    
    const page1Data = await page1Response.json();
    expect(page1Data.data.page).toBe(1);
    expect(page1Data.data.page_size).toBe(2);
    expect(page1Data.data.data.length).toBeLessThanOrEqual(2);
    
    // 如果有多于2个用户，测试第二页
    if (page1Data.data.total > 2) {
      const page2Response = await request.get(`${API_BASE}/users?page=2&page_size=2`);
      expect(page2Response.status()).toBe(200);
      
      const page2Data = await page2Response.json();
      expect(page2Data.data.page).toBe(2);
      expect(page2Data.data.page_size).toBe(2);
      
      // 验证第二页的用户与第一页不同
      const page1UserIds = page1Data.data.data.map(user => user.id);
      const page2UserIds = page2Data.data.data.map(user => user.id);
      
      page2UserIds.forEach(id => {
        expect(page1UserIds).not.toContain(id);
      });
    }
  });

  test('搜索功能测试 - 按昵称搜索', async ({ request }) => {
    // 先获取所有用户，找一个有昵称的用户
    const allUsersResponse = await request.get(`${API_BASE}/users`);
    const allUsersData = await allUsersResponse.json();
    const usersWithNickname = allUsersData.data.data.filter(user => user.nickname && user.nickname.length > 0);
    
    if (usersWithNickname.length > 0) {
      const testUser = usersWithNickname[0];
      const searchTerm = testUser.nickname.substring(0, 2); // 取昵称的前两个字符
      
      const searchResponse = await request.get(`${API_BASE}/users?nickname=${encodeURIComponent(searchTerm)}`);
      expect(searchResponse.status()).toBe(200);
      
      const searchData = await searchResponse.json();
      const searchResults = searchData.data.data;
      
      // 验证搜索结果包含搜索词
      searchResults.forEach(user => {
        expect(user.nickname).toContain(searchTerm);
      });
      
      // 验证搜索结果包含原用户
      const foundUser = searchResults.find(user => user.id === testUser.id);
      expect(foundUser).toBeDefined();
    }
  });

  test('搜索功能测试 - 按手机号搜索', async ({ request }) => {
    // 先获取所有用户，找一个有手机号的用户
    const allUsersResponse = await request.get(`${API_BASE}/users`);
    const allUsersData = await allUsersResponse.json();
    const usersWithPhone = allUsersData.data.data.filter(user => user.phone && user.phone.length > 0);
    
    if (usersWithPhone.length > 0) {
      const testUser = usersWithPhone[0];
      const searchTerm = testUser.phone.substring(0, 4); // 取手机号的前4位
      
      const searchResponse = await request.get(`${API_BASE}/users?phone=${encodeURIComponent(searchTerm)}`);
      expect(searchResponse.status()).toBe(200);
      
      const searchData = await searchResponse.json();
      const searchResults = searchData.data.data;
      
      // 验证搜索结果包含搜索词
      searchResults.forEach(user => {
        expect(user.phone).toContain(searchTerm);
      });
      
      // 验证搜索结果包含原用户
      const foundUser = searchResults.find(user => user.id === testUser.id);
      expect(foundUser).toBeDefined();
    }
  });

  test('日期筛选功能测试', async ({ request }) => {
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    // 测试按开始日期筛选
    const startDateResponse = await request.get(`${API_BASE}/users?start_date=${yesterday}`);
    expect(startDateResponse.status()).toBe(200);
    
    const startDateData = await startDateResponse.json();
    startDateData.data.data.forEach(user => {
      const userDate = new Date(user.created_at).toISOString().split('T')[0];
      expect(userDate >= yesterday).toBe(true);
    });
    
    // 测试按结束日期筛选
    const endDateResponse = await request.get(`${API_BASE}/users?end_date=${today}`);
    expect(endDateResponse.status()).toBe(200);
    
    const endDateData = await endDateResponse.json();
    endDateData.data.data.forEach(user => {
      const userDate = new Date(user.created_at).toISOString().split('T')[0];
      expect(userDate <= today).toBe(true);
    });
  });

  test('用户统计API测试', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users/stats`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.code).toBe(200);
    
    // 验证统计数据结构（如果API存在）
    if (data.data) {
      expect(typeof data.data).toBe('object');
    }
  });

  test('错误处理 - 无效页码', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users?page=0`);
    
    // 应该返回错误或默认到第一页
    expect([200, 400]).toContain(response.status());
    
    if (response.status() === 200) {
      const data = await response.json();
      expect(data.data.page).toBeGreaterThan(0);
    }
  });

  test('错误处理 - 无效页面大小', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users?page_size=0`);
    
    // 应该返回错误或使用默认页面大小
    expect([200, 400]).toContain(response.status());
    
    if (response.status() === 200) {
      const data = await response.json();
      expect(data.data.page_size).toBeGreaterThan(0);
    }
  });

  test('数据排序验证', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const users = data.data.data;
    
    if (users.length > 1) {
      // 验证用户按创建时间降序排列
      for (let i = 0; i < users.length - 1; i++) {
        const currentDate = new Date(users[i].created_at);
        const nextDate = new Date(users[i + 1].created_at);
        expect(currentDate >= nextDate).toBe(true);
      }
    }
  });

  test('用户余额数据验证', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users`);
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    const users = data.data.data;
    
    users.forEach((user, index) => {
      // 验证余额是数字且非负
      expect(typeof user.balance, `用户${index + 1}余额类型错误`).toBe('number');
      expect(user.balance, `用户${index + 1}余额为负数`).toBeGreaterThanOrEqual(0);
      
      // 验证余额精度（最多两位小数）
      const balanceStr = user.balance.toString();
      const decimalIndex = balanceStr.indexOf('.');
      if (decimalIndex !== -1) {
        const decimalPlaces = balanceStr.length - decimalIndex - 1;
        expect(decimalPlaces, `用户${index + 1}余额精度超过2位小数`).toBeLessThanOrEqual(2);
      }
    });
  });

  test('API响应时间测试', async ({ request }) => {
    const startTime = Date.now();
    const response = await request.get(`${API_BASE}/users`);
    const endTime = Date.now();
    
    expect(response.status()).toBe(200);
    
    const responseTime = endTime - startTime;
    expect(responseTime, 'API响应时间过长').toBeLessThan(2000); // 2秒内响应
  });

  test('并发请求测试', async ({ request }) => {
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(request.get(`${API_BASE}/users`));
    }

    const responses = await Promise.all(promises);

    responses.forEach((response, index) => {
      expect(response.status(), `并发请求${index + 1}失败`).toBe(200);
    });

    // 验证所有响应返回相同的数据
    const firstResponseData = await responses[0].json();
    for (let i = 1; i < responses.length; i++) {
      const responseData = await responses[i].json();
      expect(responseData.data.total).toBe(firstResponseData.data.total);
    }
  });

  test('用户订单统计数据验证', async ({ request }) => {
    const response = await request.get(`${API_BASE}/users`);

    expect(response.status()).toBe(200);

    const data = await response.json();
    const users = data.data.data;

    // 验证每个用户的订单统计数据
    users.forEach((user, index) => {
      // 验证订单数量是非负整数
      expect(user.order_count, `用户${index + 1}订单数量类型错误`).toBeGreaterThanOrEqual(0);
      expect(Number.isInteger(user.order_count), `用户${index + 1}订单数量不是整数`).toBe(true);

      // 验证总消费是非负数字
      expect(user.total_consumption, `用户${index + 1}总消费类型错误`).toBeGreaterThanOrEqual(0);
      expect(typeof user.total_consumption, `用户${index + 1}总消费不是数字`).toBe('number');

      // 验证最后登录时间字段存在（可以为null）
      expect(user.hasOwnProperty('last_login_at'), `用户${index + 1}缺少last_login_at字段`).toBe(true);
    });

    // 验证具体的订单统计数据（基于已知的测试数据）
    const expectedData = [
      { id: 1, order_count: 1, total_consumption: 40 },
      { id: 2, order_count: 1, total_consumption: 60 },
      { id: 3, order_count: 1, total_consumption: 20 }
    ];

    expectedData.forEach(expected => {
      const user = users.find(u => u.id === expected.id);
      if (user) {
        expect(user.order_count, `用户${expected.id}订单数量不匹配`).toBe(expected.order_count);
        expect(user.total_consumption, `用户${expected.id}总消费不匹配`).toBe(expected.total_consumption);
      }
    });
  });
});
