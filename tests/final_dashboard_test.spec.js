const { test, expect } = require('@playwright/test');

test('最终Dashboard数据验证', async ({ page }) => {
  console.log('=== 最终Dashboard数据验证测试 ===');
  
  // 直接测试API
  console.log('1. 测试Dashboard API...');
  const dashboardResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const dashboardData = await dashboardResponse.json();
  
  console.log('Dashboard API响应状态:', dashboardResponse.status());
  console.log('Dashboard API数据:', JSON.stringify(dashboardData, null, 2));
  
  expect(dashboardResponse.status()).toBe(200);
  expect(dashboardData.code).toBe(200);
  expect(dashboardData.data).toBeDefined();
  
  // 验证新的字段
  const data = dashboardData.data;
  console.log('✅ 今日收入:', data.todayIncome);
  console.log('✅ 活跃订单:', data.activeOrders);
  console.log('✅ 设备总数:', data.totalDevices);
  console.log('✅ 在线设备:', data.onlineDevices);
  console.log('✅ 离线设备:', data.offlineDevices);
  
  // 验证数据类型
  expect(typeof data.todayIncome).toBe('number');
  expect(typeof data.activeOrders).toBe('number');
  expect(typeof data.totalDevices).toBe('number');
  expect(typeof data.onlineDevices).toBe('number');
  expect(typeof data.offlineDevices).toBe('number');
  
  console.log('2. 测试房间统计API...');
  const roomStatsResponse = await page.request.get('http://localhost:8080/api/v1/admin/rooms/statistics');
  const roomStatsData = await roomStatsResponse.json();
  
  console.log('房间统计API响应状态:', roomStatsResponse.status());
  console.log('房间统计API数据:', JSON.stringify(roomStatsData, null, 2));
  
  expect(roomStatsResponse.status()).toBe(200);
  expect(roomStatsData.code).toBe(200);
  expect(roomStatsData.data).toBeDefined();
  
  console.log('3. 访问前端页面...');
  await page.goto('http://localhost:3001');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  // 检查前端是否显示新的字段
  try {
    const activeOrdersElement = await page.locator('[data-testid="active-orders"]');
    if (await activeOrdersElement.count() > 0) {
      const activeOrdersText = await activeOrdersElement.textContent();
      console.log('✅ 前端显示活跃订单:', activeOrdersText);
    }
    
    const totalDevicesElement = await page.locator('[data-testid="total-devices"]');
    if (await totalDevicesElement.count() > 0) {
      const totalDevicesText = await totalDevicesElement.textContent();
      console.log('✅ 前端显示设备总数:', totalDevicesText);
    }
  } catch (error) {
    console.log('前端元素检查出错:', error.message);
  }
  
  console.log('=== 验证完成 ===');
  console.log('🎉 所有API都正常工作，数据格式正确！');
});
