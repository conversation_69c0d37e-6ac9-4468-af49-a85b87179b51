// 中优先级问题优化验证测试
const { test, expect } = require('@playwright/test');

const API_BASE = 'http://localhost:8080/api/v1';

test.describe('🔧 中优先级问题优化验证', () => {

  test.beforeAll(async () => {
    console.log('🚀 开始中优先级问题优化验证');
    console.log('🎯 验证目标: 性能优化、缓存机制、错误处理、API格式统一');
  });

  // ==================== 优化1: 用户管理页面性能优化验证 ====================
  test('✅ 优化1: 用户管理页面性能优化验证', async ({ request }) => {
    console.log('🔧 验证用户管理页面性能优化');
    
    const startTime = Date.now();
    
    // 测试用户列表查询性能
    const userListResponse = await request.get(`${API_BASE}/admin/users?page=1&page_size=20`);
    expect(userListResponse.status()).toBe(200);
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log(`📊 用户列表查询响应时间: ${responseTime}ms`);
    
    // 验证响应时间在合理范围内（应该小于2秒）
    expect(responseTime).toBeLessThan(2000);
    
    const userData = await userListResponse.json();
    expect(userData.code).toBe(200);
    expect(userData.data).toHaveProperty('data');
    expect(userData.data).toHaveProperty('total');
    
    console.log('✅ 优化1验证成功: 用户列表查询性能良好');
  });

  // ==================== 优化2: MQTT状态API格式统一验证 ====================
  test('✅ 优化2: MQTT状态API格式统一验证', async ({ request }) => {
    console.log('🔧 验证MQTT状态API格式统一');
    
    // 测试MQTT连接状态API
    const mqttStatusResponse = await request.get(`${API_BASE}/mqtt/connection-status`);
    expect(mqttStatusResponse.status()).toBe(200);
    
    const mqttData = await mqttStatusResponse.json();
    expect(mqttData.code).toBe(200);
    
    // 验证统一的响应格式
    expect(mqttData.data).toHaveProperty('status');
    expect(mqttData.data).toHaveProperty('mqtt_connected');
    expect(mqttData.data).toHaveProperty('broker');
    expect(mqttData.data).toHaveProperty('client_id');
    expect(mqttData.data).toHaveProperty('topics');
    expect(mqttData.data).toHaveProperty('connection_info');
    
    console.log('✅ 优化2验证成功: MQTT API格式已统一');
  });

  // ==================== 优化3: 数据库查询性能优化验证 ====================
  test('✅ 优化3: 数据库查询性能优化验证', async ({ request }) => {
    console.log('🔧 验证数据库查询性能优化');
    
    // 测试多个查询的性能
    const queries = [
      { name: '用户列表', url: `${API_BASE}/admin/users?page=1&page_size=50` },
      { name: '用户统计', url: `${API_BASE}/admin/users/stats` },
      { name: '房间列表', url: `${API_BASE}/admin/rooms?page=1&page_size=20` }
    ];
    
    const results = [];
    
    for (const query of queries) {
      const startTime = Date.now();
      const response = await request.get(query.url);
      const endTime = Date.now();
      
      expect(response.status()).toBe(200);
      
      const responseTime = endTime - startTime;
      results.push({ name: query.name, time: responseTime });
      
      console.log(`📊 ${query.name}查询时间: ${responseTime}ms`);
      
      // 验证查询时间在合理范围内
      expect(responseTime).toBeLessThan(1500);
    }
    
    // 验证平均响应时间
    const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
    console.log(`📊 平均查询时间: ${avgTime.toFixed(2)}ms`);
    expect(avgTime).toBeLessThan(1000);
    
    console.log('✅ 优化3验证成功: 数据库查询性能良好');
  });

  // ==================== 优化4: API响应缓存验证 ====================
  test('✅ 优化4: API响应缓存验证', async ({ request }) => {
    console.log('🔧 验证API响应缓存机制');
    
    const testUrl = `${API_BASE}/admin/users/stats`;
    
    // 第一次请求（应该从数据库查询）
    const startTime1 = Date.now();
    const response1 = await request.get(testUrl);
    const endTime1 = Date.now();
    const time1 = endTime1 - startTime1;
    
    expect(response1.status()).toBe(200);
    const data1 = await response1.json();
    
    // 第二次请求（应该从缓存获取）
    const startTime2 = Date.now();
    const response2 = await request.get(testUrl);
    const endTime2 = Date.now();
    const time2 = endTime2 - startTime2;
    
    expect(response2.status()).toBe(200);
    const data2 = await response2.json();
    
    // 验证数据一致性
    expect(data1.data.total_users).toBe(data2.data.total_users);
    
    // 验证缓存效果（第二次请求应该更快）
    console.log(`📊 第一次请求时间: ${time1}ms`);
    console.log(`📊 第二次请求时间: ${time2}ms`);
    console.log(`📊 性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`);
    
    // 缓存命中应该显著提升性能
    expect(time2).toBeLessThan(time1);
    
    console.log('✅ 优化4验证成功: API缓存机制工作正常');
  });

  // ==================== 优化5: 错误处理增强验证 ====================
  test('✅ 优化5: 错误处理增强验证', async ({ request }) => {
    console.log('🔧 验证错误处理增强');
    
    // 测试各种错误场景
    const errorTests = [
      {
        name: '无效的分页参数',
        url: `${API_BASE}/admin/users?page=-1&page_size=0`,
        expectedStatus: 400
      },
      {
        name: '不存在的资源',
        url: `${API_BASE}/admin/users/99999`,
        expectedStatus: 404
      },
      {
        name: '无效的查询参数',
        url: `${API_BASE}/admin/users?invalid_param=test`,
        expectedStatus: 200 // 应该忽略无效参数
      }
    ];
    
    for (const test of errorTests) {
      console.log(`🧪 测试: ${test.name}`);
      
      const response = await request.get(test.url);
      expect(response.status()).toBe(test.expectedStatus);
      
      if (response.status() !== 200) {
        const errorData = await response.json();
        expect(errorData).toHaveProperty('message');
        expect(errorData.message).toBeTruthy();
      }
    }
    
    console.log('✅ 优化5验证成功: 错误处理机制完善');
  });

  // ==================== 性能基准测试 ====================
  test('✅ 性能基准测试', async ({ request }) => {
    console.log('🔧 执行性能基准测试');
    
    const concurrentRequests = 5;
    const testUrl = `${API_BASE}/admin/users?page=1&page_size=10`;
    
    // 并发请求测试
    const startTime = Date.now();
    const promises = Array(concurrentRequests).fill().map(() => request.get(testUrl));
    const responses = await Promise.all(promises);
    const endTime = Date.now();
    
    // 验证所有请求都成功
    responses.forEach(response => {
      expect(response.status()).toBe(200);
    });
    
    const totalTime = endTime - startTime;
    const avgTime = totalTime / concurrentRequests;
    
    console.log(`📊 并发请求数: ${concurrentRequests}`);
    console.log(`📊 总耗时: ${totalTime}ms`);
    console.log(`📊 平均响应时间: ${avgTime.toFixed(2)}ms`);
    
    // 验证并发性能
    expect(avgTime).toBeLessThan(500);
    expect(totalTime).toBeLessThan(2000);
    
    console.log('✅ 性能基准测试通过');
  });

  // ==================== 缓存一致性测试 ====================
  test('✅ 缓存一致性测试', async ({ request }) => {
    console.log('🔧 验证缓存一致性');
    
    // 创建测试用户
    const testUser = {
      openid: 'cache_test_' + Date.now(),
      nickname: '缓存测试用户'
    };
    
    const createResponse = await request.post(`${API_BASE}/users/register`, {
      data: testUser
    });
    expect(createResponse.status()).toBe(200);
    
    // 获取用户列表（应该包含新用户）
    const listResponse = await request.get(`${API_BASE}/admin/users?page=1&page_size=10`);
    expect(listResponse.status()).toBe(200);
    
    const listData = await listResponse.json();
    const users = listData.data.data;
    
    // 验证新用户在列表中
    const newUser = users.find(u => u.nickname === testUser.nickname);
    expect(newUser).toBeTruthy();
    
    console.log('✅ 缓存一致性验证成功');
  });

  // ==================== 索引效果验证 ====================
  test('✅ 索引效果验证', async ({ request }) => {
    console.log('🔧 验证数据库索引效果');
    
    // 测试各种查询场景
    const searchTests = [
      {
        name: '昵称搜索',
        url: `${API_BASE}/admin/users?nickname=测试`,
        description: '测试昵称索引效果'
      },
      {
        name: '时间范围查询',
        url: `${API_BASE}/admin/users?start_date=2025-01-01&end_date=2025-12-31`,
        description: '测试时间索引效果'
      },
      {
        name: '分页查询',
        url: `${API_BASE}/admin/users?page=2&page_size=20`,
        description: '测试分页索引效果'
      }
    ];
    
    for (const test of searchTests) {
      console.log(`🔍 ${test.description}`);
      
      const startTime = Date.now();
      const response = await request.get(test.url);
      const endTime = Date.now();
      
      expect(response.status()).toBe(200);
      
      const responseTime = endTime - startTime;
      console.log(`📊 ${test.name}响应时间: ${responseTime}ms`);
      
      // 验证查询性能
      expect(responseTime).toBeLessThan(800);
    }
    
    console.log('✅ 索引效果验证成功');
  });

  test.afterAll(async () => {
    console.log('🏁 中优先级问题优化验证完成');
    console.log('📋 优化验证总结:');
    console.log('  ✅ 优化1: 用户管理页面性能 - 已优化');
    console.log('  ✅ 优化2: MQTT状态API格式 - 已统一');
    console.log('  ✅ 优化3: 数据库查询性能 - 已优化');
    console.log('  ✅ 优化4: API响应缓存 - 已实现');
    console.log('  ✅ 优化5: 错误处理增强 - 已完善');
    console.log('  ✅ 性能基准测试 - 通过');
    console.log('  ✅ 缓存一致性 - 正常');
    console.log('  ✅ 索引效果 - 良好');
    console.log('🎉 所有中优先级问题已成功优化！');
  });

});
