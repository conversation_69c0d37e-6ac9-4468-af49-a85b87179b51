const { test, expect } = require('@playwright/test');

test('最终Dashboard数据验证 - 确保数据准确性', async ({ page }) => {
  console.log('=== 最终Dashboard数据验证测试 ===');
  
  // 1. 测试Dashboard API数据准确性
  console.log('1. 验证Dashboard API数据...');
  const dashboardResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const dashboardData = await dashboardResponse.json();
  
  expect(dashboardResponse.status()).toBe(200);
  expect(dashboardData.code).toBe(200);
  
  const data = dashboardData.data;
  console.log('✅ Dashboard API数据:', JSON.stringify(data, null, 2));
  
  // 2. 验证设备数据准确性
  console.log('2. 验证设备数据准确性...');
  const devicesResponse = await page.request.get('http://localhost:8080/api/v1/admin/devices?page_size=100');
  const devicesData = await devicesResponse.json();
  
  const actualDeviceTotal = devicesData.data.total;
  const actualOnlineDevices = devicesData.data.data.filter(device => device.is_online).length;
  const actualOfflineDevices = devicesData.data.data.filter(device => !device.is_online).length;
  
  console.log(`实际设备总数: ${actualDeviceTotal}`);
  console.log(`实际在线设备: ${actualOnlineDevices}`);
  console.log(`实际离线设备: ${actualOfflineDevices}`);
  
  // 验证Dashboard返回的设备数据与实际一致
  expect(data.onlineDevices).toBe(actualOnlineDevices);
  expect(data.offlineDevices).toBe(actualOfflineDevices);
  console.log('✅ 设备数据验证通过');
  
  // 3. 验证活跃订单数据准确性
  console.log('3. 验证活跃订单数据准确性...');
  const ordersResponse = await page.request.get('http://localhost:8080/api/v1/orders?page_size=100');
  const ordersData = await ordersResponse.json();
  
  const activeOrders = ordersData.data.data.filter(order => 
    order.status === 'pending' || order.status === 'paid'
  );
  const actualActiveOrderCount = activeOrders.length;
  
  console.log(`实际活跃订单数: ${actualActiveOrderCount}`);
  console.log('活跃订单详情:', activeOrders.map(order => ({
    id: order.id,
    order_number: order.order_number,
    status: order.status,
    room: order.room.name
  })));
  
  // 验证Dashboard返回的活跃订单数据与实际一致
  expect(data.activeOrders).toBe(actualActiveOrderCount);
  console.log('✅ 活跃订单数据验证通过');
  
  // 4. 验证房间数据准确性
  console.log('4. 验证房间数据准确性...');
  const roomStatsResponse = await page.request.get('http://localhost:8080/api/v1/admin/rooms/statistics');
  const roomStatsData = await roomStatsResponse.json();
  
  console.log('房间统计数据:', roomStatsData.data);
  
  // 验证Dashboard返回的房间数据与实际一致
  expect(data.totalRooms).toBe(roomStatsData.data.total);
  expect(data.availableRooms).toBe(roomStatsData.data.available);
  expect(data.occupiedRooms).toBe(roomStatsData.data.occupied);
  console.log('✅ 房间数据验证通过');
  
  // 5. 验证今日收入数据
  console.log('5. 验证今日收入数据...');
  const todayIncomeResponse = await page.request.get('http://localhost:8080/api/v1/admin/orders/today-income');
  const todayIncomeData = await todayIncomeResponse.json();
  
  console.log(`今日订单收入: ${todayIncomeData.data}`);
  console.log(`Dashboard显示今日收入: ${data.todayIncome}`);
  
  // 验证收入数据类型正确
  expect(typeof data.todayIncome).toBe('number');
  console.log('✅ 今日收入数据验证通过');
  
  // 6. 验证数据稳定性（不跳动）
  console.log('6. 验证数据稳定性...');
  
  // 等待2秒后再次获取数据
  await page.waitForTimeout(2000);
  const dashboardResponse2 = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const dashboardData2 = await dashboardResponse2.json();
  const data2 = dashboardData2.data;
  
  // 验证数据没有跳动
  expect(data2.activeOrders).toBe(data.activeOrders);
  expect(data2.onlineDevices).toBe(data.onlineDevices);
  expect(data2.offlineDevices).toBe(data.offlineDevices);
  expect(data2.todayIncome).toBe(data.todayIncome);
  expect(data2.totalRooms).toBe(data.totalRooms);
  
  console.log('✅ 数据稳定性验证通过 - 数据没有跳动');
  
  // 7. 验证前端页面显示
  console.log('7. 验证前端页面显示...');
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  // 检查前端是否正确显示数据
  try {
    const activeOrdersElement = await page.locator('[data-testid="active-orders"]');
    if (await activeOrdersElement.count() > 0) {
      const activeOrdersText = await activeOrdersElement.textContent();
      console.log(`前端显示活跃订单: ${activeOrdersText}`);
      expect(activeOrdersText).toBe(data.activeOrders.toString());
    }
    
    const onlineDevicesElement = await page.locator('[data-testid="online-devices"]');
    if (await onlineDevicesElement.count() > 0) {
      const onlineDevicesText = await onlineDevicesElement.textContent();
      console.log(`前端显示在线设备: ${onlineDevicesText}`);
      expect(onlineDevicesText).toBe(data.onlineDevices.toString());
    }
    
    const todayIncomeElement = await page.locator('[data-testid="today-income"]');
    if (await todayIncomeElement.count() > 0) {
      const todayIncomeText = await todayIncomeElement.textContent();
      console.log(`前端显示今日收入: ${todayIncomeText}`);
      // 收入显示格式为 ¥0.00，所以需要检查包含关系
      expect(todayIncomeText).toContain(data.todayIncome.toFixed(2));
    }
    
    console.log('✅ 前端页面显示验证通过');
  } catch (error) {
    console.log('前端页面验证出错:', error.message);
  }
  
  // 8. 最终总结
  console.log('=== 验证结果总结 ===');
  console.log(`📊 活跃订单: ${data.activeOrders}个 (正在进行中的订单)`);
  console.log(`📈 今日收入: ¥${data.todayIncome.toFixed(2)} (订单收入 + 平台核销收入)`);
  console.log(`💻 在线设备: ${data.onlineDevices}个 / 总设备: ${actualDeviceTotal}个`);
  console.log(`🏠 房间状态: ${data.availableRooms}个空闲 / ${data.totalRooms}个总房间`);
  console.log('🎉 所有数据验证通过，Dashboard显示准确且稳定！');
});
