const { test, expect } = require('@playwright/test');

// 测试配置
const FRONTEND_URL = 'http://localhost:3000';
const API_BASE = 'http://localhost:8080/api/v1';

test.describe('麻将室管理系统界面全面测试', () => {
  
  // 测试所有主要页面的加载状态
  const pages = [
    { path: '/dashboard', name: '仪表盘', hasData: true },
    { path: '/rooms/list', name: '房间列表', hasData: true },
    { path: '/rooms/create', name: '添加房间', hasData: false },
    { path: '/orders/list', name: '订单列表', hasData: true },
    { path: '/users/list', name: '用户列表', hasData: true },
    { path: '/devices/list', name: '设备管理', hasData: true },
    { path: '/finance/analysis', name: '财务分析', hasData: true },
    { path: '/platform/orders', name: '平台订单', hasData: true },
    { path: '/platform/verification', name: '订单核销', hasData: true },
    { path: '/system/settings', name: '系统配置', hasData: false },
    { path: '/system/pricing', name: '计费规则', hasData: true }
  ];

  test.beforeEach(async ({ page }) => {
    // 设置网络监听
    page.on('response', response => {
      if (response.status() >= 400) {
        console.log(`❌ 网络错误: ${response.url()} - ${response.status()}`);
      }
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ 控制台错误: ${msg.text()}`);
      }
    });
  });

  // 测试每个页面的基本加载
  pages.forEach(pageInfo => {
    test(`页面加载测试 - ${pageInfo.name}`, async ({ page }) => {
      console.log(`🔍 测试页面: ${pageInfo.name} (${pageInfo.path})`);
      
      // 导航到页面
      await page.goto(`${FRONTEND_URL}${pageInfo.path}`);
      
      // 等待页面加载
      await page.waitForLoadState('networkidle');
      
      // 检查页面是否正常加载（没有404或错误页面）
      const pageTitle = await page.title();
      expect(pageTitle).toContain('自助麻将室管理系统');
      
      // 检查是否有Vue应用加载
      const vueApp = await page.locator('#app').count();
      expect(vueApp).toBeGreaterThanOrEqual(1);
      
      // 检查是否有侧边栏
      const sidebar = await page.locator('.sidebar-container').count();
      expect(sidebar).toBe(1);
      
      // 检查是否有主内容区域
      const mainContent = await page.locator('.main-content').count();
      expect(mainContent).toBeGreaterThanOrEqual(0);
      
      console.log(`✅ ${pageInfo.name} 基本加载正常`);
    });
  });

  // 测试数据加载状态
  pages.filter(p => p.hasData).forEach(pageInfo => {
    test(`数据加载测试 - ${pageInfo.name}`, async ({ page }) => {
      console.log(`📊 测试数据加载: ${pageInfo.name}`);
      
      await page.goto(`${FRONTEND_URL}${pageInfo.path}`);
      await page.waitForLoadState('networkidle');
      
      // 等待数据加载完成
      await page.waitForTimeout(2000);
      
      // 检查是否有加载指示器
      const loadingExists = await page.locator('.el-loading-mask').isVisible().catch(() => false);
      
      // 检查是否有数据表格或内容
      const hasTable = await page.locator('.el-table').count() > 0;
      const hasCards = await page.locator('.el-card').count() > 0;
      const hasContent = hasTable || hasCards;
      
      if (!hasContent) {
        // 检查是否有错误信息
        const errorMessage = await page.locator('.el-empty, .error-message').count();
        if (errorMessage > 0) {
          console.log(`⚠️ ${pageInfo.name} 显示空状态或错误信息`);
        } else {
          console.log(`❌ ${pageInfo.name} 没有找到预期的数据内容`);
        }
      } else {
        console.log(`✅ ${pageInfo.name} 数据内容加载正常`);
      }
      
      expect(hasContent).toBe(true);
    });
  });

  // 测试API连接状态
  test('API连接状态测试', async ({ request }) => {
    const apiEndpoints = [
      { url: `${API_BASE}/admin/dashboard`, name: '仪表盘数据' },
      { url: `${API_BASE}/rooms`, name: '房间列表' },
      { url: `${API_BASE}/orders`, name: '订单列表' },
      { url: `${API_BASE}/admin/users`, name: '用户列表' },
      { url: `${API_BASE}/admin/devices`, name: '设备列表' },
      { url: `${API_BASE}/admin/finance/report`, name: '财务报告' },
      { url: `${API_BASE}/admin/platform/orders`, name: '平台订单' },
      { url: `${API_BASE}/admin/pricing-rules`, name: '计费规则' }
    ];

    for (const endpoint of apiEndpoints) {
      try {
        console.log(`🔗 测试API: ${endpoint.name}`);
        const response = await request.get(endpoint.url);
        
        if (response.status() === 200) {
          console.log(`✅ ${endpoint.name} API连接正常`);
        } else {
          console.log(`❌ ${endpoint.name} API返回状态码: ${response.status()}`);
        }
        
        expect([200, 404]).toContain(response.status());
      } catch (error) {
        console.log(`❌ ${endpoint.name} API连接失败: ${error.message}`);
      }
    }
  });

  // 测试导航功能
  test('导航功能测试', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    
    // 测试侧边栏菜单点击
    const menuItems = [
      { selector: '.el-sub-menu:has-text("房间管理")', expectedPath: '/rooms' },
      { selector: '.el-sub-menu:has-text("订单管理")', expectedPath: '/orders' },
      { selector: '.el-sub-menu:has-text("用户管理")', expectedPath: '/users' },
      { selector: '.el-menu-item:has-text("设备管理")', expectedPath: '/devices' }
    ];

    for (const item of menuItems) {
      try {
        await page.click(item.selector);
        await page.waitForTimeout(1000);
        
        const currentUrl = page.url();
        expect(currentUrl).toContain(item.expectedPath);
        console.log(`✅ 导航到 ${item.expectedPath} 成功`);
      } catch (error) {
        console.log(`❌ 导航到 ${item.expectedPath} 失败: ${error.message}`);
      }
    }
  });

  // 测试响应式设计
  test('响应式设计测试', async ({ page }) => {
    const viewports = [
      { width: 1920, height: 1080, name: '桌面大屏' },
      { width: 1366, height: 768, name: '桌面标准' },
      { width: 768, height: 1024, name: '平板' },
      { width: 375, height: 667, name: '手机' }
    ];

    for (const viewport of viewports) {
      console.log(`📱 测试 ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto(`${FRONTEND_URL}/dashboard`);
      await page.waitForLoadState('networkidle');
      
      // 检查侧边栏是否适应
      const sidebar = await page.locator('.sidebar-container').isVisible();
      expect(sidebar).toBe(true);
      
      // 检查主内容区域是否可见
      const mainContent = await page.locator('.layout-main').isVisible();
      expect(mainContent).toBe(true);
      
      console.log(`✅ ${viewport.name} 响应式布局正常`);
    }
  });

  // 测试错误处理
  test('错误处理测试', async ({ page }) => {
    // 测试不存在的路由
    await page.goto(`${FRONTEND_URL}/nonexistent-page`);
    await page.waitForLoadState('networkidle');
    
    // 应该重定向到仪表盘或显示404页面
    const currentUrl = page.url();
    const isRedirected = currentUrl.includes('/dashboard') || currentUrl.includes('/404');
    
    if (!isRedirected) {
      console.log(`⚠️ 不存在的路由没有正确处理: ${currentUrl}`);
    } else {
      console.log(`✅ 不存在的路由处理正常`);
    }
  });

  // 测试数据刷新功能
  test('数据刷新功能测试', async ({ page }) => {
    await page.goto(`${FRONTEND_URL}/users/list`);
    await page.waitForLoadState('networkidle');
    
    // 记录初始数据
    const initialRowCount = await page.locator('.el-table__body tr').count();
    
    // 刷新页面
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // 检查数据是否重新加载
    const refreshedRowCount = await page.locator('.el-table__body tr').count();
    expect(refreshedRowCount).toBe(initialRowCount);
    
    console.log(`✅ 数据刷新功能正常，数据行数: ${refreshedRowCount}`);
  });

  // 测试搜索和筛选功能
  test('搜索筛选功能测试', async ({ page }) => {
    const pagesWithSearch = [
      '/users/list',
      '/orders/list',
      '/rooms/list'
    ];

    for (const pagePath of pagesWithSearch) {
      await page.goto(`${FRONTEND_URL}${pagePath}`);
      await page.waitForLoadState('networkidle');
      
      // 检查是否有搜索框
      const searchInputs = await page.locator('input[placeholder*="搜索"], input[placeholder*="请输入"]').count();
      
      if (searchInputs > 0) {
        console.log(`✅ ${pagePath} 有搜索功能`);
      } else {
        console.log(`⚠️ ${pagePath} 没有找到搜索功能`);
      }
    }
  });
});
