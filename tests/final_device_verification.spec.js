const { test, expect } = require('@playwright/test');

test('最终设备数据验证 - 确保设备总数正确', async ({ page }) => {
  console.log('=== 最终设备数据验证测试 ===');
  
  // 1. 验证设备列表API
  console.log('1. 验证设备列表API...');
  const devicesResponse = await page.request.get('http://localhost:8080/api/v1/admin/devices?page_size=100');
  const devicesData = await devicesResponse.json();
  
  expect(devicesResponse.status()).toBe(200);
  expect(devicesData.code).toBe(200);
  
  const deviceList = devicesData.data.data;
  const totalDevices = devicesData.data.total;
  
  console.log(`✅ 设备列表API返回设备总数: ${totalDevices}`);
  console.log(`✅ 实际设备列表长度: ${deviceList.length}`);
  
  // 验证设备总数
  expect(totalDevices).toBe(19);
  expect(deviceList.length).toBe(19);
  
  // 2. 验证设备类型分布
  console.log('2. 验证设备类型分布...');
  const deviceTypes = {};
  deviceList.forEach(device => {
    deviceTypes[device.type] = (deviceTypes[device.type] || 0) + 1;
  });
  
  console.log('设备类型分布:', deviceTypes);
  
  // 验证设备类型数量
  expect(deviceTypes['main_lock']).toBe(1); // 1个主门禁锁
  expect(deviceTypes['room_lock']).toBe(6); // 6个房间门锁
  expect(deviceTypes['power']).toBe(6);     // 6个电源控制
  expect(deviceTypes['speaker']).toBe(6);   // 6个音响设备
  
  // 3. 验证房间设备分布
  console.log('3. 验证房间设备分布...');
  const roomDevices = {};
  deviceList.forEach(device => {
    if (device.room_id) {
      if (!roomDevices[device.room_id]) {
        roomDevices[device.room_id] = [];
      }
      roomDevices[device.room_id].push(device.type);
    }
  });
  
  console.log('房间设备分布:', roomDevices);
  
  // 验证每个房间都有3个设备
  for (let roomId = 1; roomId <= 6; roomId++) {
    expect(roomDevices[roomId]).toBeDefined();
    expect(roomDevices[roomId].length).toBe(3);
    expect(roomDevices[roomId]).toContain('room_lock');
    expect(roomDevices[roomId]).toContain('power');
    expect(roomDevices[roomId]).toContain('speaker');
  }
  
  // 4. 验证Dashboard API设备统计
  console.log('4. 验证Dashboard API设备统计...');
  const dashboardResponse = await page.request.get('http://localhost:8080/api/v1/admin/dashboard');
  const dashboardData = await dashboardResponse.json();
  
  expect(dashboardResponse.status()).toBe(200);
  expect(dashboardData.code).toBe(200);
  
  const data = dashboardData.data;
  console.log(`Dashboard显示在线设备: ${data.onlineDevices}`);
  console.log(`Dashboard显示离线设备: ${data.offlineDevices}`);
  
  // 验证Dashboard设备统计
  expect(data.onlineDevices).toBe(0);
  expect(data.offlineDevices).toBe(19);
  
  // 验证总数一致性
  const totalFromDashboard = data.onlineDevices + data.offlineDevices;
  expect(totalFromDashboard).toBe(totalDevices);
  
  // 5. 验证设备在线状态
  console.log('5. 验证设备在线状态...');
  const onlineDevices = deviceList.filter(device => device.is_online);
  const offlineDevices = deviceList.filter(device => !device.is_online);
  
  console.log(`实际在线设备数: ${onlineDevices.length}`);
  console.log(`实际离线设备数: ${offlineDevices.length}`);
  
  expect(onlineDevices.length).toBe(data.onlineDevices);
  expect(offlineDevices.length).toBe(data.offlineDevices);
  
  // 6. 验证MAC地址格式
  console.log('6. 验证MAC地址格式...');
  const validMacPattern = /^AA:BB:CC:DD:EE:[0-9A-F]{2}$/;
  let validMacCount = 0;
  
  deviceList.forEach(device => {
    if (validMacPattern.test(device.mac_address)) {
      validMacCount++;
    }
  });
  
  console.log(`有效MAC地址数量: ${validMacCount}`);
  expect(validMacCount).toBe(19); // 所有设备都应该有有效的MAC地址
  
  // 7. 验证前端页面显示
  console.log('7. 验证前端页面显示...');
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  try {
    const onlineDevicesElement = await page.locator('[data-testid="online-devices"]');
    if (await onlineDevicesElement.count() > 0) {
      const onlineDevicesText = await onlineDevicesElement.textContent();
      console.log(`前端显示在线设备: ${onlineDevicesText}`);
      expect(onlineDevicesText).toBe(data.onlineDevices.toString());
    }
    
    console.log('✅ 前端页面显示验证通过');
  } catch (error) {
    console.log('前端页面验证出错:', error.message);
  }
  
  // 8. 最终总结
  console.log('=== 验证结果总结 ===');
  console.log(`📊 设备总数: ${totalDevices}个 (正确！)`);
  console.log(`🏠 主门禁锁: ${deviceTypes['main_lock']}个`);
  console.log(`🚪 房间门锁: ${deviceTypes['room_lock']}个 (6个房间)`);
  console.log(`⚡ 电源控制: ${deviceTypes['power']}个 (6个房间)`);
  console.log(`🔊 音响设备: ${deviceTypes['speaker']}个 (6个房间)`);
  console.log(`💻 在线设备: ${data.onlineDevices}个`);
  console.log(`🔴 离线设备: ${data.offlineDevices}个`);
  console.log('🎉 设备数据已完全修正，所有验证通过！');
});
