import { Component, PropsWithChildren } from 'react'
import { useLaunch } from '@tarojs/taro'
import { authService } from '@/services/auth'
import { storage } from '@/utils/storage'
import './app.scss'

function App({ children }: PropsWithChildren<any>) {
  useLaunch(() => {
    console.log('App launched.')
    
    // 应用启动时的初始化逻辑
    initializeApp()
  })

  /**
   * 应用初始化
   */
  const initializeApp = async () => {
    try {
      // 尝试自动登录
      await authService.autoLogin()
      
      // 初始化其他服务
      // ...
      
      console.log('应用初始化完成')
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }

  // children 是将要会渲染的页面
  return children
}

export default App
