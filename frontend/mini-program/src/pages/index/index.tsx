import React, { useState } from 'react'
import { View, Text, <PERSON><PERSON>, ScrollView } from '@tarojs/components'
import Taro, { useLoad, usePullDownRefresh } from '@tarojs/taro'
import RoomCard from '@/components/RoomCard'
import OrderItem from '@/components/OrderItem'
import { authService } from '@/services/auth'
import { roomService } from '@/services/room'
import { storage } from '@/utils/storage'
import { showPlatformToast, navigateTo } from '@/utils/platform'
import { formatAmount } from '@/utils'
import type { Room, Order, User } from '@/types'
import './index.scss'

const IndexPage: React.FC = () => {
  const [user, setUser] = useState<User | null>(null)
  const [rooms, setRooms] = useState<Room[]>([])
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  useLoad(() => {
    console.log('Index page loaded')
    initializePage()
  })

  usePullDownRefresh(() => {
    handleRefresh()
  })

  const initializePage = async () => {
    try {
      setLoading(true)
      
      // 检查登录状态
      const currentUser = authService.getCurrentUser()
      setUser(currentUser)
      
      // 如果未登录，跳转到登录页
      if (!currentUser) {
        Taro.redirectTo({
          url: '/pages/login/index'
        })
        return
      }
      
      // 加载页面数据
      await Promise.all([
        loadRooms(),
        loadCurrentOrder()
      ])
    } catch (error) {
      console.error('页面初始化失败:', error)
      showPlatformToast('加载失败，请重试', 'error')
    } finally {
      setLoading(false)
    }
  }

  const loadRooms = async () => {
    try {
      const response = await roomService.getRoomList({ useCache: true })
      setRooms(response.data)
    } catch (error) {
      console.error('加载房间列表失败:', error)
    }
  }

  const loadCurrentOrder = async () => {
    try {
      const order = storage.getCurrentOrder()
      setCurrentOrder(order)
    } catch (error) {
      console.error('加载当前订单失败:', error)
    }
  }

  const handleRefresh = async () => {
    try {
      setRefreshing(true)
      
      // 刷新用户信息
      if (user) {
        const updatedUser = await authService.refreshUserInfo()
        setUser(updatedUser)
      }
      
      // 刷新房间列表
      await roomService.refreshRoomList()
      await loadRooms()
      
      // 刷新当前订单
      await loadCurrentOrder()
      
      showPlatformToast('刷新成功', 'success')
    } catch (error) {
      console.error('刷新失败:', error)
      showPlatformToast('刷新失败', 'error')
    } finally {
      setRefreshing(false)
      Taro.stopPullDownRefresh()
    }
  }

  const handleScan = () => {
    navigateTo('/pages/scan/index')
  }

  const handleRoomSelect = (room: Room) => {
    if (room.status !== 'available') {
      showPlatformToast('房间不可用', 'error')
      return
    }
    
    navigateTo(`/pages/room-detail/index?id=${room.id}`)
  }

  const handleOrderContinue = (order: Order) => {
    navigateTo(`/pages/order-detail/index?id=${order.id}&action=continue`)
  }

  const handleOrderSettle = (order: Order) => {
    navigateTo(`/pages/order-detail/index?id=${order.id}&action=settle`)
  }

  const handleOrderDetail = (order: Order) => {
    navigateTo(`/pages/order-detail/index?id=${order.id}`)
  }

  const getRoomStats = () => {
    const total = rooms.length
    const available = rooms.filter(room => room.status === 'available').length
    const occupied = rooms.filter(room => room.status === 'occupied').length
    
    return { total, available, occupied }
  }

  const stats = getRoomStats()

  return (
    <View className="index-page">
      {/* 用户信息栏 */}
      <View className="index-page__header">
        <View className="index-page__user">
          <View className="index-page__user-info">
            <Text className="index-page__user-name">
              {user?.nickname || '未登录'}
            </Text>
            <Text className="index-page__user-balance">
              余额：{user ? formatAmount(user.balance * 100) : '¥0.00'}
            </Text>
          </View>
          <View className="index-page__user-avatar">
            {user?.nickname?.charAt(0) || '?'}
          </View>
        </View>
      </View>

      <ScrollView 
        className="index-page__content"
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        {/* 快速扫码 */}
        <View className="index-page__scan-section">
          <Button 
            className="index-page__scan-btn"
            onClick={handleScan}
          >
            <View className="index-page__scan-icon">📱</View>
            <Text className="index-page__scan-text">扫码开台</Text>
          </Button>
        </View>

        {/* 当前订单 */}
        {currentOrder && (
          <View className="index-page__current-order">
            <View className="index-page__section-header">
              <Text className="index-page__section-title">当前订单</Text>
            </View>
            <OrderItem 
              order={currentOrder}
              showActions
              onContinue={handleOrderContinue}
              onSettle={handleOrderSettle}
              onDetail={handleOrderDetail}
            />
          </View>
        )}

        {/* 房间状态 */}
        <View className="index-page__rooms-section">
          <View className="index-page__section-header">
            <Text className="index-page__section-title">房间状态</Text>
            <Text className="index-page__section-subtitle">
              {stats.available}/{stats.total} 空闲
            </Text>
          </View>

          <View className="index-page__rooms-grid">
            {rooms.map(room => (
              <RoomCard 
                key={room.id}
                room={room}
                onClick={handleRoomSelect}
                size="medium"
                className={`room-card--${room.status}`}
              />
            ))}
          </View>

          {rooms.length === 0 && !loading && (
            <View className="index-page__empty">
              <Text className="index-page__empty-text">暂无房间信息</Text>
            </View>
          )}
        </View>

        {/* 快捷功能 */}
        <View className="index-page__quick-actions">
          <View className="index-page__action-item" onClick={() => navigateTo('/pages/reservation/index')}>
            <View className="index-page__action-icon">📅</View>
            <Text className="index-page__action-text">预约</Text>
          </View>
          <View className="index-page__action-item" onClick={() => navigateTo('/pages/package/index')}>
            <View className="index-page__action-icon">🎁</View>
            <Text className="index-page__action-text">套餐</Text>
          </View>
          <View className="index-page__action-item" onClick={() => navigateTo('/pages/recharge/index')}>
            <View className="index-page__action-icon">💰</View>
            <Text className="index-page__action-text">充值</Text>
          </View>
          <View className="index-page__action-item" onClick={() => navigateTo('/pages/settings/index')}>
            <View className="index-page__action-icon">⚙️</View>
            <Text className="index-page__action-text">设置</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

export default IndexPage
