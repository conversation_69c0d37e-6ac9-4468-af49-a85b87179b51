@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.index-page {
  min-height: 100vh;
  background-color: $background-color;

  &__header {
    padding: $spacing-base;
    background-color: $white;
    @include shadow(sm);
  }

  &__user {
    @include flex-between;
  }

  &__user-info {
    flex: 1;
  }

  &__user-name {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: 4px;
  }

  &__user-balance {
    font-size: $font-size-base;
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  &__user-avatar {
    width: 60px;
    height: 60px;
    @include flex-center;
    background-color: $primary-color;
    color: $white;
    border-radius: 50%;
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
  }

  &__content {
    height: calc(100vh - 120px);
    padding: $spacing-base;
  }

  &__scan-section {
    margin-bottom: $spacing-lg;
  }

  &__scan-btn {
    width: 100%;
    height: 120px;
    @include flex-center;
    flex-direction: column;
    background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
    color: $white;
    border-radius: $border-radius-xl;
    @include shadow(base);
    @include transition(all, $transition-duration-base);

    &:active {
      transform: scale(0.98);
    }

    &::after {
      border: none;
    }
  }

  &__scan-icon {
    font-size: 48px;
    margin-bottom: $spacing-xs;
  }

  &__scan-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
  }

  &__current-order {
    margin-bottom: $spacing-lg;
  }

  &__section-header {
    @include flex-between;
    margin-bottom: $spacing-base;
  }

  &__section-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }

  &__section-subtitle {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__rooms-section {
    margin-bottom: $spacing-lg;
  }

  &__rooms-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-base;
  }

  &__empty {
    @include flex-center;
    height: 200px;
    background-color: $white;
    border-radius: $border-radius-lg;
    border: 2px dashed $border-color;
  }

  &__empty-text {
    font-size: $font-size-base;
    color: $text-placeholder;
  }

  &__quick-actions {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-base;
    margin-bottom: $spacing-xl;
  }

  &__action-item {
    @include flex-center;
    flex-direction: column;
    height: 100px;
    background-color: $white;
    border-radius: $border-radius-lg;
    @include shadow(sm);
    @include transition(all, $transition-duration-base);
    cursor: pointer;

    &:active {
      transform: scale(0.95);
      background-color: $background-color;
    }
  }

  &__action-icon {
    font-size: 32px;
    margin-bottom: $spacing-xs;
  }

  &__action-text {
    font-size: $font-size-sm;
    color: $text-primary;
    font-weight: $font-weight-medium;
  }
}

// 响应式适配
@media (max-width: 480px) {
  .index-page {
    &__rooms-grid {
      grid-template-columns: 1fr;
    }

    &__quick-actions {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
