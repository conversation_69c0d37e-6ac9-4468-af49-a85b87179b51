import React, { useState } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import { authService } from '@/services/auth'
import { showPlatformToast } from '@/utils/platform'
import { formatTime } from '@/utils'
import type { Reservation } from '@/types'
import './index.scss'

const ReservationPage: React.FC = () => {
  const [reservations, setReservations] = useState<Reservation[]>([])
  const [loading, setLoading] = useState(false)

  useLoad(() => {
    console.log('Reservation page loaded')
    initializePage()
  })

  const initializePage = async () => {
    try {
      setLoading(true)
      
      // 检查登录状态
      if (!authService.isLoggedIn()) {
        showPlatformToast('请先登录', 'error')
        Taro.switchTab({
          url: '/pages/index/index'
        })
        return
      }
      
      await loadReservations()
    } catch (error) {
      console.error('页面初始化失败:', error)
      showPlatformToast('加载失败，请重试', 'error')
    } finally {
      setLoading(false)
    }
  }

  const loadReservations = async () => {
    try {
      // 模拟预约数据
      const mockReservations: Reservation[] = [
        {
          id: 1,
          user_id: 1,
          room_id: 1,
          room_number: '001',
          room_name: '豪华包间A',
          start_time: '2025-01-28T14:00:00Z',
          end_time: '2025-01-28T16:00:00Z',
          status: 'confirmed',
          created_at: '2025-01-27T10:00:00Z'
        },
        {
          id: 2,
          user_id: 1,
          room_id: 2,
          room_number: '002',
          room_name: '标准包间B',
          start_time: '2025-01-29T10:00:00Z',
          end_time: '2025-01-29T12:00:00Z',
          status: 'confirmed',
          created_at: '2025-01-27T11:00:00Z'
        }
      ]
      
      setReservations(mockReservations)
    } catch (error) {
      console.error('加载预约失败:', error)
    }
  }

  const handleCreateReservation = () => {
    showPlatformToast('预约功能开发中', 'none')
  }

  const handleCancelReservation = async (reservation: Reservation) => {
    try {
      const result = await Taro.showModal({
        title: '取消预约',
        content: `确定要取消${reservation.room_name}的预约吗？`,
        confirmText: '取消预约',
        cancelText: '保留'
      })
      
      if (result.confirm) {
        // 这里应该调用取消预约的API
        showPlatformToast('预约已取消', 'success')
        
        // 更新本地状态
        setReservations(prev => 
          prev.map(item => 
            item.id === reservation.id 
              ? { ...item, status: 'cancelled' }
              : item
          )
        )
      }
    } catch (error) {
      console.error('取消预约失败:', error)
      showPlatformToast('取消失败', 'error')
    }
  }

  const getStatusText = (status: string) => {
    const statusMap = {
      confirmed: '已确认',
      cancelled: '已取消',
      completed: '已完成'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap = {
      confirmed: '#1890FF',
      cancelled: '#FF4D4F',
      completed: '#52C41A'
    }
    return colorMap[status] || '#999999'
  }

  return (
    <View className="reservation-page">
      <ScrollView className="reservation-page__content" scrollY>
        {/* 创建预约按钮 */}
        <View className="reservation-page__create-section">
          <View 
            className="reservation-page__create-btn"
            onClick={handleCreateReservation}
          >
            <View className="reservation-page__create-icon">+</View>
            <Text className="reservation-page__create-text">创建新预约</Text>
          </View>
        </View>

        {/* 预约列表 */}
        <View className="reservation-page__list-section">
          <View className="reservation-page__section-header">
            <Text className="reservation-page__section-title">我的预约</Text>
            <Text className="reservation-page__section-count">
              共{reservations.length}条
            </Text>
          </View>

          {reservations.length > 0 ? (
            <View className="reservation-page__list">
              {reservations.map(reservation => (
                <View 
                  key={reservation.id}
                  className="reservation-page__item"
                >
                  <View className="reservation-page__item-header">
                    <Text className="reservation-page__room-name">
                      {reservation.room_name}
                    </Text>
                    <View 
                      className="reservation-page__status"
                      style={{ color: getStatusColor(reservation.status) }}
                    >
                      <View 
                        className="reservation-page__status-dot"
                        style={{ backgroundColor: getStatusColor(reservation.status) }}
                      />
                      <Text className="reservation-page__status-text">
                        {getStatusText(reservation.status)}
                      </Text>
                    </View>
                  </View>

                  <View className="reservation-page__item-content">
                    <View className="reservation-page__info-row">
                      <Text className="reservation-page__info-label">房间号</Text>
                      <Text className="reservation-page__info-value">
                        {reservation.room_number}号
                      </Text>
                    </View>
                    
                    <View className="reservation-page__info-row">
                      <Text className="reservation-page__info-label">预约时间</Text>
                      <Text className="reservation-page__info-value">
                        {formatTime(reservation.start_time, 'MM-DD HH:mm')} - {formatTime(reservation.end_time, 'HH:mm')}
                      </Text>
                    </View>
                    
                    <View className="reservation-page__info-row">
                      <Text className="reservation-page__info-label">创建时间</Text>
                      <Text className="reservation-page__info-value">
                        {formatTime(reservation.created_at, 'MM-DD HH:mm')}
                      </Text>
                    </View>
                  </View>

                  {reservation.status === 'confirmed' && (
                    <View className="reservation-page__item-actions">
                      <View 
                        className="reservation-page__action-btn reservation-page__action-btn--cancel"
                        onClick={() => handleCancelReservation(reservation)}
                      >
                        <Text className="reservation-page__action-text">取消预约</Text>
                      </View>
                    </View>
                  )}
                </View>
              ))}
            </View>
          ) : (
            <View className="reservation-page__empty">
              <View className="reservation-page__empty-icon">📅</View>
              <Text className="reservation-page__empty-text">暂无预约记录</Text>
              <Text className="reservation-page__empty-tip">
                点击上方按钮创建新预约
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  )
}

export default ReservationPage
