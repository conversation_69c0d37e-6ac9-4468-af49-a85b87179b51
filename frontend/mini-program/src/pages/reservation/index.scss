@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.reservation-page {
  min-height: 100vh;
  background-color: $background-color;

  &__content {
    height: 100vh;
    padding: $spacing-base;
  }

  &__create-section {
    margin-bottom: $spacing-lg;
  }

  &__create-btn {
    @include flex-center;
    height: 120px;
    background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
    color: $white;
    border-radius: $border-radius-xl;
    @include shadow(base);
    @include transition(all, $transition-duration-base);
    cursor: pointer;

    &:active {
      transform: scale(0.98);
    }
  }

  &__create-icon {
    font-size: 48px;
    font-weight: $font-weight-bold;
    margin-right: $spacing-sm;
  }

  &__create-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
  }

  &__list-section {
    margin-bottom: $spacing-lg;
  }

  &__section-header {
    @include flex-between;
    margin-bottom: $spacing-base;
    padding: 0 $spacing-xs;
  }

  &__section-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }

  &__section-count {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__list {
    @include flex-column;
    gap: $spacing-base;
  }

  &__item {
    @include card($spacing-base, $border-radius-lg);
    @include transition(all, $transition-duration-base);
  }

  &__item-header {
    @include flex-between;
    margin-bottom: $spacing-base;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid $border-color;
  }

  &__room-name {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }

  &__status {
    @include flex(row, flex-start, center);
    gap: 4px;
  }

  &__status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  &__status-text {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }

  &__item-content {
    margin-bottom: $spacing-base;
  }

  &__info-row {
    @include flex-between;
    margin-bottom: $spacing-xs;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__info-label {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__info-value {
    font-size: $font-size-sm;
    color: $text-primary;
    font-weight: $font-weight-medium;
  }

  &__item-actions {
    @include flex(row, flex-end, center);
    padding-top: $spacing-base;
    border-top: 1px solid $border-color;
  }

  &__action-btn {
    padding: $spacing-xs $spacing-base;
    border-radius: $border-radius-base;
    @include transition(all, $transition-duration-base);
    cursor: pointer;

    &--cancel {
      background-color: $white;
      border: 1px solid $error-color;

      &:active {
        background-color: rgba($error-color, 0.05);
      }
    }
  }

  &__action-text {
    font-size: $font-size-sm;
    color: $error-color;
    font-weight: $font-weight-medium;
  }

  &__empty {
    @include flex-center;
    flex-direction: column;
    height: 300px;
    background-color: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
  }

  &__empty-icon {
    font-size: 64px;
    margin-bottom: $spacing-base;
    opacity: 0.5;
  }

  &__empty-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
    font-weight: $font-weight-medium;
  }

  &__empty-tip {
    font-size: $font-size-sm;
    color: $text-placeholder;
    text-align: center;
    line-height: 1.5;
  }
}
