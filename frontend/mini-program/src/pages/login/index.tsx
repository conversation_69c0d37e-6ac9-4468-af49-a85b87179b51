import React, { useState } from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import { authService } from '@/services/auth'
import { getCurrentPlatform, showPlatformToast } from '@/utils/platform'
import './index.scss'

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const platform = getCurrentPlatform()
  const platformName = platform === 'wechat' ? '微信' : '支付宝'

  useLoad(() => {
    console.log('Login page loaded')
  })

  const handleLogin = async () => {
    if (loading) return

    try {
      setLoading(true)
      
      // 尝试登录
      await authService.login()
      
      // 登录成功，跳转到首页
      Taro.switchTab({
        url: '/pages/index/index'
      })
    } catch (error) {
      console.error('登录失败:', error)
      
      // 如果是用户不存在，尝试注册
      if (error.message.includes('用户不存在')) {
        try {
          await authService.register()
          
          // 注册成功，跳转到首页
          Taro.switchTab({
            url: '/pages/index/index'
          })
        } catch (registerError) {
          console.error('注册失败:', registerError)
          showPlatformToast('注册失败，请重试', 'error')
        }
      } else {
        showPlatformToast('登录失败，请重试', 'error')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleSkip = () => {
    // 游客模式，跳转到首页（功能受限）
    Taro.switchTab({
      url: '/pages/index/index'
    })
  }

  return (
    <View className="login-page">
      <View className="login-page__header">
        <Image 
          className="login-page__logo"
          src="/assets/images/logo.png"
          mode="aspectFit"
        />
        <Text className="login-page__title">自助麻将室</Text>
        <Text className="login-page__subtitle">便捷的麻将室预订服务</Text>
      </View>

      <View className="login-page__content">
        <View className="login-page__features">
          <View className="login-page__feature">
            <View className="login-page__feature-icon">📱</View>
            <Text className="login-page__feature-text">扫码开台</Text>
          </View>
          <View className="login-page__feature">
            <View className="login-page__feature-icon">💰</View>
            <Text className="login-page__feature-text">智能计费</Text>
          </View>
          <View className="login-page__feature">
            <View className="login-page__feature-icon">📅</View>
            <Text className="login-page__feature-text">在线预约</Text>
          </View>
          <View className="login-page__feature">
            <View className="login-page__feature-icon">🎁</View>
            <Text className="login-page__feature-text">优惠套餐</Text>
          </View>
        </View>

        <View className="login-page__description">
          <Text className="login-page__description-text">
            使用{platformName}账号登录，享受更多便捷服务
          </Text>
        </View>
      </View>

      <View className="login-page__actions">
        <Button 
          className="login-page__login-btn"
          type="primary"
          loading={loading}
          onClick={handleLogin}
        >
          {loading ? '登录中...' : `${platformName}一键登录`}
        </Button>
        
        <Button 
          className="login-page__skip-btn"
          onClick={handleSkip}
        >
          暂不登录，先看看
        </Button>
      </View>

      <View className="login-page__footer">
        <Text className="login-page__privacy-text">
          登录即表示同意
          <Text className="login-page__privacy-link">《用户协议》</Text>
          和
          <Text className="login-page__privacy-link">《隐私政策》</Text>
        </Text>
      </View>
    </View>
  )
}

export default LoginPage
