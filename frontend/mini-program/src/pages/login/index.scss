@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.login-page {
  min-height: 100vh;
  @include flex-column;
  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
  padding: $spacing-xl $spacing-base;

  &__header {
    @include flex-column;
    align-items: center;
    padding: $spacing-xl 0;
    flex: 1;
    justify-content: center;
  }

  &__logo {
    width: 120px;
    height: 120px;
    margin-bottom: $spacing-lg;
    border-radius: $border-radius-xl;
    @include shadow(lg);
  }

  &__title {
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: $white;
    margin-bottom: $spacing-sm;
    text-align: center;
  }

  &__subtitle {
    font-size: $font-size-base;
    color: rgba($white, 0.9);
    text-align: center;
  }

  &__content {
    flex: 1;
    @include flex-column;
    justify-content: center;
    padding: $spacing-lg 0;
  }

  &__features {
    @include flex(row, space-around, center);
    flex-wrap: wrap;
    margin-bottom: $spacing-xl;
    padding: $spacing-lg;
    background-color: rgba($white, 0.1);
    border-radius: $border-radius-xl;
    backdrop-filter: blur(10px);
  }

  &__feature {
    @include flex-column;
    align-items: center;
    width: 25%;
    margin-bottom: $spacing-base;
  }

  &__feature-icon {
    font-size: 40px;
    margin-bottom: $spacing-xs;
  }

  &__feature-text {
    font-size: $font-size-sm;
    color: $white;
    text-align: center;
  }

  &__description {
    text-align: center;
    margin-bottom: $spacing-xl;
  }

  &__description-text {
    font-size: $font-size-base;
    color: rgba($white, 0.9);
    line-height: 1.6;
  }

  &__actions {
    padding: $spacing-base 0;
  }

  &__login-btn {
    width: 100%;
    height: 88px;
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    background-color: $white;
    color: $primary-color;
    border-radius: $border-radius-xl;
    margin-bottom: $spacing-base;
    @include shadow(base);

    &:active {
      transform: scale(0.98);
    }

    &::after {
      border: none;
    }
  }

  &__skip-btn {
    width: 100%;
    height: 80px;
    font-size: $font-size-base;
    background-color: transparent;
    color: rgba($white, 0.8);
    border: 2px solid rgba($white, 0.3);
    border-radius: $border-radius-xl;

    &:active {
      background-color: rgba($white, 0.1);
    }

    &::after {
      border: none;
    }
  }

  &__footer {
    padding: $spacing-lg 0 $spacing-base;
    text-align: center;
  }

  &__privacy-text {
    font-size: $font-size-xs;
    color: rgba($white, 0.7);
    line-height: 1.5;
  }

  &__privacy-link {
    color: $white;
    text-decoration: underline;
  }
}

// 适配安全区域
.login-page {
  padding-top: calc(#{$spacing-xl} + constant(safe-area-inset-top));
  padding-top: calc(#{$spacing-xl} + env(safe-area-inset-top));
  padding-bottom: calc(#{$spacing-base} + constant(safe-area-inset-bottom));
  padding-bottom: calc(#{$spacing-base} + env(safe-area-inset-bottom));
}
