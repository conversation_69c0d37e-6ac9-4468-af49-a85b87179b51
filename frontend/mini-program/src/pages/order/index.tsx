import React, { useState } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import Taro, { useLoad, usePullDownRefresh } from '@tarojs/taro'
import OrderItem from '@/components/OrderItem'
import { authService } from '@/services/auth'
import { storage } from '@/utils/storage'
import { showPlatformToast, navigateTo } from '@/utils/platform'
import type { Order } from '@/types'
import './index.scss'

const OrderPage: React.FC = () => {
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null)
  const [historyOrders, setHistoryOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  useLoad(() => {
    console.log('Order page loaded')
    initializePage()
  })

  usePullDownRefresh(() => {
    handleRefresh()
  })

  const initializePage = async () => {
    try {
      setLoading(true)
      
      // 检查登录状态
      if (!authService.isLoggedIn()) {
        showPlatformToast('请先登录', 'error')
        Taro.switchTab({
          url: '/pages/index/index'
        })
        return
      }
      
      await loadOrders()
    } catch (error) {
      console.error('页面初始化失败:', error)
      showPlatformToast('加载失败，请重试', 'error')
    } finally {
      setLoading(false)
    }
  }

  const loadOrders = async () => {
    try {
      // 获取当前订单
      const current = storage.getCurrentOrder()
      setCurrentOrder(current)
      
      // 模拟历史订单数据
      const mockHistoryOrders: Order[] = [
        {
          id: 1,
          user_id: 1,
          room_id: 1,
          room_number: '001',
          room_name: '豪华包间A',
          start_time: '2025-01-26T10:00:00Z',
          end_time: '2025-01-26T13:30:00Z',
          total_amount: 7000, // 70元
          paid_amount: 7000,
          status: 'completed',
          package_type: 'hourly',
          payment_method: 'wechat',
          created_at: '2025-01-26T10:00:00Z',
          updated_at: '2025-01-26T13:30:00Z'
        },
        {
          id: 2,
          user_id: 1,
          room_id: 2,
          room_number: '002',
          room_name: '标准包间B',
          start_time: '2025-01-25T14:00:00Z',
          end_time: '2025-01-25T16:45:00Z',
          total_amount: 5500, // 55元
          paid_amount: 5500,
          status: 'completed',
          package_type: 'package',
          package_hours_used: 3,
          payment_method: 'package',
          created_at: '2025-01-25T14:00:00Z',
          updated_at: '2025-01-25T16:45:00Z'
        }
      ]
      
      setHistoryOrders(mockHistoryOrders)
    } catch (error) {
      console.error('加载订单失败:', error)
    }
  }

  const handleRefresh = async () => {
    try {
      setRefreshing(true)
      await loadOrders()
      showPlatformToast('刷新成功', 'success')
    } catch (error) {
      console.error('刷新失败:', error)
      showPlatformToast('刷新失败', 'error')
    } finally {
      setRefreshing(false)
      Taro.stopPullDownRefresh()
    }
  }

  const handleOrderContinue = (order: Order) => {
    navigateTo(`/pages/order-detail/index?id=${order.id}&action=continue`)
  }

  const handleOrderSettle = (order: Order) => {
    navigateTo(`/pages/order-detail/index?id=${order.id}&action=settle`)
  }

  const handleOrderDetail = (order: Order) => {
    navigateTo(`/pages/order-detail/index?id=${order.id}`)
  }

  return (
    <View className="order-page">
      <ScrollView 
        className="order-page__content"
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
      >
        {/* 当前订单 */}
        {currentOrder && (
          <View className="order-page__current-section">
            <View className="order-page__section-header">
              <Text className="order-page__section-title">使用中</Text>
              <View className="order-page__section-badge">
                <Text className="order-page__badge-text">进行中</Text>
              </View>
            </View>
            
            <OrderItem 
              order={currentOrder}
              showActions
              onContinue={handleOrderContinue}
              onSettle={handleOrderSettle}
              onDetail={handleOrderDetail}
            />
          </View>
        )}

        {/* 历史订单 */}
        <View className="order-page__history-section">
          <View className="order-page__section-header">
            <Text className="order-page__section-title">历史订单</Text>
            <Text className="order-page__section-count">
              共{historyOrders.length}条
            </Text>
          </View>

          {historyOrders.length > 0 ? (
            <View className="order-page__order-list">
              {historyOrders.map(order => (
                <OrderItem 
                  key={order.id}
                  order={order}
                  onDetail={handleOrderDetail}
                  className={`order-item--${order.status}`}
                />
              ))}
            </View>
          ) : (
            <View className="order-page__empty">
              <View className="order-page__empty-icon">📋</View>
              <Text className="order-page__empty-text">暂无历史订单</Text>
              <Text className="order-page__empty-tip">
                扫码开台后，订单记录会显示在这里
              </Text>
            </View>
          )}
        </View>

        {/* 底部占位 */}
        <View className="order-page__bottom-spacer" />
      </ScrollView>
    </View>
  )
}

export default OrderPage
