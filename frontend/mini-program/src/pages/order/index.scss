@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.order-page {
  min-height: 100vh;
  background-color: $background-color;

  &__content {
    height: 100vh;
    padding: $spacing-base;
  }

  &__current-section {
    margin-bottom: $spacing-lg;
  }

  &__history-section {
    margin-bottom: $spacing-lg;
  }

  &__section-header {
    @include flex-between;
    margin-bottom: $spacing-base;
    padding: 0 $spacing-xs;
  }

  &__section-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }

  &__section-badge {
    padding: 4px 12px;
    background-color: $primary-color;
    border-radius: $border-radius-round;
  }

  &__badge-text {
    font-size: $font-size-xs;
    color: $white;
    font-weight: $font-weight-medium;
  }

  &__section-count {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__order-list {
    @include flex-column;
    gap: $spacing-base;
  }

  &__empty {
    @include flex-center;
    flex-direction: column;
    height: 300px;
    background-color: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
  }

  &__empty-icon {
    font-size: 64px;
    margin-bottom: $spacing-base;
    opacity: 0.5;
  }

  &__empty-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
    font-weight: $font-weight-medium;
  }

  &__empty-tip {
    font-size: $font-size-sm;
    color: $text-placeholder;
    text-align: center;
    line-height: 1.5;
  }

  &__bottom-spacer {
    height: 100px;
  }
}

// 订单项状态样式
.order-item {
  &--pending {
    border-left: 4px solid $order-pending-color;
  }

  &--paid {
    border-left: 4px solid $order-paid-color;
  }

  &--completed {
    border-left: 4px solid $order-completed-color;
  }

  &--cancelled {
    border-left: 4px solid $order-cancelled-color;
    opacity: 0.7;
  }
}
