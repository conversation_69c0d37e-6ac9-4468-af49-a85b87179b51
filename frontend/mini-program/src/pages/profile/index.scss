@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.profile-page {
  min-height: 100vh;
  background-color: $background-color;

  &__content {
    height: 100vh;
  }

  &__user-section {
    margin-bottom: $spacing-base;
  }

  &__user-info {
    @include flex(row, space-between, center);
    padding: $spacing-lg $spacing-base;
    background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
    color: $white;
  }

  &__avatar {
    width: 80px;
    height: 80px;
    @include flex-center;
    background-color: rgba($white, 0.2);
    border-radius: 50%;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    margin-right: $spacing-base;
  }

  &__user-details {
    flex: 1;
  }

  &__username {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    margin-bottom: 4px;
  }

  &__user-id {
    font-size: $font-size-sm;
    opacity: 0.8;
    margin-bottom: 4px;
  }

  &__platform {
    font-size: $font-size-sm;
    opacity: 0.8;
  }

  &__balance {
    @include flex-center;
    flex-direction: column;
    text-align: center;
  }

  &__balance-label {
    font-size: $font-size-sm;
    opacity: 0.8;
    margin-bottom: 4px;
  }

  &__balance-amount {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
  }

  &__login-prompt {
    @include flex(row, space-between, center);
    padding: $spacing-lg $spacing-base;
    background-color: $white;
    @include shadow(sm);
  }

  &__login-avatar {
    width: 80px;
    height: 80px;
    @include flex-center;
    background-color: $background-color;
    border-radius: 50%;
    font-size: $font-size-xl;
    color: $text-placeholder;
    margin-right: $spacing-base;
  }

  &__login-info {
    flex: 1;
  }

  &__login-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: 4px;
  }

  &__login-subtitle {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__login-btn {
    width: 80px;
    height: 60px;
    font-size: $font-size-sm;
    border-radius: $border-radius-lg;

    &::after {
      border: none;
    }
  }

  &__menu-section {
    background-color: $white;
    margin-bottom: $spacing-base;
  }

  &__menu-item {
    @include flex(row, flex-start, center);
    padding: $spacing-base;
    border-bottom: 1px solid $border-color;
    @include transition(background-color, $transition-duration-base);
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: $background-color;
    }
  }

  &__menu-icon {
    width: 40px;
    height: 40px;
    @include flex-center;
    font-size: 24px;
    margin-right: $spacing-base;
  }

  &__menu-content {
    flex: 1;
  }

  &__menu-title {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: 4px;
  }

  &__menu-subtitle {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__menu-arrow {
    font-size: $font-size-lg;
    color: $text-placeholder;
    margin-left: $spacing-sm;
  }

  &__logout-section {
    padding: $spacing-base;
  }

  &__logout-btn {
    width: 100%;
    height: 80px;
    font-size: $font-size-base;
    background-color: $white;
    color: $error-color;
    border: 2px solid $error-color;
    border-radius: $border-radius-lg;

    &:active {
      background-color: rgba($error-color, 0.05);
    }

    &::after {
      border: none;
    }
  }

  &__version-section {
    @include flex-center;
    padding: $spacing-lg;
  }

  &__version-text {
    font-size: $font-size-sm;
    color: $text-placeholder;
  }
}

// 适配安全区域
.profile-page {
  &__user-info,
  &__login-prompt {
    padding-top: calc(#{$spacing-lg} + constant(safe-area-inset-top));
    padding-top: calc(#{$spacing-lg} + env(safe-area-inset-top));
  }

  &__version-section {
    padding-bottom: calc(#{$spacing-lg} + constant(safe-area-inset-bottom));
    padding-bottom: calc(#{$spacing-lg} + env(safe-area-inset-bottom));
  }
}
