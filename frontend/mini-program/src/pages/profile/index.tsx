import React, { useState } from 'react'
import { View, Text, <PERSON><PERSON>, ScrollView } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import { authService } from '@/services/auth'
import { showPlatformToast, navigateTo, getCurrentPlatform } from '@/utils/platform'
import { formatAmount } from '@/utils'
import type { User } from '@/types'
import './index.scss'

const ProfilePage: React.FC = () => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const platform = getCurrentPlatform()
  const platformName = platform === 'wechat' ? '微信' : '支付宝'

  useLoad(() => {
    console.log('Profile page loaded')
    initializePage()
  })

  const initializePage = async () => {
    try {
      setLoading(true)
      
      // 获取用户信息
      const currentUser = authService.getCurrentUser()
      setUser(currentUser)
      
      // 如果已登录，刷新用户信息
      if (currentUser) {
        try {
          const updatedUser = await authService.refreshUserInfo()
          setUser(updatedUser)
        } catch (error) {
          console.error('刷新用户信息失败:', error)
        }
      }
    } catch (error) {
      console.error('页面初始化失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogin = async () => {
    try {
      await authService.login()
      const user = authService.getCurrentUser()
      setUser(user)
      showPlatformToast('登录成功', 'success')
    } catch (error) {
      console.error('登录失败:', error)
      showPlatformToast('登录失败', 'error')
    }
  }

  const handleLogout = async () => {
    try {
      const result = await Taro.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        confirmText: '退出',
        cancelText: '取消'
      })
      
      if (result.confirm) {
        await authService.logout()
        setUser(null)
        showPlatformToast('已退出登录', 'success')
      }
    } catch (error) {
      console.error('退出登录失败:', error)
    }
  }

  const handleRecharge = () => {
    if (!user) {
      showPlatformToast('请先登录', 'error')
      return
    }
    navigateTo('/pages/recharge/index')
  }

  const handlePackage = () => {
    if (!user) {
      showPlatformToast('请先登录', 'error')
      return
    }
    navigateTo('/pages/package/index')
  }

  const handleSettings = () => {
    navigateTo('/pages/settings/index')
  }

  const menuItems = [
    {
      icon: '💰',
      title: '余额充值',
      subtitle: '充值余额，享受便捷支付',
      onClick: handleRecharge,
      showArrow: true
    },
    {
      icon: '🎁',
      title: '我的套餐',
      subtitle: '查看和管理套餐',
      onClick: handlePackage,
      showArrow: true
    },
    {
      icon: '📊',
      title: '消费记录',
      subtitle: '查看详细消费明细',
      onClick: () => navigateTo('/pages/consumption/index'),
      showArrow: true
    },
    {
      icon: '🎫',
      title: '优惠券',
      subtitle: '查看可用优惠券',
      onClick: () => navigateTo('/pages/coupon/index'),
      showArrow: true
    },
    {
      icon: '⚙️',
      title: '设置',
      subtitle: '个人设置和偏好',
      onClick: handleSettings,
      showArrow: true
    },
    {
      icon: '📞',
      title: '联系客服',
      subtitle: '遇到问题？联系我们',
      onClick: () => {
        Taro.makePhoneCall({
          phoneNumber: '************'
        })
      },
      showArrow: false
    },
    {
      icon: '📋',
      title: '关于我们',
      subtitle: '了解更多信息',
      onClick: () => navigateTo('/pages/about/index'),
      showArrow: true
    }
  ]

  return (
    <View className="profile-page">
      <ScrollView className="profile-page__content" scrollY>
        {/* 用户信息区域 */}
        <View className="profile-page__user-section">
          {user ? (
            <View className="profile-page__user-info">
              <View className="profile-page__avatar">
                {user.nickname?.charAt(0) || '?'}
              </View>
              <View className="profile-page__user-details">
                <Text className="profile-page__username">{user.nickname}</Text>
                <Text className="profile-page__user-id">ID: {user.id}</Text>
                <Text className="profile-page__platform">
                  {platformName}用户
                </Text>
              </View>
              <View className="profile-page__balance">
                <Text className="profile-page__balance-label">余额</Text>
                <Text className="profile-page__balance-amount">
                  {formatAmount(user.balance * 100)}
                </Text>
              </View>
            </View>
          ) : (
            <View className="profile-page__login-prompt">
              <View className="profile-page__login-avatar">?</View>
              <View className="profile-page__login-info">
                <Text className="profile-page__login-title">未登录</Text>
                <Text className="profile-page__login-subtitle">
                  登录后享受更多服务
                </Text>
              </View>
              <Button 
                className="profile-page__login-btn"
                size="mini"
                type="primary"
                onClick={handleLogin}
              >
                登录
              </Button>
            </View>
          )}
        </View>

        {/* 功能菜单 */}
        <View className="profile-page__menu-section">
          {menuItems.map((item, index) => (
            <View 
              key={index}
              className="profile-page__menu-item"
              onClick={item.onClick}
            >
              <View className="profile-page__menu-icon">{item.icon}</View>
              <View className="profile-page__menu-content">
                <Text className="profile-page__menu-title">{item.title}</Text>
                <Text className="profile-page__menu-subtitle">{item.subtitle}</Text>
              </View>
              {item.showArrow && (
                <View className="profile-page__menu-arrow">›</View>
              )}
            </View>
          ))}
        </View>

        {/* 退出登录 */}
        {user && (
          <View className="profile-page__logout-section">
            <Button 
              className="profile-page__logout-btn"
              onClick={handleLogout}
            >
              退出登录
            </Button>
          </View>
        )}

        {/* 版本信息 */}
        <View className="profile-page__version-section">
          <Text className="profile-page__version-text">
            自助麻将室 v1.0.0
          </Text>
        </View>
      </ScrollView>
    </View>
  )
}

export default ProfilePage
