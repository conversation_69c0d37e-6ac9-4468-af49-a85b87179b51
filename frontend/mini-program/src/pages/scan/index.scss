@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.scan-page {
  min-height: 100vh;
  background-color: #000000;
  @include flex-column;

  &__camera-section {
    flex: 1;
    @include flex-column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl $spacing-base;
  }

  &__camera-container {
    position: relative;
    width: 300px;
    height: 300px;
    margin-bottom: $spacing-lg;
  }

  &__camera {
    width: 100%;
    height: 100%;
    border-radius: $border-radius-lg;
    overflow: hidden;
  }

  &__scan-frame {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }

  &__scan-corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid $white;

    &--top-left {
      top: 20px;
      left: 20px;
      border-right: none;
      border-bottom: none;
    }

    &--top-right {
      top: 20px;
      right: 20px;
      border-left: none;
      border-bottom: none;
    }

    &--bottom-left {
      bottom: 20px;
      left: 20px;
      border-right: none;
      border-top: none;
    }

    &--bottom-right {
      bottom: 20px;
      right: 20px;
      border-left: none;
      border-top: none;
    }
  }

  &__scan-tip {
    font-size: $font-size-base;
    color: $white;
    text-align: center;
    opacity: 0.8;
  }

  &__room-info {
    background-color: $white;
    margin: $spacing-base;
    padding: $spacing-base;
    border-radius: $border-radius-lg;
    @include shadow(base);
  }

  &__room-header {
    @include flex-between;
    margin-bottom: $spacing-base;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid $border-color;
  }

  &__room-number {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }

  &__room-status {
    @include flex(row, flex-start, center);
    gap: 4px;
  }

  &__status-dot {
    width: 8px;
    height: 8px;
    background-color: $success-color;
    border-radius: 50%;
  }

  &__status-text {
    font-size: $font-size-sm;
    color: $success-color;
    font-weight: $font-weight-medium;
  }

  &__room-content {
    @include flex-column;
    gap: $spacing-xs;
  }

  &__room-name {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-primary;
  }

  &__room-price {
    font-size: $font-size-base;
    font-weight: $font-weight-bold;
    color: $primary-color;
  }

  &__room-description {
    font-size: $font-size-sm;
    color: $text-secondary;
    line-height: 1.5;
  }

  &__actions {
    padding: $spacing-base;
    background-color: $white;
    @include flex-column;
    gap: $spacing-base;
  }

  &__scan-btn {
    width: 100%;
    height: 88px;
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    background-color: $primary-color;
    color: $white;
    border-radius: $border-radius-xl;
    @include shadow(base);

    &:active {
      transform: scale(0.98);
      background-color: $primary-dark;
    }

    &::after {
      border: none;
    }
  }

  &__manual-btn {
    width: 100%;
    height: 80px;
    font-size: $font-size-base;
    background-color: $white;
    color: $text-primary;
    border: 2px solid $border-color;
    border-radius: $border-radius-xl;

    &:active {
      background-color: $background-color;
    }

    &::after {
      border: none;
    }
  }
}

// 适配安全区域
.scan-page {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
