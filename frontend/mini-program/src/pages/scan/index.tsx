import React, { useState } from 'react'
import { View, Text, Button, Camera } from '@tarojs/components'
import Taro, { useLoad } from '@tarojs/taro'
import PaymentModal from '@/components/PaymentModal'
import { scanService } from '@/services/scan'
import { paymentService } from '@/services/payment'
import { authService } from '@/services/auth'
import { showPlatformToast, navigateTo } from '@/utils/platform'
import { formatAmount } from '@/utils'
import type { Room, PaymentMethod } from '@/types'
import './index.scss'

const ScanPage: React.FC = () => {
  const [room, setRoom] = useState<Room | null>(null)
  const [scanning, setScanning] = useState(false)
  const [showPayment, setShowPayment] = useState(false)
  const [estimatedAmount, setEstimatedAmount] = useState(0)

  useLoad(() => {
    console.log('Scan page loaded')
    checkLoginStatus()
  })

  const checkLoginStatus = () => {
    if (!authService.isLoggedIn()) {
      showPlatformToast('请先登录', 'error')
      Taro.redirectTo({
        url: '/pages/login/index'
      })
    }
  }

  const handleScan = async () => {
    if (scanning) return

    try {
      setScanning(true)
      
      const result = await scanService.scanToStartRoom()
      
      if (!result.canStart) {
        showPlatformToast(result.reason || '房间不可用', 'error')
        return
      }
      
      setRoom(result.room)
      
      // 计算预估费用（预付1小时）
      const amount = result.room.price_per_hour * 100 // 转换为分
      setEstimatedAmount(amount)
      
      // 显示支付弹窗
      setShowPayment(true)
    } catch (error) {
      console.error('扫码失败:', error)
      
      if (error.message.includes('用户取消')) {
        // 用户取消扫码，不显示错误提示
        return
      }
      
      showPlatformToast(error.message || '扫码失败', 'error')
    } finally {
      setScanning(false)
    }
  }

  const handlePaymentConfirm = async (paymentMethod: PaymentMethod, extra?: any) => {
    if (!room) return

    try {
      setShowPayment(false)
      
      // 创建订单
      const orderData = {
        room_id: room.id,
        start_time: new Date().toISOString(),
        total_amount: estimatedAmount,
        package_type: paymentMethod === 'package' ? 'package' : 'hourly',
        user_package_id: extra?.userPackageId
      }
      
      // 这里应该调用创建订单的API
      // const order = await orderService.createOrder(orderData)
      
      // 发起支付
      const paymentParams = {
        orderId: 1, // 临时订单ID
        amount: estimatedAmount,
        paymentMethod,
        ...extra
      }
      
      await paymentService.pay(paymentParams)
      
      showPlatformToast('开台成功', 'success')
      
      // 跳转到订单详情页
      navigateTo('/pages/order-detail/index?id=1')
    } catch (error) {
      console.error('支付失败:', error)
      showPlatformToast(error.message || '支付失败', 'error')
    }
  }

  const handlePaymentCancel = () => {
    setShowPayment(false)
    setRoom(null)
  }

  const handleManualInput = () => {
    // 手动输入房间号
    Taro.showModal({
      title: '输入房间号',
      editable: true,
      placeholderText: '请输入房间号',
      success: async (res) => {
        if (res.confirm && res.content) {
          try {
            const result = await scanService.validateRoomQRCode(`ROOM_${res.content}`)
            if (result.isValid && result.room) {
              setRoom(result.room)
              const amount = result.room.price_per_hour * 100
              setEstimatedAmount(amount)
              setShowPayment(true)
            } else {
              showPlatformToast(result.error || '房间不存在', 'error')
            }
          } catch (error) {
            showPlatformToast('查询房间失败', 'error')
          }
        }
      }
    })
  }

  return (
    <View className="scan-page">
      {/* 扫码区域 */}
      <View className="scan-page__camera-section">
        <View className="scan-page__camera-container">
          <Camera 
            className="scan-page__camera"
            devicePosition="back"
            flash="off"
            onScanCode={handleScan}
          />
          <View className="scan-page__scan-frame">
            <View className="scan-page__scan-corner scan-page__scan-corner--top-left" />
            <View className="scan-page__scan-corner scan-page__scan-corner--top-right" />
            <View className="scan-page__scan-corner scan-page__scan-corner--bottom-left" />
            <View className="scan-page__scan-corner scan-page__scan-corner--bottom-right" />
          </View>
        </View>
        
        <Text className="scan-page__scan-tip">
          请将摄像头对准房间二维码
        </Text>
      </View>

      {/* 房间信息 */}
      {room && (
        <View className="scan-page__room-info">
          <View className="scan-page__room-header">
            <Text className="scan-page__room-number">房间{room.room_number}号</Text>
            <View className="scan-page__room-status">
              <View className="scan-page__status-dot" />
              <Text className="scan-page__status-text">空闲</Text>
            </View>
          </View>
          
          <View className="scan-page__room-content">
            <Text className="scan-page__room-name">{room.name}</Text>
            <Text className="scan-page__room-price">
              计费：{formatAmount(room.price_per_hour * 100)}/小时
            </Text>
            {room.description && (
              <Text className="scan-page__room-description">{room.description}</Text>
            )}
          </View>
        </View>
      )}

      {/* 操作按钮 */}
      <View className="scan-page__actions">
        <Button 
          className="scan-page__scan-btn"
          type="primary"
          loading={scanning}
          onClick={handleScan}
        >
          {scanning ? '扫码中...' : '扫码开台'}
        </Button>
        
        <Button 
          className="scan-page__manual-btn"
          onClick={handleManualInput}
        >
          手动输入房间号
        </Button>
      </View>

      {/* 支付弹窗 */}
      <PaymentModal 
        visible={showPayment}
        amount={estimatedAmount}
        title="确认开台"
        description={room ? `房间${room.room_number}号 - ${room.name}` : ''}
        onConfirm={handlePaymentConfirm}
        onCancel={handlePaymentCancel}
      />
    </View>
  )
}

export default ScanPage
