import '@testing-library/jest-dom'

// Mock Taro APIs
const mockTaro = {
  login: jest.fn(),
  getUserProfile: jest.fn(),
  requestPayment: jest.fn(),
  scanCode: jest.fn(),
  showToast: jest.fn(),
  showModal: jest.fn(),
  navigateTo: jest.fn(),
  switchTab: jest.fn(),
  redirectTo: jest.fn(),
  navigateBack: jest.fn(),
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  request: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  stopPullDownRefresh: jest.fn(),
  makePhoneCall: jest.fn(),
  getSystemInfoSync: jest.fn(() => ({
    platform: 'devtools',
    system: 'iOS 14.0',
    version: '8.0.5'
  }))
}

// Mock @tarojs/taro
jest.mock('@tarojs/taro', () => mockTaro)

// Mock process.env
process.env.TARO_ENV = 'weapp'
process.env.API_BASE_URL = 'http://localhost:8080/api/v1'

// Global test utilities
global.mockTaro = mockTaro

// Setup global test environment
beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks()
  
  // Reset localStorage
  localStorage.clear()
  
  // Reset console methods
  jest.spyOn(console, 'error').mockImplementation(() => {})
  jest.spyOn(console, 'warn').mockImplementation(() => {})
})

afterEach(() => {
  // Restore console methods
  jest.restoreAllMocks()
})
