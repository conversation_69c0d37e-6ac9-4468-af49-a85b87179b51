// 基础类型定义
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationResponse<T = any> {
  page: number
  page_size: number
  total: number
  total_pages: number
  data: T[]
}

// 用户相关类型
export interface User {
  id: number
  openid: string
  nickname: string
  avatar_url: string
  phone: string
  balance: number
  created_at: string
  updated_at: string
}

export interface UserLoginRequest {
  openid: string
  platform?: 'wechat' | 'alipay'
}

export interface UserRegisterRequest {
  openid: string
  nickname: string
  avatar_url: string
  phone?: string
  platform?: 'wechat' | 'alipay'
}

export interface UserUpdateRequest {
  nickname?: string
  avatar_url?: string
  phone?: string
}

export interface UserRechargeRequest {
  amount: number
  payment_method: 'wechat' | 'alipay' | 'balance'
}

// 房间相关类型
export interface Room {
  id: number
  room_number: string
  name: string
  description: string
  status: 'available' | 'occupied' | 'maintenance'
  pricing_rule_id: number
  price_per_hour: number
  created_at: string
  updated_at: string
}

export interface RoomWithDevices extends Room {
  devices: Device[]
}

// 设备相关类型
export interface Device {
  id: number
  type: 'main_lock' | 'room_lock' | 'power' | 'speaker' | 'sensor'
  room_id: number
  mac_address: string
  status: 'online' | 'offline' | 'maintenance'
  last_heartbeat: string
  installed_at: string
}

// 订单相关类型
export interface Order {
  id: number
  user_id: number
  room_id: number
  room_number: string
  room_name: string
  start_time: string
  end_time?: string
  total_amount: number
  paid_amount: number
  status: 'pending' | 'paid' | 'completed' | 'cancelled'
  package_type: 'hourly' | 'package'
  user_package_id?: number
  package_hours_used?: number
  payment_method: 'wechat' | 'alipay' | 'package' | 'platform_voucher'
  created_at: string
  updated_at: string
}

export interface OrderCreateRequest {
  room_id: number
  start_time: string
  total_amount: number
  package_type?: 'hourly' | 'package'
  user_package_id?: number
}

export interface OrderPaymentRequest {
  payment_method: 'wechat' | 'alipay' | 'balance'
  amount: number
}

export interface OrderExtendRequest {
  extend_hours: number
  amount: number
}

// 套餐相关类型
export interface Package {
  id: number
  name: string
  type: 'fixed_duration' | 'flexible_recharge'
  duration_hours?: number
  original_price: number
  sale_price: number
  discount_rate: number
  description: string
  features: string
  is_active: boolean
  sort_order: number
  valid_days: number
  min_recharge_hours?: number
  max_recharge_hours?: number
  created_at: string
  updated_at: string
}

export interface UserPackage {
  id: number
  user_id: number
  package_id: number
  package_name: string
  total_hours: number
  used_hours: number
  remaining_hours: number
  purchase_price: number
  status: 'active' | 'expired' | 'used_up' | 'refunded'
  expires_at: string
  activated_at?: string
  created_at: string
  updated_at: string
}

// 预约相关类型
export interface Reservation {
  id: number
  user_id: number
  room_id: number
  room_number: string
  room_name: string
  start_time: string
  end_time: string
  status: 'confirmed' | 'cancelled' | 'completed'
  created_at: string
}

export interface ReservationCreateRequest {
  room_id: number
  start_time: string
  end_time: string
}

// 平台适配器类型
export interface PlatformAdapter {
  login(): Promise<LoginResult>
  getUserInfo(): Promise<UserInfo>
  requestPayment(params: PaymentParams): Promise<PaymentResult>
  scanCode(): Promise<ScanResult>
  subscribeMessage(templateId: string): Promise<boolean>
  setStorage(key: string, data: any): void
  getStorage(key: string): any
  removeStorage(key: string): void
}

export interface LoginResult {
  code: string
  platform: 'wechat' | 'alipay'
}

export interface UserInfo {
  nickName: string
  avatarUrl: string
  gender?: number
  country?: string
  province?: string
  city?: string
  language?: string
}

export interface PaymentParams {
  timeStamp: string
  nonceStr: string
  package: string
  signType: string
  paySign: string
}

export interface PaymentResult {
  errMsg: string
}

export interface ScanResult {
  result: string
  scanType: string
  charSet: string
  path: string
}

// 系统配置类型
export interface SystemConfig {
  default_pricing_rule_id: number
  heartbeat_timeout: number
  order_timeout: number
  business_hours: string
  contact_phone: string
  notification_enabled: boolean
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
}

// 平台类型
export type Platform = 'wechat' | 'alipay'

// 支付方式类型
export type PaymentMethod = 'wechat' | 'alipay' | 'balance' | 'package'

// 房间状态类型
export type RoomStatus = 'available' | 'occupied' | 'maintenance'

// 订单状态类型
export type OrderStatus = 'pending' | 'paid' | 'completed' | 'cancelled'

// 设备状态类型
export type DeviceStatus = 'online' | 'offline' | 'maintenance'

// 设备类型
export type DeviceType = 'main_lock' | 'room_lock' | 'power' | 'speaker' | 'sensor'
