// 混入样式
@import './variables.scss';

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本溢出省略号
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
  }
}

// 居中对齐
@mixin center($position: absolute) {
  position: $position;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 水平居中
@mixin center-x($position: absolute) {
  position: $position;
  left: 50%;
  transform: translateX(-50%);
}

// 垂直居中
@mixin center-y($position: absolute) {
  position: $position;
  top: 50%;
  transform: translateY(-50%);
}

// Flex布局
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// Flex居中
@mixin flex-center {
  @include flex(row, center, center);
}

// Flex两端对齐
@mixin flex-between {
  @include flex(row, space-between, center);
}

// Flex环绕对齐
@mixin flex-around {
  @include flex(row, space-around, center);
}

// 按钮样式
@mixin button($bg-color: $primary-color, $text-color: $white, $border-color: $bg-color) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-base;
  border: $border-width $border-style $border-color;
  border-radius: $border-radius-base;
  background-color: $bg-color;
  color: $text-color;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  text-align: center;
  cursor: pointer;
  transition: all $transition-duration-base $transition-timing-function;
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card($padding: $spacing-base, $radius: $border-radius-base, $shadow: $box-shadow-base) {
  padding: $padding;
  background-color: $white;
  border-radius: $radius;
  box-shadow: $shadow;
}

// 输入框样式
@mixin input($height: 80px) {
  width: 100%;
  height: $height;
  padding: 0 $spacing-base;
  border: $border;
  border-radius: $border-radius-base;
  background-color: $white;
  font-size: $font-size-base;
  color: $text-primary;
  
  &::placeholder {
    color: $text-placeholder;
  }
  
  &:focus {
    border-color: $primary-color;
    outline: none;
  }
  
  &:disabled {
    background-color: $background-color;
    color: $text-placeholder;
    cursor: not-allowed;
  }
}

// 标签样式
@mixin tag($bg-color: $background-color, $text-color: $text-primary, $border-color: $border-color) {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border: $border-width $border-style $border-color;
  border-radius: $border-radius-sm;
  background-color: $bg-color;
  color: $text-color;
  font-size: $font-size-sm;
  line-height: 1;
}

// 徽章样式
@mixin badge($bg-color: $error-color, $text-color: $white, $size: 16px) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: $size;
  height: $size;
  padding: 0 4px;
  border-radius: $size / 2;
  background-color: $bg-color;
  color: $text-color;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  line-height: 1;
}

// 分割线
@mixin divider($color: $border-color, $thickness: 1px, $margin: $spacing-base) {
  margin: $margin 0;
  border: none;
  border-top: $thickness solid $color;
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: $breakpoint-xs) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}

// 动画
@mixin animation($name, $duration: $transition-duration-base, $timing-function: $transition-timing-function, $delay: 0s, $iteration-count: 1, $direction: normal, $fill-mode: both) {
  animation: $name $duration $timing-function $delay $iteration-count $direction $fill-mode;
}

// 过渡动画
@mixin transition($property: all, $duration: $transition-duration-base, $timing-function: $transition-timing-function, $delay: 0s) {
  transition: $property $duration $timing-function $delay;
}

// 阴影
@mixin shadow($level: base) {
  @if $level == sm {
    box-shadow: $box-shadow-sm;
  } @else if $level == base {
    box-shadow: $box-shadow-base;
  } @else if $level == lg {
    box-shadow: $box-shadow-lg;
  } @else if $level == xl {
    box-shadow: $box-shadow-xl;
  }
}

// 渐变背景
@mixin gradient($start-color, $end-color, $direction: to bottom) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 固定宽高比
@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: ($height / $width) * 100%;
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 隐藏滚动条
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 自定义滚动条
@mixin custom-scrollbar($width: 6px, $track-color: $background-color, $thumb-color: $border-color) {
  &::-webkit-scrollbar {
    width: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: darken($thumb-color, 10%);
  }
}
