// 颜色变量
$primary-color: #1E88E5;
$primary-light: #42A5F5;
$primary-dark: #1565C0;

$success-color: #52C41A;
$warning-color: #FAAD14;
$error-color: #FF4D4F;
$info-color: #1890FF;

$text-primary: #333333;
$text-secondary: #666666;
$text-placeholder: #999999;

$border-color: #E8E8E8;
$background-color: #F5F5F5;
$white: #FFFFFF;

// 字体大小
$font-size-xs: 20px;
$font-size-sm: 24px;
$font-size-base: 28px;
$font-size-lg: 32px;
$font-size-xl: 36px;
$font-size-xxl: 40px;

// 字体粗细
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 600;

// 间距
$spacing-xs: 8px;
$spacing-sm: 16px;
$spacing-base: 24px;
$spacing-lg: 32px;
$spacing-xl: 48px;

// 圆角
$border-radius-sm: 4px;
$border-radius-base: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-round: 50px;

// 阴影
$box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 4px 8px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
$box-shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.1);

// 边框
$border-width: 1px;
$border-style: solid;
$border: $border-width $border-style $border-color;

// 动画时间
$transition-duration-fast: 0.2s;
$transition-duration-base: 0.3s;
$transition-duration-slow: 0.5s;

// 动画函数
$transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// 容器最大宽度
$container-max-width: 750px;

// 房间状态颜色
$room-available-color: $success-color;
$room-occupied-color: $warning-color;
$room-maintenance-color: $error-color;

// 订单状态颜色
$order-pending-color: $warning-color;
$order-paid-color: $info-color;
$order-completed-color: $success-color;
$order-cancelled-color: $error-color;

// 支付方式颜色
$payment-wechat-color: #07C160;
$payment-alipay-color: #1677FF;
$payment-balance-color: $primary-color;
$payment-package-color: $warning-color;
