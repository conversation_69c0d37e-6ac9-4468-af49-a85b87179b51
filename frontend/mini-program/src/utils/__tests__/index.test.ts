import {
  formatAmount,
  formatTime,
  getTimeDiff,
  formatDuration,
  validatePhone,
  validateEmail,
  generateRandomString,
  debounce,
  throttle,
  deepClone,
  isEmpty,
  unique
} from '../index'

describe('工具函数测试', () => {
  describe('formatAmount', () => {
    test('格式化金额（分转元）', () => {
      expect(formatAmount(1000)).toBe('¥10.00')
      expect(formatAmount(1234)).toBe('¥12.34')
      expect(formatAmount(0)).toBe('¥0.00')
    })

    test('不显示货币符号', () => {
      expect(formatAmount(1000, false)).toBe('10.00')
      expect(formatAmount(1234, false)).toBe('12.34')
    })
  })

  describe('formatTime', () => {
    test('格式化时间', () => {
      const timestamp = new Date('2025-01-27T10:30:00Z').getTime()
      expect(formatTime(timestamp, 'YYYY-MM-DD')).toBe('2025-01-27')
      expect(formatTime(timestamp, 'HH:mm')).toBe('10:30')
    })

    test('格式化时间字符串', () => {
      const timeString = '2025-01-27T10:30:00Z'
      expect(formatTime(timeString, 'YYYY-MM-DD HH:mm')).toBe('2025-01-27 10:30')
    })
  })

  describe('getTimeDiff', () => {
    test('计算时间差', () => {
      const startTime = new Date('2025-01-27T10:00:00Z')
      const endTime = new Date('2025-01-27T12:30:00Z')
      
      const diff = getTimeDiff(startTime, endTime)
      
      expect(diff.hours).toBe(2)
      expect(diff.minutes).toBe(150)
      expect(diff.text).toBe('2小时30分钟')
    })

    test('计算到当前时间的差值', () => {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      const diff = getTimeDiff(oneHourAgo)
      
      expect(diff.hours).toBeGreaterThanOrEqual(0)
      expect(diff.minutes).toBeGreaterThanOrEqual(59)
    })
  })

  describe('formatDuration', () => {
    test('格式化持续时间', () => {
      expect(formatDuration(3661)).toBe('1小时1分钟')
      expect(formatDuration(3600)).toBe('1小时0分钟')
      expect(formatDuration(61)).toBe('1分钟1秒')
      expect(formatDuration(30)).toBe('30秒')
    })
  })

  describe('validatePhone', () => {
    test('有效手机号', () => {
      expect(validatePhone('13800138000')).toBe(true)
      expect(validatePhone('15912345678')).toBe(true)
      expect(validatePhone('18888888888')).toBe(true)
    })

    test('无效手机号', () => {
      expect(validatePhone('12345678901')).toBe(false)
      expect(validatePhone('1380013800')).toBe(false)
      expect(validatePhone('138001380000')).toBe(false)
      expect(validatePhone('abc')).toBe(false)
    })
  })

  describe('validateEmail', () => {
    test('有效邮箱', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
    })

    test('无效邮箱', () => {
      expect(validateEmail('invalid-email')).toBe(false)
      expect(validateEmail('test@')).toBe(false)
      expect(validateEmail('@example.com')).toBe(false)
    })
  })

  describe('generateRandomString', () => {
    test('生成指定长度的随机字符串', () => {
      const str1 = generateRandomString(8)
      const str2 = generateRandomString(8)
      
      expect(str1).toHaveLength(8)
      expect(str2).toHaveLength(8)
      expect(str1).not.toBe(str2)
    })

    test('默认长度为8', () => {
      const str = generateRandomString()
      expect(str).toHaveLength(8)
    })
  })

  describe('debounce', () => {
    test('防抖函数', (done) => {
      const mockFn = jest.fn()
      const debouncedFn = debounce(mockFn, 100)
      
      debouncedFn()
      debouncedFn()
      debouncedFn()
      
      expect(mockFn).not.toHaveBeenCalled()
      
      setTimeout(() => {
        expect(mockFn).toHaveBeenCalledTimes(1)
        done()
      }, 150)
    })
  })

  describe('throttle', () => {
    test('节流函数', (done) => {
      const mockFn = jest.fn()
      const throttledFn = throttle(mockFn, 100)
      
      throttledFn()
      throttledFn()
      throttledFn()
      
      expect(mockFn).toHaveBeenCalledTimes(1)
      
      setTimeout(() => {
        throttledFn()
        expect(mockFn).toHaveBeenCalledTimes(2)
        done()
      }, 150)
    })
  })

  describe('deepClone', () => {
    test('深拷贝对象', () => {
      const original = {
        name: 'test',
        nested: {
          value: 123,
          array: [1, 2, 3]
        }
      }
      
      const cloned = deepClone(original)
      
      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.nested).not.toBe(original.nested)
      expect(cloned.nested.array).not.toBe(original.nested.array)
    })

    test('深拷贝数组', () => {
      const original = [1, { a: 2 }, [3, 4]]
      const cloned = deepClone(original)
      
      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned[1]).not.toBe(original[1])
    })
  })

  describe('isEmpty', () => {
    test('空值检查', () => {
      expect(isEmpty(null)).toBe(true)
      expect(isEmpty(undefined)).toBe(true)
      expect(isEmpty('')).toBe(true)
      expect(isEmpty('   ')).toBe(true)
      expect(isEmpty([])).toBe(true)
      expect(isEmpty({})).toBe(true)
      
      expect(isEmpty('test')).toBe(false)
      expect(isEmpty([1])).toBe(false)
      expect(isEmpty({ a: 1 })).toBe(false)
      expect(isEmpty(0)).toBe(false)
    })
  })

  describe('unique', () => {
    test('数组去重', () => {
      expect(unique([1, 2, 2, 3, 3, 3])).toEqual([1, 2, 3])
      expect(unique(['a', 'b', 'a', 'c'])).toEqual(['a', 'b', 'c'])
    })

    test('对象数组去重', () => {
      const array = [
        { id: 1, name: 'a' },
        { id: 2, name: 'b' },
        { id: 1, name: 'c' }
      ]
      
      const result = unique(array, 'id')
      
      expect(result).toHaveLength(2)
      expect(result[0].id).toBe(1)
      expect(result[1].id).toBe(2)
    })
  })
})
