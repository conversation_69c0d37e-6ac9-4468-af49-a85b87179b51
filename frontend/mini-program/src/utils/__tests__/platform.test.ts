import { 
  getCurrentPlatform, 
  isWeapp, 
  isAlipay, 
  getPlatformConfig,
  getPlatformStorageKey,
  showPlatformToast
} from '../platform'

describe('平台工具测试', () => {
  beforeEach(() => {
    // 重置环境变量
    delete process.env.TARO_ENV
  })

  describe('getCurrentPlatform', () => {
    test('微信平台', () => {
      process.env.TARO_ENV = 'weapp'
      expect(getCurrentPlatform()).toBe('wechat')
    })

    test('支付宝平台', () => {
      process.env.TARO_ENV = 'alipay'
      expect(getCurrentPlatform()).toBe('alipay')
    })

    test('默认平台', () => {
      process.env.TARO_ENV = 'unknown'
      expect(getCurrentPlatform()).toBe('wechat')
    })
  })

  describe('isWeapp', () => {
    test('微信环境返回true', () => {
      process.env.TARO_ENV = 'weapp'
      expect(isWeapp()).toBe(true)
    })

    test('非微信环境返回false', () => {
      process.env.TARO_ENV = 'alipay'
      expect(isWeapp()).toBe(false)
    })
  })

  describe('isAlipay', () => {
    test('支付宝环境返回true', () => {
      process.env.TARO_ENV = 'alipay'
      expect(isAlipay()).toBe(true)
    })

    test('非支付宝环境返回false', () => {
      process.env.TARO_ENV = 'weapp'
      expect(isAlipay()).toBe(false)
    })
  })

  describe('getPlatformConfig', () => {
    test('微信平台配置', () => {
      process.env.TARO_ENV = 'weapp'
      const config = getPlatformConfig()
      
      expect(config.name).toBe('微信小程序')
      expect(config.paymentMethod).toBe('wechat')
      expect(config.storagePrefix).toBe('wx_')
    })

    test('支付宝平台配置', () => {
      process.env.TARO_ENV = 'alipay'
      const config = getPlatformConfig()
      
      expect(config.name).toBe('支付宝小程序')
      expect(config.paymentMethod).toBe('alipay')
      expect(config.storagePrefix).toBe('alipay_')
    })
  })

  describe('getPlatformStorageKey', () => {
    test('微信平台存储键名', () => {
      process.env.TARO_ENV = 'weapp'
      const key = getPlatformStorageKey('userInfo')
      expect(key).toBe('wx_userInfo')
    })

    test('支付宝平台存储键名', () => {
      process.env.TARO_ENV = 'alipay'
      const key = getPlatformStorageKey('userInfo')
      expect(key).toBe('alipay_userInfo')
    })
  })

  describe('showPlatformToast', () => {
    test('调用Taro.showToast', () => {
      const mockShowToast = jest.fn()
      jest.doMock('@tarojs/taro', () => ({
        showToast: mockShowToast
      }))

      showPlatformToast('测试消息', 'success')
      
      expect(mockShowToast).toHaveBeenCalledWith({
        title: '测试消息',
        icon: 'success',
        duration: 2000
      })
    })
  })
})
