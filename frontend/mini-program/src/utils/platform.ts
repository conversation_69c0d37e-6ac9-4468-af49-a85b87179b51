import Taro from '@tarojs/taro'
import type { Platform } from '@/types'

/**
 * 获取当前运行平台
 */
export const getCurrentPlatform = (): Platform => {
  const env = process.env.TARO_ENV
  if (env === 'weapp') {
    return 'wechat'
  } else if (env === 'alipay') {
    return 'alipay'
  }
  // 默认返回微信平台
  return 'wechat'
}

/**
 * 判断是否为微信小程序
 */
export const isWeapp = (): boolean => {
  return process.env.TARO_ENV === 'weapp'
}

/**
 * 判断是否为支付宝小程序
 */
export const isAlipay = (): boolean => {
  return process.env.TARO_ENV === 'alipay'
}

/**
 * 获取平台特定配置
 */
export const getPlatformConfig = () => {
  const platform = getCurrentPlatform()
  
  const configs = {
    wechat: {
      appId: 'wx_app_id', // 微信小程序 AppID
      apiBase: process.env.API_BASE_URL || 'http://localhost:8080/api/v1',
      name: '微信小程序',
      paymentMethod: 'wechat' as const,
      loginMethod: 'wechat' as const,
      scanCodeApi: 'scanCode',
      paymentApi: 'requestPayment',
      storagePrefix: 'wx_'
    },
    alipay: {
      appId: 'alipay_app_id', // 支付宝小程序 AppID
      apiBase: process.env.API_BASE_URL || 'http://localhost:8080/api/v1',
      name: '支付宝小程序',
      paymentMethod: 'alipay' as const,
      loginMethod: 'alipay' as const,
      scanCodeApi: 'scan',
      paymentApi: 'tradePay',
      storagePrefix: 'alipay_'
    }
  }
  
  return configs[platform]
}

/**
 * 获取平台特定的存储键名
 */
export const getPlatformStorageKey = (key: string): string => {
  const config = getPlatformConfig()
  return `${config.storagePrefix}${key}`
}

/**
 * 显示平台特定的提示信息
 */
export const showPlatformToast = (title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none') => {
  return Taro.showToast({
    title,
    icon,
    duration: 2000
  })
}

/**
 * 显示平台特定的模态框
 */
export const showPlatformModal = (options: {
  title: string
  content: string
  showCancel?: boolean
  cancelText?: string
  confirmText?: string
}) => {
  const platform = getCurrentPlatform()
  
  if (platform === 'alipay') {
    // 支付宝小程序的模态框API略有不同
    return Taro.showModal({
      title: options.title,
      content: options.content,
      showCancel: options.showCancel ?? true,
      cancelText: options.cancelText || '取消',
      confirmText: options.confirmText || '确定'
    })
  } else {
    // 微信小程序
    return Taro.showModal({
      title: options.title,
      content: options.content,
      showCancel: options.showCancel ?? true,
      cancelText: options.cancelText || '取消',
      confirmText: options.confirmText || '确定'
    })
  }
}

/**
 * 获取平台特定的导航栏配置
 */
export const getPlatformNavigationBarConfig = () => {
  const platform = getCurrentPlatform()
  
  const configs = {
    wechat: {
      navigationBarTitleText: '自助麻将室',
      navigationBarBackgroundColor: '#1E88E5',
      navigationBarTextStyle: 'white' as const
    },
    alipay: {
      navigationBarTitleText: '自助麻将室',
      navigationBarBackgroundColor: '#1E88E5',
      navigationBarTextStyle: 'white' as const
    }
  }
  
  return configs[platform]
}

/**
 * 获取平台特定的TabBar配置
 */
export const getPlatformTabBarConfig = () => {
  const platform = getCurrentPlatform()
  
  const baseConfig = {
    color: '#999999',
    selectedColor: '#1E88E5',
    backgroundColor: '#ffffff',
    borderStyle: 'black' as const,
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: 'assets/icons/home.png',
        selectedIconPath: 'assets/icons/home-active.png'
      },
      {
        pagePath: 'pages/order/index',
        text: '订单',
        iconPath: 'assets/icons/order.png',
        selectedIconPath: 'assets/icons/order-active.png'
      },
      {
        pagePath: 'pages/reservation/index',
        text: '预约',
        iconPath: 'assets/icons/calendar.png',
        selectedIconPath: 'assets/icons/calendar-active.png'
      },
      {
        pagePath: 'pages/profile/index',
        text: '我的',
        iconPath: 'assets/icons/profile.png',
        selectedIconPath: 'assets/icons/profile-active.png'
      }
    ]
  }
  
  // 不同平台可以有不同的TabBar配置
  if (platform === 'alipay') {
    // 支付宝小程序特定配置
    return {
      ...baseConfig,
      // 可以添加支付宝特定的配置
    }
  }
  
  return baseConfig
}

/**
 * 平台特定的页面跳转
 */
export const navigateTo = (url: string) => {
  return Taro.navigateTo({ url })
}

export const redirectTo = (url: string) => {
  return Taro.redirectTo({ url })
}

export const switchTab = (url: string) => {
  return Taro.switchTab({ url })
}

export const navigateBack = (delta: number = 1) => {
  return Taro.navigateBack({ delta })
}

/**
 * 平台特定的分享配置
 */
export const getPlatformShareConfig = () => {
  const platform = getCurrentPlatform()
  
  const configs = {
    wechat: {
      title: '自助麻将室 - 便捷的麻将室预订服务',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-logo.png'
    },
    alipay: {
      title: '自助麻将室 - 便捷的麻将室预订服务',
      desc: '扫码开台，智能计费，轻松享受麻将时光',
      path: '/pages/index/index',
      bgImgUrl: '/assets/images/share-bg.png'
    }
  }
  
  return configs[platform]
}
