import Taro from '@tarojs/taro'
import type { ApiResponse, ApiError } from '@/types'
import { getPlatformConfig, showPlatformToast } from './platform'
import { platformAdapter } from './adapter'

// 请求配置接口
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  timeout?: number
  showLoading?: boolean
  showError?: boolean
}

// 请求拦截器类型
type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
type ResponseInterceptor = (response: any) => any | Promise<any>
type ErrorInterceptor = (error: any) => any | Promise<any>

/**
 * HTTP请求工具类
 */
class HttpClient {
  private baseURL: string
  private timeout: number
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private errorInterceptors: ErrorInterceptor[] = []

  constructor() {
    const config = getPlatformConfig()
    this.baseURL = config.apiBase
    this.timeout = 10000
    this.setupDefaultInterceptors()
  }

  /**
   * 设置默认拦截器
   */
  private setupDefaultInterceptors() {
    // 请求拦截器 - 添加认证信息
    this.addRequestInterceptor((config) => {
      const token = platformAdapter.getStorage('token')
      const userId = platformAdapter.getStorage('userId')
      
      if (!config.header) {
        config.header = {}
      }
      
      if (token) {
        config.header['Authorization'] = `Bearer ${token}`
      }
      
      if (userId) {
        config.header['X-User-ID'] = userId.toString()
      }
      
      config.header['Content-Type'] = 'application/json'
      
      return config
    })

    // 响应拦截器 - 处理通用响应
    this.addResponseInterceptor((response) => {
      const { data } = response
      
      // 检查业务状态码
      if (data.code === 200) {
        return data.data
      } else if (data.code === 401) {
        // 未授权，清除本地存储并跳转到登录页
        this.clearAuthInfo()
        Taro.redirectTo({ url: '/pages/login/index' })
        throw new Error('用户未登录')
      } else {
        throw new Error(data.message || '请求失败')
      }
    })

    // 错误拦截器 - 处理网络错误
    this.addErrorInterceptor((error) => {
      console.error('请求错误:', error)
      
      let message = '网络请求失败'
      
      if (error.errMsg) {
        if (error.errMsg.includes('timeout')) {
          message = '请求超时，请检查网络连接'
        } else if (error.errMsg.includes('fail')) {
          message = '网络连接失败，请检查网络设置'
        }
      }
      
      showPlatformToast(message, 'error')
      throw new Error(message)
    })
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor) {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor) {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: ErrorInterceptor) {
    this.errorInterceptors.push(interceptor)
  }

  /**
   * 清除认证信息
   */
  private clearAuthInfo() {
    platformAdapter.removeStorage('token')
    platformAdapter.removeStorage('userId')
    platformAdapter.removeStorage('userInfo')
  }

  /**
   * 执行请求拦截器
   */
  private async executeRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
    let result = config
    for (const interceptor of this.requestInterceptors) {
      result = await interceptor(result)
    }
    return result
  }

  /**
   * 执行响应拦截器
   */
  private async executeResponseInterceptors(response: any): Promise<any> {
    let result = response
    for (const interceptor of this.responseInterceptors) {
      result = await interceptor(result)
    }
    return result
  }

  /**
   * 执行错误拦截器
   */
  private async executeErrorInterceptors(error: any): Promise<any> {
    let result = error
    for (const interceptor of this.errorInterceptors) {
      result = await interceptor(result)
    }
    return result
  }

  /**
   * 发送HTTP请求
   */
  async request<T = any>(config: RequestConfig): Promise<T> {
    try {
      // 显示加载提示
      if (config.showLoading !== false) {
        Taro.showLoading({ title: '加载中...' })
      }

      // 执行请求拦截器
      const finalConfig = await this.executeRequestInterceptors(config)

      // 构建完整URL
      const url = finalConfig.url.startsWith('http') 
        ? finalConfig.url 
        : `${this.baseURL}${finalConfig.url}`

      // 发送请求
      const response = await Taro.request({
        url,
        method: finalConfig.method || 'GET',
        data: finalConfig.data,
        header: finalConfig.header,
        timeout: finalConfig.timeout || this.timeout
      })

      // 隐藏加载提示
      if (config.showLoading !== false) {
        Taro.hideLoading()
      }

      // 执行响应拦截器
      return await this.executeResponseInterceptors(response)
    } catch (error) {
      // 隐藏加载提示
      if (config.showLoading !== false) {
        Taro.hideLoading()
      }

      // 执行错误拦截器
      await this.executeErrorInterceptors(error)
      throw error
    }
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'GET',
      data: params,
      ...config
    })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'DELETE',
      data: params,
      ...config
    })
  }
}

// 创建HTTP客户端实例
export const http = new HttpClient()

// 导出类型
export type { RequestConfig, ApiResponse, ApiError }
