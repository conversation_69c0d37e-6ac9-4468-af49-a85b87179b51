/**
 * 应用常量定义
 */

// 房间状态
export const ROOM_STATUS = {
  AVAILABLE: 'available',
  OCCUPIED: 'occupied',
  MAINTENANCE: 'maintenance'
} as const

export const ROOM_STATUS_TEXT = {
  [ROOM_STATUS.AVAILABLE]: '空闲',
  [ROOM_STATUS.OCCUPIED]: '使用中',
  [ROOM_STATUS.MAINTENANCE]: '维护中'
} as const

export const ROOM_STATUS_COLOR = {
  [ROOM_STATUS.AVAILABLE]: '#52C41A',
  [ROOM_STATUS.OCCUPIED]: '#FAAD14',
  [ROOM_STATUS.MAINTENANCE]: '#FF4D4F'
} as const

// 订单状态
export const ORDER_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const

export const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.PENDING]: '待支付',
  [ORDER_STATUS.PAID]: '使用中',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.CANCELLED]: '已取消'
} as const

export const ORDER_STATUS_COLOR = {
  [ORDER_STATUS.PENDING]: '#FAAD14',
  [ORDER_STATUS.PAID]: '#1890FF',
  [ORDER_STATUS.COMPLETED]: '#52C41A',
  [ORDER_STATUS.CANCELLED]: '#FF4D4F'
} as const

// 支付方式
export const PAYMENT_METHOD = {
  WECHAT: 'wechat',
  ALIPAY: 'alipay',
  BALANCE: 'balance',
  PACKAGE: 'package'
} as const

export const PAYMENT_METHOD_TEXT = {
  [PAYMENT_METHOD.WECHAT]: '微信支付',
  [PAYMENT_METHOD.ALIPAY]: '支付宝支付',
  [PAYMENT_METHOD.BALANCE]: '余额支付',
  [PAYMENT_METHOD.PACKAGE]: '套餐抵扣'
} as const

// 设备类型
export const DEVICE_TYPE = {
  MAIN_LOCK: 'main_lock',
  ROOM_LOCK: 'room_lock',
  POWER: 'power',
  SPEAKER: 'speaker',
  SENSOR: 'sensor'
} as const

export const DEVICE_TYPE_TEXT = {
  [DEVICE_TYPE.MAIN_LOCK]: '主门锁',
  [DEVICE_TYPE.ROOM_LOCK]: '房间门锁',
  [DEVICE_TYPE.POWER]: '电源控制',
  [DEVICE_TYPE.SPEAKER]: '音响设备',
  [DEVICE_TYPE.SENSOR]: '环境传感器'
} as const

// 设备状态
export const DEVICE_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  MAINTENANCE: 'maintenance'
} as const

export const DEVICE_STATUS_TEXT = {
  [DEVICE_STATUS.ONLINE]: '在线',
  [DEVICE_STATUS.OFFLINE]: '离线',
  [DEVICE_STATUS.MAINTENANCE]: '维护中'
} as const

export const DEVICE_STATUS_COLOR = {
  [DEVICE_STATUS.ONLINE]: '#52C41A',
  [DEVICE_STATUS.OFFLINE]: '#FF4D4F',
  [DEVICE_STATUS.MAINTENANCE]: '#FAAD14'
} as const

// 套餐类型
export const PACKAGE_TYPE = {
  FIXED_DURATION: 'fixed_duration',
  FLEXIBLE_RECHARGE: 'flexible_recharge'
} as const

export const PACKAGE_TYPE_TEXT = {
  [PACKAGE_TYPE.FIXED_DURATION]: '固定时长套餐',
  [PACKAGE_TYPE.FLEXIBLE_RECHARGE]: '灵活充值套餐'
} as const

// 套餐状态
export const PACKAGE_STATUS = {
  ACTIVE: 'active',
  EXPIRED: 'expired',
  USED_UP: 'used_up',
  REFUNDED: 'refunded'
} as const

export const PACKAGE_STATUS_TEXT = {
  [PACKAGE_STATUS.ACTIVE]: '可用',
  [PACKAGE_STATUS.EXPIRED]: '已过期',
  [PACKAGE_STATUS.USED_UP]: '已用完',
  [PACKAGE_STATUS.REFUNDED]: '已退款'
} as const

export const PACKAGE_STATUS_COLOR = {
  [PACKAGE_STATUS.ACTIVE]: '#52C41A',
  [PACKAGE_STATUS.EXPIRED]: '#FF4D4F',
  [PACKAGE_STATUS.USED_UP]: '#999999',
  [PACKAGE_STATUS.REFUNDED]: '#FAAD14'
} as const

// 预约状态
export const RESERVATION_STATUS = {
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed'
} as const

export const RESERVATION_STATUS_TEXT = {
  [RESERVATION_STATUS.CONFIRMED]: '已确认',
  [RESERVATION_STATUS.CANCELLED]: '已取消',
  [RESERVATION_STATUS.COMPLETED]: '已完成'
} as const

export const RESERVATION_STATUS_COLOR = {
  [RESERVATION_STATUS.CONFIRMED]: '#1890FF',
  [RESERVATION_STATUS.CANCELLED]: '#FF4D4F',
  [RESERVATION_STATUS.COMPLETED]: '#52C41A'
} as const

// API错误码
export const API_CODE = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500
} as const

// 业务错误码
export const BUSINESS_CODE = {
  VALIDATION_ERROR: 1001,
  BUSINESS_ERROR: 1002,
  UNAUTHORIZED: 1003,
  FORBIDDEN: 1004,
  NOT_FOUND: 1005
} as const

// 时间格式
export const DATE_FORMAT = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  MONTH_DAY: 'MM-DD',
  HOUR_MINUTE: 'HH:mm'
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100
} as const

// 缓存时间（毫秒）
export const CACHE_TIME = {
  SHORT: 5 * 60 * 1000,      // 5分钟
  MEDIUM: 30 * 60 * 1000,    // 30分钟
  LONG: 2 * 60 * 60 * 1000,  // 2小时
  DAY: 24 * 60 * 60 * 1000   // 24小时
} as const

// 正则表达式
export const REGEX = {
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
} as const

// 图片配置
export const IMAGE = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  QUALITY: 0.8,
  MAX_WIDTH: 1200,
  MAX_HEIGHT: 1200
} as const

// 主题色彩
export const COLORS = {
  PRIMARY: '#1E88E5',
  PRIMARY_LIGHT: '#42A5F5',
  PRIMARY_DARK: '#1565C0',
  SUCCESS: '#52C41A',
  WARNING: '#FAAD14',
  ERROR: '#FF4D4F',
  INFO: '#1890FF',
  TEXT_PRIMARY: '#333333',
  TEXT_SECONDARY: '#666666',
  TEXT_PLACEHOLDER: '#999999',
  BORDER: '#E8E8E8',
  BACKGROUND: '#F5F5F5',
  WHITE: '#FFFFFF'
} as const

// 字体大小
export const FONT_SIZE = {
  XS: 20,
  SM: 24,
  BASE: 28,
  LG: 32,
  XL: 36,
  XXL: 40
} as const

// 间距
export const SPACING = {
  XS: 8,
  SM: 16,
  BASE: 24,
  LG: 32,
  XL: 48
} as const

// 圆角
export const BORDER_RADIUS = {
  SM: 4,
  BASE: 8,
  LG: 12,
  XL: 16,
  ROUND: 50
} as const

// 阴影
export const BOX_SHADOW = {
  SM: '0 2px 4px rgba(0, 0, 0, 0.1)',
  BASE: '0 4px 8px rgba(0, 0, 0, 0.1)',
  LG: '0 8px 16px rgba(0, 0, 0, 0.1)',
  XL: '0 12px 24px rgba(0, 0, 0, 0.1)'
} as const
