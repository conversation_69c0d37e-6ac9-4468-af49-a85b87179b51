import Taro from '@tarojs/taro'
import type {
  PlatformAdapter,
  LoginResult,
  UserInfo,
  PaymentParams,
  PaymentResult,
  ScanResult
} from '@/types'
import { getCurrentPlatform, getPlatformStorageKey } from './platform'

/**
 * 平台适配器基类
 */
abstract class BasePlatformAdapter implements PlatformAdapter {
  abstract login(): Promise<LoginResult>
  abstract getUserInfo(): Promise<UserInfo>
  abstract requestPayment(params: PaymentParams): Promise<PaymentResult>
  abstract scanCode(): Promise<ScanResult>
  abstract subscribeMessage(templateId: string): Promise<boolean>

  setStorage(key: string, data: any): void {
    const platformKey = getPlatformStorageKey(key)
    Taro.setStorageSync(platformKey, data)
  }

  getStorage(key: string): any {
    const platformKey = getPlatformStorageKey(key)
    return Taro.getStorageSync(platformKey)
  }

  removeStorage(key: string): void {
    const platformKey = getPlatformStorageKey(key)
    Taro.removeStorageSync(platformKey)
  }
}

/**
 * 微信小程序适配器
 */
class WechatAdapter extends BasePlatformAdapter {
  async login(): Promise<LoginResult> {
    try {
      const result = await Taro.login()
      return {
        code: result.code,
        platform: 'wechat'
      }
    } catch (error) {
      throw new Error(`微信登录失败: ${error}`)
    }
  }

  async getUserInfo(): Promise<UserInfo> {
    try {
      const result = await Taro.getUserProfile({
        desc: '用于完善用户资料'
      })
      return result.userInfo
    } catch (error) {
      throw new Error(`获取用户信息失败: ${error}`)
    }
  }

  async requestPayment(params: PaymentParams): Promise<PaymentResult> {
    try {
      const result = await Taro.requestPayment({
        timeStamp: params.timeStamp,
        nonceStr: params.nonceStr,
        package: params.package,
        signType: params.signType,
        paySign: params.paySign
      })
      return result
    } catch (error) {
      throw new Error(`微信支付失败: ${error}`)
    }
  }

  async scanCode(): Promise<ScanResult> {
    try {
      const result = await Taro.scanCode({
        onlyFromCamera: true,
        scanType: ['qrCode']
      })
      return result
    } catch (error) {
      throw new Error(`扫码失败: ${error}`)
    }
  }

  async subscribeMessage(templateId: string): Promise<boolean> {
    try {
      const result = await Taro.requestSubscribeMessage({
        tmplIds: [templateId]
      })
      return result[templateId] === 'accept'
    } catch (error) {
      console.warn('订阅消息失败:', error)
      return false
    }
  }
}

/**
 * 支付宝小程序适配器
 */
class AlipayAdapter extends BasePlatformAdapter {
  async login(): Promise<LoginResult> {
    try {
      const result = await Taro.getAuthCode({
        scopes: ['auth_user']
      })
      return {
        code: result.authCode,
        platform: 'alipay'
      }
    } catch (error) {
      throw new Error(`支付宝登录失败: ${error}`)
    }
  }

  async getUserInfo(): Promise<UserInfo> {
    try {
      const result = await Taro.getOpenUserInfo()
      return {
        nickName: result.nickName,
        avatarUrl: result.avatar,
        gender: result.gender === 'm' ? 1 : result.gender === 'f' ? 2 : 0,
        country: result.country,
        province: result.province,
        city: result.city
      }
    } catch (error) {
      throw new Error(`获取用户信息失败: ${error}`)
    }
  }

  async requestPayment(params: PaymentParams): Promise<PaymentResult> {
    try {
      const result = await Taro.tradePay({
        orderStr: params.package // 支付宝使用orderStr参数
      })
      return {
        errMsg: result.resultCode === '9000' ? 'requestPayment:ok' : 'requestPayment:fail'
      }
    } catch (error) {
      throw new Error(`支付宝支付失败: ${error}`)
    }
  }

  async scanCode(): Promise<ScanResult> {
    try {
      const result = await Taro.scan({
        type: 'qr'
      })
      return {
        result: result.code,
        scanType: result.qrCode ? 'QR_CODE' : 'other',
        charSet: 'utf8',
        path: result.code
      }
    } catch (error) {
      throw new Error(`扫码失败: ${error}`)
    }
  }

  async subscribeMessage(templateId: string): Promise<boolean> {
    try {
      // 支付宝小程序的消息订阅方式
      const result = await Taro.requestSubscribeMessage({
        entityIds: [templateId]
      })
      return result[templateId] === 'accept'
    } catch (error) {
      console.warn('订阅消息失败:', error)
      return false
    }
  }
}

/**
 * 获取当前平台的适配器实例
 */
export const getPlatformAdapter = (): PlatformAdapter => {
  const platform = getCurrentPlatform()
  
  if (platform === 'wechat') {
    return new WechatAdapter()
  } else if (platform === 'alipay') {
    return new AlipayAdapter()
  }
  
  // 默认返回微信适配器
  return new WechatAdapter()
}

/**
 * 适配器工厂类
 */
export class AdapterFactory {
  private static instance: PlatformAdapter | null = null

  static getInstance(): PlatformAdapter {
    if (!this.instance) {
      this.instance = getPlatformAdapter()
    }
    return this.instance
  }

  static resetInstance(): void {
    this.instance = null
  }
}

// 导出适配器实例
export const platformAdapter = AdapterFactory.getInstance()
