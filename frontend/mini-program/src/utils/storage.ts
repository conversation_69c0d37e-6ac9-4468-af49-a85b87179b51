import { platformAdapter } from './adapter'
import type { User, Order, UserPackage } from '@/types'

/**
 * 存储工具类
 */
class StorageManager {
  private readonly KEYS = {
    TOKEN: 'token',
    USER_ID: 'userId',
    USER_INFO: 'userInfo',
    CURRENT_ORDER: 'currentOrder',
    USER_PACKAGES: 'userPackages',
    ROOM_CACHE: 'roomCache',
    SETTINGS: 'settings'
  } as const

  /**
   * 设置用户认证信息
   */
  setAuthInfo(token: string, userId: number, userInfo: User) {
    platformAdapter.setStorage(this.KEYS.TOKEN, token)
    platformAdapter.setStorage(this.KEYS.USER_ID, userId)
    platformAdapter.setStorage(this.KEYS.USER_INFO, userInfo)
  }

  /**
   * 获取认证token
   */
  getToken(): string | null {
    return platformAdapter.getStorage(this.KEYS.TOKEN)
  }

  /**
   * 获取用户ID
   */
  getUserId(): number | null {
    return platformAdapter.getStorage(this.KEYS.USER_ID)
  }

  /**
   * 获取用户信息
   */
  getUserInfo(): User | null {
    return platformAdapter.getStorage(this.KEYS.USER_INFO)
  }

  /**
   * 更新用户信息
   */
  updateUserInfo(userInfo: Partial<User>) {
    const currentUserInfo = this.getUserInfo()
    if (currentUserInfo) {
      const updatedUserInfo = { ...currentUserInfo, ...userInfo }
      platformAdapter.setStorage(this.KEYS.USER_INFO, updatedUserInfo)
    }
  }

  /**
   * 清除认证信息
   */
  clearAuthInfo() {
    platformAdapter.removeStorage(this.KEYS.TOKEN)
    platformAdapter.removeStorage(this.KEYS.USER_ID)
    platformAdapter.removeStorage(this.KEYS.USER_INFO)
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    const token = this.getToken()
    const userId = this.getUserId()
    return !!(token && userId)
  }

  /**
   * 设置当前订单
   */
  setCurrentOrder(order: Order | null) {
    if (order) {
      platformAdapter.setStorage(this.KEYS.CURRENT_ORDER, order)
    } else {
      platformAdapter.removeStorage(this.KEYS.CURRENT_ORDER)
    }
  }

  /**
   * 获取当前订单
   */
  getCurrentOrder(): Order | null {
    return platformAdapter.getStorage(this.KEYS.CURRENT_ORDER)
  }

  /**
   * 设置用户套餐列表
   */
  setUserPackages(packages: UserPackage[]) {
    platformAdapter.setStorage(this.KEYS.USER_PACKAGES, packages)
  }

  /**
   * 获取用户套餐列表
   */
  getUserPackages(): UserPackage[] {
    return platformAdapter.getStorage(this.KEYS.USER_PACKAGES) || []
  }

  /**
   * 设置房间缓存
   */
  setRoomCache(rooms: any[], expireTime: number = 5 * 60 * 1000) { // 默认5分钟过期
    const cacheData = {
      data: rooms,
      timestamp: Date.now(),
      expireTime
    }
    platformAdapter.setStorage(this.KEYS.ROOM_CACHE, cacheData)
  }

  /**
   * 获取房间缓存
   */
  getRoomCache(): any[] | null {
    const cacheData = platformAdapter.getStorage(this.KEYS.ROOM_CACHE)
    if (!cacheData) return null

    const { data, timestamp, expireTime } = cacheData
    const now = Date.now()

    // 检查是否过期
    if (now - timestamp > expireTime) {
      platformAdapter.removeStorage(this.KEYS.ROOM_CACHE)
      return null
    }

    return data
  }

  /**
   * 清除房间缓存
   */
  clearRoomCache() {
    platformAdapter.removeStorage(this.KEYS.ROOM_CACHE)
  }

  /**
   * 设置应用设置
   */
  setSettings(settings: Record<string, any>) {
    const currentSettings = this.getSettings()
    const updatedSettings = { ...currentSettings, ...settings }
    platformAdapter.setStorage(this.KEYS.SETTINGS, updatedSettings)
  }

  /**
   * 获取应用设置
   */
  getSettings(): Record<string, any> {
    return platformAdapter.getStorage(this.KEYS.SETTINGS) || {}
  }

  /**
   * 获取特定设置项
   */
  getSetting(key: string, defaultValue?: any): any {
    const settings = this.getSettings()
    return settings[key] !== undefined ? settings[key] : defaultValue
  }

  /**
   * 设置特定设置项
   */
  setSetting(key: string, value: any) {
    this.setSettings({ [key]: value })
  }

  /**
   * 清除所有存储数据
   */
  clearAll() {
    Object.values(this.KEYS).forEach(key => {
      platformAdapter.removeStorage(key)
    })
  }

  /**
   * 获取存储使用情况
   */
  getStorageInfo() {
    try {
      return {
        keys: Object.values(this.KEYS).map(key => ({
          key,
          hasData: !!platformAdapter.getStorage(key)
        })),
        isLoggedIn: this.isLoggedIn(),
        hasCurrentOrder: !!this.getCurrentOrder(),
        userPackagesCount: this.getUserPackages().length,
        hasRoomCache: !!this.getRoomCache()
      }
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return null
    }
  }
}

// 创建存储管理器实例
export const storage = new StorageManager()

// 导出常用方法的快捷方式
export const {
  setAuthInfo,
  getToken,
  getUserId,
  getUserInfo,
  updateUserInfo,
  clearAuthInfo,
  isLoggedIn,
  setCurrentOrder,
  getCurrentOrder,
  setUserPackages,
  getUserPackages,
  setRoomCache,
  getRoomCache,
  clearRoomCache,
  setSettings,
  getSettings,
  getSetting,
  setSetting,
  clearAll,
  getStorageInfo
} = storage
