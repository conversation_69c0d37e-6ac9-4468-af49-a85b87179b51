@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: $z-index-modal;
  @include flex-center;

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  &__content {
    position: relative;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    background-color: $white;
    border-radius: $border-radius-xl;
    overflow: hidden;
    @include shadow(lg);
  }

  &__header {
    padding: $spacing-lg $spacing-base $spacing-base;
    text-align: center;
    border-bottom: 1px solid $border-color;
  }

  &__title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-xs;
  }

  &__description {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__amount {
    padding: $spacing-base;
    text-align: center;
    background-color: $background-color;
  }

  &__amount-label {
    font-size: $font-size-sm;
    color: $text-secondary;
    margin-bottom: $spacing-xs;
  }

  &__amount-value {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $primary-color;
  }

  &__methods {
    padding: $spacing-base;
  }

  &__section-title {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: $spacing-base;
  }

  &__method {
    @include flex(row, flex-start, center);
    padding: $spacing-base;
    margin-bottom: $spacing-sm;
    border: 1px solid $border-color;
    border-radius: $border-radius-base;
    @include transition(all, $transition-duration-base);

    &:last-child {
      margin-bottom: 0;
    }

    &--disabled {
      opacity: 0.5;
      background-color: $background-color;
    }

    &:not(&--disabled):active {
      background-color: rgba($primary-color, 0.05);
      border-color: $primary-color;
    }
  }

  &__method-radio {
    margin-right: $spacing-base;
  }

  &__method-content {
    @include flex-between;
    flex: 1;
  }

  &__method-info {
    flex: 1;
  }

  &__method-name {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: 4px;
  }

  &__method-subtitle {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__method-icon {
    width: 40px;
    height: 40px;
    @include flex-center;
    background-color: $background-color;
    border-radius: $border-radius-base;
  }

  &__packages {
    padding: 0 $spacing-base $spacing-base;
    border-top: 1px solid $border-color;
    margin-top: $spacing-base;
    padding-top: $spacing-base;
  }

  &__package {
    @include flex(row, flex-start, center);
    padding: $spacing-sm $spacing-base;
    margin-bottom: $spacing-sm;
    border: 1px solid $border-color;
    border-radius: $border-radius-base;
    @include transition(all, $transition-duration-base);

    &:last-child {
      margin-bottom: 0;
    }

    &:active {
      background-color: rgba($primary-color, 0.05);
      border-color: $primary-color;
    }
  }

  &__package-radio {
    margin-right: $spacing-base;
  }

  &__package-content {
    flex: 1;
  }

  &__package-name {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: 4px;
  }

  &__package-info {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__actions {
    @include flex(row, space-between, center);
    padding: $spacing-base;
    border-top: 1px solid $border-color;
    gap: $spacing-base;
  }

  &__action-btn {
    flex: 1;
    height: 80px;
    font-size: $font-size-base;
    border-radius: $border-radius-base;

    &--cancel {
      background-color: $white;
      color: $text-primary;
      border: 1px solid $border-color;

      &:active {
        background-color: $background-color;
      }
    }

    &--confirm {
      background-color: $primary-color;
      color: $white;
      border: 1px solid $primary-color;

      &:active {
        background-color: $primary-dark;
      }

      &:disabled {
        background-color: $border-color;
        color: $text-placeholder;
        border-color: $border-color;
      }
    }
  }
}
