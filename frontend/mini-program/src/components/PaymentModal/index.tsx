import React, { useState, useEffect } from 'react'
import { View, Text, Button, Radio, RadioGroup } from '@tarojs/components'
import { formatAmount } from '@/utils'
import { paymentService } from '@/services/payment'
import { authService } from '@/services/auth'
import { storage } from '@/utils/storage'
import type { PaymentMethod, UserPackage } from '@/types'
import './index.scss'

interface PaymentModalProps {
  visible: boolean
  amount: number
  orderId?: number
  title?: string
  description?: string
  onConfirm?: (paymentMethod: PaymentMethod, extra?: any) => void
  onCancel?: () => void
  className?: string
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  visible,
  amount,
  orderId,
  title = '选择支付方式',
  description,
  onConfirm,
  onCancel,
  className = ''
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>('balance')
  const [userBalance, setUserBalance] = useState(0)
  const [userPackages, setUserPackages] = useState<UserPackage[]>([])
  const [selectedPackage, setSelectedPackage] = useState<number | null>(null)

  useEffect(() => {
    if (visible) {
      loadUserData()
    }
  }, [visible])

  const loadUserData = async () => {
    try {
      // 获取用户信息
      const user = authService.getCurrentUser()
      if (user) {
        setUserBalance(user.balance)
      }

      // 获取用户套餐
      const packages = storage.getUserPackages()
      const activePackages = packages.filter(pkg => 
        pkg.status === 'active' && pkg.remaining_hours > 0
      )
      setUserPackages(activePackages)
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  }

  const getPaymentMethods = () => {
    const methods = paymentService.getPaymentMethods()
    
    return methods.map(method => {
      let disabled = false
      let subtitle = ''

      switch (method.method) {
        case 'balance':
          disabled = userBalance < amount
          subtitle = `余额: ${formatAmount(userBalance * 100)}`
          if (disabled) subtitle += ' (余额不足)'
          break
        case 'package':
          disabled = userPackages.length === 0
          subtitle = disabled ? '暂无可用套餐' : `${userPackages.length}个可用套餐`
          break
        default:
          subtitle = method.name
      }

      return {
        ...method,
        disabled,
        subtitle
      }
    })
  }

  const handleMethodChange = (value: string) => {
    setSelectedMethod(value as PaymentMethod)
    if (value !== 'package') {
      setSelectedPackage(null)
    }
  }

  const handlePackageChange = (value: string) => {
    setSelectedPackage(parseInt(value))
  }

  const handleConfirm = () => {
    let extra: any = {}

    if (selectedMethod === 'package' && selectedPackage) {
      const pkg = userPackages.find(p => p.id === selectedPackage)
      if (pkg) {
        // 计算需要使用的小时数
        const estimatedHours = Math.ceil(amount / 100 / 20) // 假设20元/小时
        extra = {
          userPackageId: selectedPackage,
          hours: Math.min(estimatedHours, pkg.remaining_hours)
        }
      }
    }

    onConfirm?.(selectedMethod, extra)
  }

  const handleCancel = () => {
    onCancel?.()
  }

  if (!visible) return null

  const paymentMethods = getPaymentMethods()
  const canConfirm = selectedMethod && 
    (selectedMethod !== 'package' || selectedPackage !== null)

  return (
    <View className={`payment-modal ${className}`}>
      <View className="payment-modal__overlay" onClick={handleCancel} />
      
      <View className="payment-modal__content">
        <View className="payment-modal__header">
          <Text className="payment-modal__title">{title}</Text>
          {description && (
            <Text className="payment-modal__description">{description}</Text>
          )}
        </View>

        <View className="payment-modal__amount">
          <Text className="payment-modal__amount-label">支付金额</Text>
          <Text className="payment-modal__amount-value">
            {formatAmount(amount)}
          </Text>
        </View>

        <View className="payment-modal__methods">
          <Text className="payment-modal__section-title">支付方式</Text>
          
          <RadioGroup value={selectedMethod} onChange={handleMethodChange}>
            {paymentMethods.map(method => (
              <View 
                key={method.method}
                className={`payment-modal__method ${method.disabled ? 'payment-modal__method--disabled' : ''}`}
              >
                <Radio 
                  value={method.method}
                  disabled={method.disabled}
                  className="payment-modal__method-radio"
                />
                <View className="payment-modal__method-content">
                  <View className="payment-modal__method-info">
                    <Text className="payment-modal__method-name">
                      {method.name}
                    </Text>
                    <Text className="payment-modal__method-subtitle">
                      {method.subtitle}
                    </Text>
                  </View>
                  <View className="payment-modal__method-icon">
                    {/* 这里可以添加图标 */}
                  </View>
                </View>
              </View>
            ))}
          </RadioGroup>
        </View>

        {selectedMethod === 'package' && userPackages.length > 0 && (
          <View className="payment-modal__packages">
            <Text className="payment-modal__section-title">选择套餐</Text>
            
            <RadioGroup value={selectedPackage?.toString()} onChange={handlePackageChange}>
              {userPackages.map(pkg => (
                <View key={pkg.id} className="payment-modal__package">
                  <Radio 
                    value={pkg.id.toString()}
                    className="payment-modal__package-radio"
                  />
                  <View className="payment-modal__package-content">
                    <Text className="payment-modal__package-name">
                      {pkg.package_name}
                    </Text>
                    <Text className="payment-modal__package-info">
                      剩余 {pkg.remaining_hours} 小时
                    </Text>
                  </View>
                </View>
              ))}
            </RadioGroup>
          </View>
        )}

        <View className="payment-modal__actions">
          <Button 
            className="payment-modal__action-btn payment-modal__action-btn--cancel"
            onClick={handleCancel}
          >
            取消
          </Button>
          <Button 
            className="payment-modal__action-btn payment-modal__action-btn--confirm"
            type="primary"
            disabled={!canConfirm}
            onClick={handleConfirm}
          >
            确认支付
          </Button>
        </View>
      </View>
    </View>
  )
}

export default PaymentModal
