import React from 'react'
import { render, fireEvent } from '@testing-library/react'
import RoomCard from '../RoomCard'
import type { Room } from '@/types'

const mockRoom: Room = {
  id: 1,
  room_number: '001',
  name: '豪华包间A',
  description: '设施齐全的豪华包间',
  status: 'available',
  pricing_rule_id: 1,
  price_per_hour: 20,
  created_at: '2025-01-27T10:00:00Z',
  updated_at: '2025-01-27T10:00:00Z'
}

describe('RoomCard组件测试', () => {
  test('正常渲染房间信息', () => {
    const { getByText } = render(<RoomCard room={mockRoom} />)
    
    expect(getByText('001')).toBeInTheDocument()
    expect(getByText('豪华包间A')).toBeInTheDocument()
    expect(getByText('设施齐全的豪华包间')).toBeInTheDocument()
    expect(getByText('空闲')).toBeInTheDocument()
    expect(getByText('¥20.00/小时')).toBeInTheDocument()
  })

  test('不显示价格', () => {
    const { queryByText } = render(
      <RoomCard room={mockRoom} showPrice={false} />
    )
    
    expect(queryByText('¥20.00/小时')).not.toBeInTheDocument()
  })

  test('不同尺寸渲染', () => {
    const { container: smallContainer } = render(
      <RoomCard room={mockRoom} size="small" />
    )
    const { container: largeContainer } = render(
      <RoomCard room={mockRoom} size="large" />
    )
    
    expect(smallContainer.querySelector('.room-card--small')).toBeInTheDocument()
    expect(largeContainer.querySelector('.room-card--large')).toBeInTheDocument()
  })

  test('点击事件触发', () => {
    const handleClick = jest.fn()
    const { container } = render(
      <RoomCard room={mockRoom} onClick={handleClick} />
    )
    
    fireEvent.click(container.firstChild!)
    expect(handleClick).toHaveBeenCalledWith(mockRoom)
  })

  test('房间状态样式', () => {
    const occupiedRoom = { ...mockRoom, status: 'occupied' as const }
    const maintenanceRoom = { ...mockRoom, status: 'maintenance' as const }
    
    const { container: occupiedContainer } = render(
      <RoomCard room={occupiedRoom} />
    )
    const { container: maintenanceContainer } = render(
      <RoomCard room={maintenanceRoom} />
    )
    
    expect(occupiedContainer.querySelector('.room-card')).toBeInTheDocument()
    expect(maintenanceContainer.querySelector('.room-card')).toBeInTheDocument()
  })

  test('不可用房间显示遮罩', () => {
    const occupiedRoom = { ...mockRoom, status: 'occupied' as const }
    const { container } = render(<RoomCard room={occupiedRoom} />)
    
    expect(container.querySelector('.room-card__overlay')).toBeInTheDocument()
  })

  test('可用房间不显示遮罩', () => {
    const { container } = render(<RoomCard room={mockRoom} />)
    
    expect(container.querySelector('.room-card__overlay')).not.toBeInTheDocument()
  })

  test('自定义className', () => {
    const { container } = render(
      <RoomCard room={mockRoom} className="custom-class" />
    )
    
    expect(container.querySelector('.custom-class')).toBeInTheDocument()
  })

  test('无描述信息时不显示描述', () => {
    const roomWithoutDesc = { ...mockRoom, description: '' }
    const { queryByText } = render(<RoomCard room={roomWithoutDesc} />)
    
    expect(queryByText('设施齐全的豪华包间')).not.toBeInTheDocument()
  })
})
