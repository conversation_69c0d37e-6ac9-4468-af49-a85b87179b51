@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.room-card {
  position: relative;
  @include card($spacing-base, $border-radius-lg);
  @include transition(all, $transition-duration-base);
  cursor: pointer;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }

  &__header {
    @include flex-between;
    margin-bottom: $spacing-sm;
  }

  &__number {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
  }

  &__status {
    @include flex(row, flex-start, center);
    gap: 4px;
  }

  &__status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  &__status-text {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }

  &__content {
    margin-bottom: $spacing-sm;
  }

  &__name {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: 4px;
    @include text-ellipsis(1);
  }

  &__description {
    font-size: $font-size-sm;
    color: $text-secondary;
    @include text-ellipsis(2);
  }

  &__footer {
    @include flex-between;
  }

  &__price {
    font-size: $font-size-base;
    font-weight: $font-weight-bold;
    color: $primary-color;
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1;
  }

  // 尺寸变体
  &--small {
    padding: $spacing-sm;

    .room-card__number {
      font-size: $font-size-base;
    }

    .room-card__name {
      font-size: $font-size-sm;
    }

    .room-card__price {
      font-size: $font-size-sm;
    }
  }

  &--medium {
    padding: $spacing-base;
  }

  &--large {
    padding: $spacing-lg;

    .room-card__number {
      font-size: $font-size-xl;
    }

    .room-card__name {
      font-size: $font-size-lg;
    }

    .room-card__price {
      font-size: $font-size-lg;
    }
  }

  // 状态样式
  &--available {
    border-left: 4px solid $room-available-color;
  }

  &--occupied {
    border-left: 4px solid $room-occupied-color;
  }

  &--maintenance {
    border-left: 4px solid $room-maintenance-color;
  }
}
