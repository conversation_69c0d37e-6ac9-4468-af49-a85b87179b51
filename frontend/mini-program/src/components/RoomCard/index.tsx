import React from 'react'
import { View, Text } from '@tarojs/components'
import { ROOM_STATUS_TEXT, ROOM_STATUS_COLOR } from '@/utils/constants'
import { formatAmount } from '@/utils'
import type { Room } from '@/types'
import './index.scss'

interface RoomCardProps {
  room: Room
  onClick?: (room: Room) => void
  showPrice?: boolean
  size?: 'small' | 'medium' | 'large'
  className?: string
}

const RoomCard: React.FC<RoomCardProps> = ({
  room,
  onClick,
  showPrice = true,
  size = 'medium',
  className = ''
}) => {
  const handleClick = () => {
    onClick?.(room)
  }

  const statusText = ROOM_STATUS_TEXT[room.status]
  const statusColor = ROOM_STATUS_COLOR[room.status]

  return (
    <View 
      className={`room-card room-card--${size} ${className}`}
      onClick={handleClick}
    >
      <View className="room-card__header">
        <Text className="room-card__number">{room.room_number}</Text>
        <View 
          className="room-card__status"
          style={{ color: statusColor }}
        >
          <View 
            className="room-card__status-dot"
            style={{ backgroundColor: statusColor }}
          />
          <Text className="room-card__status-text">{statusText}</Text>
        </View>
      </View>

      <View className="room-card__content">
        <Text className="room-card__name">{room.name}</Text>
        {room.description && (
          <Text className="room-card__description">{room.description}</Text>
        )}
      </View>

      {showPrice && (
        <View className="room-card__footer">
          <Text className="room-card__price">
            {formatAmount(room.price_per_hour * 100)}/小时
          </Text>
        </View>
      )}

      {room.status !== 'available' && (
        <View className="room-card__overlay" />
      )}
    </View>
  )
}

export default RoomCard
