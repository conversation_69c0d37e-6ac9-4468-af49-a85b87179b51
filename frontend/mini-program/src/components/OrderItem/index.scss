@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.order-item {
  @include card($spacing-base, $border-radius-lg);
  @include transition(all, $transition-duration-base);
  cursor: pointer;
  margin-bottom: $spacing-base;

  &:active {
    transform: scale(0.98);
  }

  &__header {
    @include flex-between;
    margin-bottom: $spacing-base;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid $border-color;
  }

  &__room {
    flex: 1;
  }

  &__room-name {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-primary;
    display: block;
    margin-bottom: 4px;
  }

  &__room-number {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__status {
    @include flex(row, flex-start, center);
    gap: 4px;
  }

  &__status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  &__status-text {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
  }

  &__content {
    margin-bottom: $spacing-base;
  }

  &__info {
    margin-bottom: $spacing-base;
  }

  &__info-item {
    @include flex-between;
    margin-bottom: $spacing-xs;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__info-label {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__info-value {
    font-size: $font-size-sm;
    color: $text-primary;
    font-weight: $font-weight-medium;
  }

  &__amount {
    padding: $spacing-sm;
    background-color: $background-color;
    border-radius: $border-radius-base;
  }

  &__amount-item {
    @include flex-between;
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__amount-label {
    font-size: $font-size-sm;
    color: $text-secondary;
  }

  &__amount-value {
    font-size: $font-size-base;
    font-weight: $font-weight-bold;
    color: $primary-color;

    &--paid {
      color: $success-color;
    }
  }

  &__actions {
    @include flex(row, flex-end, center);
    gap: $spacing-sm;
    padding-top: $spacing-base;
    border-top: 1px solid $border-color;
  }

  &__action-btn {
    min-width: 80px;
    height: 60px;
    font-size: $font-size-sm;
    border-radius: $border-radius-base;

    &--secondary {
      background-color: $white;
      color: $text-primary;
      border: 1px solid $border-color;

      &:active {
        background-color: $background-color;
      }
    }

    &--primary {
      background-color: $primary-color;
      color: $white;
      border: 1px solid $primary-color;

      &:active {
        background-color: $primary-dark;
      }
    }
  }

  &__package-info {
    margin-top: $spacing-sm;
    padding: $spacing-xs $spacing-sm;
    background-color: rgba($warning-color, 0.1);
    border-radius: $border-radius-base;
    border-left: 3px solid $warning-color;
  }

  &__package-text {
    font-size: $font-size-sm;
    color: $warning-color;
    font-weight: $font-weight-medium;
  }

  // 状态样式
  &--pending {
    border-left: 4px solid $order-pending-color;
  }

  &--paid {
    border-left: 4px solid $order-paid-color;
  }

  &--completed {
    border-left: 4px solid $order-completed-color;
  }

  &--cancelled {
    border-left: 4px solid $order-cancelled-color;
    opacity: 0.7;
  }
}
