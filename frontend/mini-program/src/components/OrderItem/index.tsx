import React from 'react'
import { View, Text, Button } from '@tarojs/components'
import { ORDER_STATUS_TEXT, ORDER_STATUS_COLOR, PAYMENT_METHOD_TEXT } from '@/utils/constants'
import { formatAmount, formatTime, getTimeDiff } from '@/utils'
import type { Order } from '@/types'
import './index.scss'

interface OrderItemProps {
  order: Order
  showActions?: boolean
  onContinue?: (order: Order) => void
  onSettle?: (order: Order) => void
  onDetail?: (order: Order) => void
  className?: string
}

const OrderItem: React.FC<OrderItemProps> = ({
  order,
  showActions = false,
  onContinue,
  onSettle,
  onDetail,
  className = ''
}) => {
  const statusText = ORDER_STATUS_TEXT[order.status]
  const statusColor = ORDER_STATUS_COLOR[order.status]
  const paymentMethodText = PAYMENT_METHOD_TEXT[order.payment_method]

  // 计算使用时长
  const getDuration = () => {
    if (order.end_time) {
      return getTimeDiff(order.start_time, order.end_time).text
    } else if (order.status === 'paid') {
      return getTimeDiff(order.start_time).text
    }
    return '0分钟'
  }

  const handleContinue = (e: any) => {
    e.stopPropagation()
    onContinue?.(order)
  }

  const handleSettle = (e: any) => {
    e.stopPropagation()
    onSettle?.(order)
  }

  const handleDetail = () => {
    onDetail?.(order)
  }

  return (
    <View 
      className={`order-item ${className}`}
      onClick={handleDetail}
    >
      <View className="order-item__header">
        <View className="order-item__room">
          <Text className="order-item__room-name">
            {order.room_name || `房间${order.room_number}号`}
          </Text>
          <Text className="order-item__room-number">#{order.room_number}</Text>
        </View>
        <View 
          className="order-item__status"
          style={{ color: statusColor }}
        >
          <View 
            className="order-item__status-dot"
            style={{ backgroundColor: statusColor }}
          />
          <Text className="order-item__status-text">{statusText}</Text>
        </View>
      </View>

      <View className="order-item__content">
        <View className="order-item__info">
          <View className="order-item__info-item">
            <Text className="order-item__info-label">开始时间</Text>
            <Text className="order-item__info-value">
              {formatTime(order.start_time, 'MM-DD HH:mm')}
            </Text>
          </View>
          
          {order.end_time && (
            <View className="order-item__info-item">
              <Text className="order-item__info-label">结束时间</Text>
              <Text className="order-item__info-value">
                {formatTime(order.end_time, 'MM-DD HH:mm')}
              </Text>
            </View>
          )}

          <View className="order-item__info-item">
            <Text className="order-item__info-label">使用时长</Text>
            <Text className="order-item__info-value">{getDuration()}</Text>
          </View>

          <View className="order-item__info-item">
            <Text className="order-item__info-label">支付方式</Text>
            <Text className="order-item__info-value">{paymentMethodText}</Text>
          </View>
        </View>

        <View className="order-item__amount">
          <View className="order-item__amount-item">
            <Text className="order-item__amount-label">总金额</Text>
            <Text className="order-item__amount-value">
              {formatAmount(order.total_amount)}
            </Text>
          </View>
          
          {order.paid_amount !== order.total_amount && (
            <View className="order-item__amount-item">
              <Text className="order-item__amount-label">已支付</Text>
              <Text className="order-item__amount-value order-item__amount-value--paid">
                {formatAmount(order.paid_amount)}
              </Text>
            </View>
          )}
        </View>
      </View>

      {showActions && order.status === 'paid' && (
        <View className="order-item__actions">
          <Button 
            className="order-item__action-btn order-item__action-btn--secondary"
            size="mini"
            onClick={handleContinue}
          >
            续费
          </Button>
          <Button 
            className="order-item__action-btn order-item__action-btn--primary"
            size="mini"
            type="primary"
            onClick={handleSettle}
          >
            结算
          </Button>
        </View>
      )}

      {order.package_type === 'package' && order.package_hours_used && (
        <View className="order-item__package-info">
          <Text className="order-item__package-text">
            套餐抵扣 {order.package_hours_used}小时
          </Text>
        </View>
      )}
    </View>
  )
}

export default OrderItem
