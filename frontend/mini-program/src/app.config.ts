import { getPlatformTabBarConfig } from '@/utils/platform'

export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/scan/index',
    'pages/order/index',
    'pages/reservation/index',
    'pages/profile/index',
    'pages/login/index',
    'pages/room-detail/index',
    'pages/order-detail/index',
    'pages/payment/index',
    'pages/recharge/index',
    'pages/package/index',
    'pages/settings/index'
  ],
  
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#1E88E5',
    navigationBarTitleText: '自助麻将室',
    navigationBarTextStyle: 'white',
    backgroundColor: '#F5F5F5'
  },
  
  tabBar: {
    color: '#999999',
    selectedColor: '#1E88E5',
    backgroundColor: '#ffffff',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: 'assets/icons/home.png',
        selectedIconPath: 'assets/icons/home-active.png'
      },
      {
        pagePath: 'pages/order/index',
        text: '订单',
        iconPath: 'assets/icons/order.png',
        selectedIconPath: 'assets/icons/order-active.png'
      },
      {
        pagePath: 'pages/reservation/index',
        text: '预约',
        iconPath: 'assets/icons/calendar.png',
        selectedIconPath: 'assets/icons/calendar-active.png'
      },
      {
        pagePath: 'pages/profile/index',
        text: '我的',
        iconPath: 'assets/icons/profile.png',
        selectedIconPath: 'assets/icons/profile-active.png'
      }
    ]
  },
  
  permission: {
    'scope.camera': {
      desc: '需要使用相机扫描房间二维码'
    }
  },
  
  requiredPrivateInfos: [
    'getLocation'
  ],
  
  lazyCodeLoading: 'requiredComponents'
})
