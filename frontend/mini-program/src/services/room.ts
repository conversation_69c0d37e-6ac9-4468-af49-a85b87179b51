import { http } from '@/utils/request'
import { storage } from '@/utils/storage'
import { showPlatformToast } from '@/utils/platform'
import { CACHE_TIME } from '@/utils/constants'
import type { Room, RoomWithDevices, PaginationResponse } from '@/types'

/**
 * 房间服务类
 */
class RoomService {
  /**
   * 获取房间列表
   */
  async getRoomList(params?: {
    page?: number
    page_size?: number
    status?: string
    useCache?: boolean
  }): Promise<PaginationResponse<Room>> {
    try {
      const { useCache = true, ...queryParams } = params || {}
      
      // 尝试从缓存获取
      if (useCache && !queryParams.page && !queryParams.status) {
        const cachedRooms = storage.getRoomCache()
        if (cachedRooms) {
          return {
            page: 1,
            page_size: cachedRooms.length,
            total: cachedRooms.length,
            total_pages: 1,
            data: cachedRooms
          }
        }
      }
      
      const response = await http.get<PaginationResponse<Room>>('/rooms', queryParams)
      
      // 缓存第一页的完整数据
      if (!queryParams.page && !queryParams.status) {
        storage.setRoomCache(response.data, CACHE_TIME.SHORT)
      }
      
      return response
    } catch (error) {
      console.error('获取房间列表失败:', error)
      throw new Error(`获取房间列表失败: ${error.message}`)
    }
  }

  /**
   * 获取可用房间列表
   */
  async getAvailableRooms(): Promise<Room[]> {
    try {
      const response = await http.get<Room[]>('/rooms/available')
      return response
    } catch (error) {
      console.error('获取可用房间失败:', error)
      throw new Error(`获取可用房间失败: ${error.message}`)
    }
  }

  /**
   * 获取房间详情
   */
  async getRoomDetail(roomId: number): Promise<Room> {
    try {
      const response = await http.get<Room>(`/rooms/${roomId}`)
      return response
    } catch (error) {
      console.error('获取房间详情失败:', error)
      throw new Error(`获取房间详情失败: ${error.message}`)
    }
  }

  /**
   * 根据房间号获取房间信息
   */
  async getRoomByNumber(roomNumber: string): Promise<Room> {
    try {
      const response = await http.get<Room>(`/rooms/number/${roomNumber}`)
      return response
    } catch (error) {
      console.error('获取房间信息失败:', error)
      throw new Error(`获取房间信息失败: ${error.message}`)
    }
  }

  /**
   * 获取房间及设备信息
   */
  async getRoomWithDevices(roomId: number): Promise<RoomWithDevices> {
    try {
      const response = await http.get<RoomWithDevices>(`/rooms/${roomId}/devices`)
      return response
    } catch (error) {
      console.error('获取房间设备信息失败:', error)
      throw new Error(`获取房间设备信息失败: ${error.message}`)
    }
  }

  /**
   * 刷新房间列表缓存
   */
  async refreshRoomList(): Promise<Room[]> {
    try {
      // 清除缓存
      storage.clearRoomCache()
      
      // 重新获取数据
      const response = await this.getRoomList({ useCache: false })
      return response.data
    } catch (error) {
      console.error('刷新房间列表失败:', error)
      throw error
    }
  }

  /**
   * 搜索房间
   */
  async searchRooms(keyword: string): Promise<Room[]> {
    try {
      const response = await this.getRoomList({ useCache: false })
      
      // 前端过滤搜索结果
      const filteredRooms = response.data.filter(room => 
        room.room_number.includes(keyword) ||
        room.name.includes(keyword) ||
        room.description.includes(keyword)
      )
      
      return filteredRooms
    } catch (error) {
      console.error('搜索房间失败:', error)
      throw new Error(`搜索房间失败: ${error.message}`)
    }
  }

  /**
   * 获取房间状态统计
   */
  async getRoomStatusStats(): Promise<{
    total: number
    available: number
    occupied: number
    maintenance: number
  }> {
    try {
      const response = await this.getRoomList({ useCache: true })
      const rooms = response.data
      
      const stats = {
        total: rooms.length,
        available: rooms.filter(room => room.status === 'available').length,
        occupied: rooms.filter(room => room.status === 'occupied').length,
        maintenance: rooms.filter(room => room.status === 'maintenance').length
      }
      
      return stats
    } catch (error) {
      console.error('获取房间状态统计失败:', error)
      throw error
    }
  }

  /**
   * 检查房间是否可用
   */
  async checkRoomAvailability(roomId: number): Promise<boolean> {
    try {
      const room = await this.getRoomDetail(roomId)
      return room.status === 'available'
    } catch (error) {
      console.error('检查房间可用性失败:', error)
      return false
    }
  }

  /**
   * 获取房间计费信息
   */
  async getRoomPricing(roomId: number): Promise<{
    pricePerHour: number
    pricingRuleId: number
  }> {
    try {
      const room = await this.getRoomDetail(roomId)
      return {
        pricePerHour: room.price_per_hour,
        pricingRuleId: room.pricing_rule_id
      }
    } catch (error) {
      console.error('获取房间计费信息失败:', error)
      throw error
    }
  }

  /**
   * 预估使用费用
   */
  estimateUsageCost(pricePerHour: number, hours: number): number {
    return Math.ceil(pricePerHour * hours * 100) // 转换为分
  }

  /**
   * 获取推荐房间
   */
  async getRecommendedRooms(limit: number = 5): Promise<Room[]> {
    try {
      const availableRooms = await this.getAvailableRooms()
      
      // 简单的推荐逻辑：按价格排序，返回前几个
      const sortedRooms = availableRooms
        .sort((a, b) => a.price_per_hour - b.price_per_hour)
        .slice(0, limit)
      
      return sortedRooms
    } catch (error) {
      console.error('获取推荐房间失败:', error)
      throw error
    }
  }

  /**
   * 清除房间缓存
   */
  clearCache(): void {
    storage.clearRoomCache()
  }

  /**
   * 获取缓存的房间列表
   */
  getCachedRooms(): Room[] | null {
    return storage.getRoomCache()
  }
}

// 创建房间服务实例
export const roomService = new RoomService()

// 导出常用方法的快捷方式
export const {
  getRoomList,
  getAvailableRooms,
  getRoomDetail,
  getRoomByNumber,
  getRoomWithDevices,
  refreshRoomList,
  searchRooms,
  getRoomStatusStats,
  checkRoomAvailability,
  getRoomPricing,
  estimateUsageCost,
  getRecommendedRooms,
  clearCache: clearRoomCache,
  getCachedRooms
} = roomService
