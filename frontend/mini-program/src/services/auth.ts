import { http } from '@/utils/request'
import { platformAdapter } from '@/utils/adapter'
import { storage } from '@/utils/storage'
import { getCurrentPlatform, showPlatformToast } from '@/utils/platform'
import type { 
  User, 
  UserLoginRequest, 
  UserRegisterRequest, 
  UserUpdateRequest,
  LoginResult,
  UserInfo
} from '@/types'

/**
 * 认证服务类
 */
class AuthService {
  /**
   * 平台登录
   */
  async login(): Promise<User> {
    try {
      // 获取平台授权码
      const loginResult: LoginResult = await platformAdapter.login()
      
      // 调用后端登录接口
      const loginRequest: UserLoginRequest = {
        openid: loginResult.code,
        platform: loginResult.platform
      }
      
      const response = await http.post<{
        user: User
        token: string
      }>('/users/login', loginRequest)
      
      // 保存认证信息
      storage.setAuthInfo(response.token, response.user.id, response.user)
      
      showPlatformToast('登录成功', 'success')
      return response.user
    } catch (error) {
      console.error('登录失败:', error)
      throw new Error(`登录失败: ${error.message}`)
    }
  }

  /**
   * 用户注册
   */
  async register(): Promise<User> {
    try {
      // 获取平台授权码
      const loginResult: LoginResult = await platformAdapter.login()
      
      // 获取用户信息
      const userInfo: UserInfo = await platformAdapter.getUserInfo()
      
      // 调用后端注册接口
      const registerRequest: UserRegisterRequest = {
        openid: loginResult.code,
        nickname: userInfo.nickName,
        avatar_url: userInfo.avatarUrl,
        platform: loginResult.platform
      }
      
      const response = await http.post<{
        user: User
        token: string
      }>('/users/register', registerRequest)
      
      // 保存认证信息
      storage.setAuthInfo(response.token, response.user.id, response.user)
      
      showPlatformToast('注册成功', 'success')
      return response.user
    } catch (error) {
      console.error('注册失败:', error)
      throw new Error(`注册失败: ${error.message}`)
    }
  }

  /**
   * 自动登录（检查本地存储的认证信息）
   */
  async autoLogin(): Promise<User | null> {
    try {
      if (!storage.isLoggedIn()) {
        return null
      }

      // 验证token有效性
      const userInfo = await this.getUserProfile()
      return userInfo
    } catch (error) {
      console.error('自动登录失败:', error)
      // 清除无效的认证信息
      storage.clearAuthInfo()
      return null
    }
  }

  /**
   * 获取用户资料
   */
  async getUserProfile(): Promise<User> {
    try {
      const response = await http.get<User>('/users/profile')
      
      // 更新本地存储的用户信息
      storage.updateUserInfo(response)
      
      return response
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw new Error(`获取用户资料失败: ${error.message}`)
    }
  }

  /**
   * 更新用户资料
   */
  async updateUserProfile(updateData: UserUpdateRequest): Promise<User> {
    try {
      const response = await http.put<User>('/users/profile', updateData)
      
      // 更新本地存储的用户信息
      storage.updateUserInfo(response)
      
      showPlatformToast('资料更新成功', 'success')
      return response
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw new Error(`更新用户资料失败: ${error.message}`)
    }
  }

  /**
   * 退出登录
   */
  async logout(): Promise<void> {
    try {
      // 清除本地存储
      storage.clearAuthInfo()
      storage.setCurrentOrder(null)
      storage.clearRoomCache()
      
      showPlatformToast('已退出登录', 'success')
    } catch (error) {
      console.error('退出登录失败:', error)
    }
  }

  /**
   * 检查登录状态
   */
  isLoggedIn(): boolean {
    return storage.isLoggedIn()
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): User | null {
    return storage.getUserInfo()
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId(): number | null {
    return storage.getUserId()
  }

  /**
   * 获取认证token
   */
  getToken(): string | null {
    return storage.getToken()
  }

  /**
   * 刷新用户信息
   */
  async refreshUserInfo(): Promise<User> {
    try {
      const userInfo = await this.getUserProfile()
      return userInfo
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      throw error
    }
  }

  /**
   * 检查是否需要完善用户信息
   */
  needCompleteProfile(): boolean {
    const user = this.getCurrentUser()
    if (!user) return false
    
    // 检查必要信息是否完整
    return !user.nickname || !user.phone
  }

  /**
   * 获取平台特定的用户信息
   */
  async getPlatformUserInfo(): Promise<UserInfo> {
    try {
      return await platformAdapter.getUserInfo()
    } catch (error) {
      console.error('获取平台用户信息失败:', error)
      throw new Error(`获取平台用户信息失败: ${error.message}`)
    }
  }

  /**
   * 订阅消息
   */
  async subscribeMessage(templateId: string): Promise<boolean> {
    try {
      return await platformAdapter.subscribeMessage(templateId)
    } catch (error) {
      console.error('订阅消息失败:', error)
      return false
    }
  }

  /**
   * 获取登录平台信息
   */
  getLoginPlatform(): string {
    const platform = getCurrentPlatform()
    return platform === 'wechat' ? '微信' : '支付宝'
  }
}

// 创建认证服务实例
export const authService = new AuthService()

// 导出常用方法的快捷方式
export const {
  login,
  register,
  autoLogin,
  getUserProfile,
  updateUserProfile,
  logout,
  isLoggedIn,
  getCurrentUser,
  getCurrentUserId,
  getToken,
  refreshUserInfo,
  needCompleteProfile,
  getPlatformUserInfo,
  subscribeMessage,
  getLoginPlatform
} = authService
