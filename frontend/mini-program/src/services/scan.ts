import { platformAdapter } from '@/utils/adapter'
import { roomService } from './room'
import { showPlatformToast } from '@/utils/platform'
import type { Room, ScanResult } from '@/types'

/**
 * 扫码服务类
 */
class ScanService {
  /**
   * 扫描二维码
   */
  async scanQRCode(): Promise<ScanResult> {
    try {
      const result = await platformAdapter.scanCode()
      return result
    } catch (error) {
      console.error('扫码失败:', error)
      throw new Error(`扫码失败: ${error.message}`)
    }
  }

  /**
   * 解析房间二维码
   */
  parseRoomQRCode(qrContent: string): {
    roomId?: number
    roomNumber?: string
    type: 'room' | 'unknown'
  } {
    try {
      // 尝试解析JSON格式的二维码
      if (qrContent.startsWith('{')) {
        const data = JSON.parse(qrContent)
        if (data.type === 'room' && (data.roomId || data.roomNumber)) {
          return {
            roomId: data.roomId,
            roomNumber: data.roomNumber,
            type: 'room'
          }
        }
      }
      
      // 尝试解析URL格式的二维码
      if (qrContent.startsWith('http')) {
        const url = new URL(qrContent)
        const roomId = url.searchParams.get('roomId')
        const roomNumber = url.searchParams.get('roomNumber')
        
        if (roomId || roomNumber) {
          return {
            roomId: roomId ? parseInt(roomId) : undefined,
            roomNumber: roomNumber || undefined,
            type: 'room'
          }
        }
      }
      
      // 尝试解析简单格式：ROOM_房间号
      if (qrContent.startsWith('ROOM_')) {
        const roomNumber = qrContent.replace('ROOM_', '')
        return {
          roomNumber,
          type: 'room'
        }
      }
      
      // 尝试直接解析为房间号
      if (/^\d+$/.test(qrContent)) {
        return {
          roomNumber: qrContent,
          type: 'room'
        }
      }
      
      return { type: 'unknown' }
    } catch (error) {
      console.error('解析二维码失败:', error)
      return { type: 'unknown' }
    }
  }

  /**
   * 扫码获取房间信息
   */
  async scanAndGetRoom(): Promise<{
    room: Room
    qrData: any
  }> {
    try {
      // 扫描二维码
      const scanResult = await this.scanQRCode()
      
      // 解析二维码内容
      const qrData = this.parseRoomQRCode(scanResult.result)
      
      if (qrData.type !== 'room') {
        throw new Error('无效的房间二维码')
      }
      
      // 获取房间信息
      let room: Room
      
      if (qrData.roomId) {
        room = await roomService.getRoomDetail(qrData.roomId)
      } else if (qrData.roomNumber) {
        room = await roomService.getRoomByNumber(qrData.roomNumber)
      } else {
        throw new Error('二维码中缺少房间信息')
      }
      
      return {
        room,
        qrData
      }
    } catch (error) {
      console.error('扫码获取房间信息失败:', error)
      
      if (error.message.includes('用户取消')) {
        throw new Error('用户取消扫码')
      } else if (error.message.includes('无效的房间二维码')) {
        showPlatformToast('请扫描有效的房间二维码', 'error')
        throw new Error('无效的房间二维码')
      } else {
        showPlatformToast('扫码失败，请重试', 'error')
        throw new Error(`扫码失败: ${error.message}`)
      }
    }
  }

  /**
   * 验证房间二维码
   */
  async validateRoomQRCode(qrContent: string): Promise<{
    isValid: boolean
    room?: Room
    error?: string
  }> {
    try {
      const qrData = this.parseRoomQRCode(qrContent)
      
      if (qrData.type !== 'room') {
        return {
          isValid: false,
          error: '不是有效的房间二维码'
        }
      }
      
      // 获取房间信息验证
      let room: Room
      
      if (qrData.roomId) {
        room = await roomService.getRoomDetail(qrData.roomId)
      } else if (qrData.roomNumber) {
        room = await roomService.getRoomByNumber(qrData.roomNumber)
      } else {
        return {
          isValid: false,
          error: '二维码中缺少房间信息'
        }
      }
      
      return {
        isValid: true,
        room
      }
    } catch (error) {
      return {
        isValid: false,
        error: error.message
      }
    }
  }

  /**
   * 生成房间二维码内容（用于测试）
   */
  generateRoomQRCode(roomId: number, roomNumber: string): string {
    return JSON.stringify({
      type: 'room',
      roomId,
      roomNumber,
      timestamp: Date.now()
    })
  }

  /**
   * 检查扫码权限
   */
  async checkScanPermission(): Promise<boolean> {
    try {
      // 在小程序中，扫码权限通常是默认允许的
      // 这里可以添加额外的权限检查逻辑
      return true
    } catch (error) {
      console.error('检查扫码权限失败:', error)
      return false
    }
  }

  /**
   * 扫码开台流程
   */
  async scanToStartRoom(): Promise<{
    room: Room
    canStart: boolean
    reason?: string
  }> {
    try {
      // 检查扫码权限
      const hasPermission = await this.checkScanPermission()
      if (!hasPermission) {
        throw new Error('没有扫码权限')
      }
      
      // 扫码获取房间信息
      const { room } = await this.scanAndGetRoom()
      
      // 检查房间状态
      if (room.status !== 'available') {
        return {
          room,
          canStart: false,
          reason: room.status === 'occupied' ? '房间使用中' : '房间维护中'
        }
      }
      
      return {
        room,
        canStart: true
      }
    } catch (error) {
      console.error('扫码开台失败:', error)
      throw error
    }
  }
}

// 创建扫码服务实例
export const scanService = new ScanService()

// 导出常用方法的快捷方式
export const {
  scanQRCode,
  parseRoomQRCode,
  scanAndGetRoom,
  validateRoomQRCode,
  generateRoomQRCode,
  checkScanPermission,
  scanToStartRoom
} = scanService
