import { http } from '@/utils/request'
import { platformAdapter } from '@/utils/adapter'
import { getCurrentPlatform, showPlatformToast } from '@/utils/platform'
import type { PaymentParams, PaymentResult, PaymentMethod } from '@/types'

/**
 * 支付服务类
 */
class PaymentService {
  /**
   * 创建支付订单
   */
  async createPayment(params: {
    orderId: number
    amount: number
    paymentMethod: PaymentMethod
    description?: string
  }): Promise<PaymentParams> {
    try {
      const platform = getCurrentPlatform()
      
      const response = await http.post<PaymentParams>('/orders/payment/create', {
        order_id: params.orderId,
        amount: params.amount,
        payment_method: params.paymentMethod,
        platform,
        description: params.description || '麻将室消费'
      })
      
      return response
    } catch (error) {
      console.error('创建支付订单失败:', error)
      throw new Error(`创建支付订单失败: ${error.message}`)
    }
  }

  /**
   * 发起平台支付
   */
  async requestPlatformPayment(paymentParams: PaymentParams): Promise<PaymentResult> {
    try {
      const result = await platformAdapter.requestPayment(paymentParams)
      return result
    } catch (error) {
      console.error('平台支付失败:', error)
      
      if (error.errMsg && error.errMsg.includes('cancel')) {
        throw new Error('用户取消支付')
      } else {
        throw new Error(`支付失败: ${error.message}`)
      }
    }
  }

  /**
   * 余额支付
   */
  async payWithBalance(params: {
    orderId: number
    amount: number
  }): Promise<void> {
    try {
      await http.post('/orders/payment/balance', {
        order_id: params.orderId,
        amount: params.amount
      })
      
      showPlatformToast('余额支付成功', 'success')
    } catch (error) {
      console.error('余额支付失败:', error)
      throw new Error(`余额支付失败: ${error.message}`)
    }
  }

  /**
   * 套餐支付
   */
  async payWithPackage(params: {
    orderId: number
    userPackageId: number
    hours: number
  }): Promise<void> {
    try {
      await http.post('/orders/payment/package', {
        order_id: params.orderId,
        user_package_id: params.userPackageId,
        hours: params.hours
      })
      
      showPlatformToast('套餐支付成功', 'success')
    } catch (error) {
      console.error('套餐支付失败:', error)
      throw new Error(`套餐支付失败: ${error.message}`)
    }
  }

  /**
   * 统一支付接口
   */
  async pay(params: {
    orderId: number
    amount: number
    paymentMethod: PaymentMethod
    userPackageId?: number
    hours?: number
    description?: string
  }): Promise<void> {
    try {
      const { orderId, amount, paymentMethod, userPackageId, hours, description } = params
      
      switch (paymentMethod) {
        case 'balance':
          await this.payWithBalance({ orderId, amount })
          break
          
        case 'package':
          if (!userPackageId || !hours) {
            throw new Error('套餐支付缺少必要参数')
          }
          await this.payWithPackage({ orderId, userPackageId, hours })
          break
          
        case 'wechat':
        case 'alipay':
          // 创建支付订单
          const paymentParams = await this.createPayment({
            orderId,
            amount,
            paymentMethod,
            description
          })
          
          // 发起平台支付
          await this.requestPlatformPayment(paymentParams)
          
          // 验证支付结果
          await this.verifyPayment(orderId)
          break
          
        default:
          throw new Error('不支持的支付方式')
      }
    } catch (error) {
      console.error('支付失败:', error)
      throw error
    }
  }

  /**
   * 验证支付结果
   */
  async verifyPayment(orderId: number): Promise<boolean> {
    try {
      const response = await http.get<{ isPaid: boolean }>(`/orders/${orderId}/payment/verify`)
      return response.isPaid
    } catch (error) {
      console.error('验证支付结果失败:', error)
      return false
    }
  }

  /**
   * 获取支付状态
   */
  async getPaymentStatus(orderId: number): Promise<{
    status: 'pending' | 'paid' | 'failed' | 'cancelled'
    amount: number
    paidAmount: number
    paymentMethod?: PaymentMethod
    paidAt?: string
  }> {
    try {
      const response = await http.get(`/orders/${orderId}/payment/status`)
      return response
    } catch (error) {
      console.error('获取支付状态失败:', error)
      throw error
    }
  }

  /**
   * 充值
   */
  async recharge(params: {
    amount: number
    paymentMethod: 'wechat' | 'alipay'
  }): Promise<void> {
    try {
      const platform = getCurrentPlatform()
      
      // 创建充值订单
      const paymentParams = await http.post<PaymentParams>('/users/recharge/create', {
        amount: params.amount,
        payment_method: params.paymentMethod,
        platform
      })
      
      // 发起平台支付
      await this.requestPlatformPayment(paymentParams)
      
      showPlatformToast('充值成功', 'success')
    } catch (error) {
      console.error('充值失败:', error)
      throw new Error(`充值失败: ${error.message}`)
    }
  }

  /**
   * 获取支付方式列表
   */
  getPaymentMethods(): Array<{
    method: PaymentMethod
    name: string
    icon: string
    available: boolean
  }> {
    const platform = getCurrentPlatform()
    
    const methods = [
      {
        method: 'balance' as PaymentMethod,
        name: '余额支付',
        icon: 'wallet',
        available: true
      },
      {
        method: 'package' as PaymentMethod,
        name: '套餐抵扣',
        icon: 'gift',
        available: true
      }
    ]
    
    if (platform === 'wechat') {
      methods.push({
        method: 'wechat' as PaymentMethod,
        name: '微信支付',
        icon: 'wechat-pay',
        available: true
      })
    } else if (platform === 'alipay') {
      methods.push({
        method: 'alipay' as PaymentMethod,
        name: '支付宝支付',
        icon: 'alipay',
        available: true
      })
    }
    
    return methods
  }

  /**
   * 计算实际支付金额
   */
  calculatePaymentAmount(params: {
    totalAmount: number
    discountAmount?: number
    couponAmount?: number
  }): number {
    const { totalAmount, discountAmount = 0, couponAmount = 0 } = params
    return Math.max(0, totalAmount - discountAmount - couponAmount)
  }

  /**
   * 格式化支付金额
   */
  formatPaymentAmount(amount: number): string {
    return (amount / 100).toFixed(2)
  }

  /**
   * 检查支付方式是否可用
   */
  async checkPaymentMethodAvailable(paymentMethod: PaymentMethod): Promise<boolean> {
    try {
      switch (paymentMethod) {
        case 'balance':
          // 检查余额是否足够
          return true // 具体逻辑在支付时检查
          
        case 'package':
          // 检查是否有可用套餐
          return true // 具体逻辑在支付时检查
          
        case 'wechat':
          return getCurrentPlatform() === 'wechat'
          
        case 'alipay':
          return getCurrentPlatform() === 'alipay'
          
        default:
          return false
      }
    } catch (error) {
      console.error('检查支付方式可用性失败:', error)
      return false
    }
  }
}

// 创建支付服务实例
export const paymentService = new PaymentService()

// 导出常用方法的快捷方式
export const {
  createPayment,
  requestPlatformPayment,
  payWithBalance,
  payWithPackage,
  pay,
  verifyPayment,
  getPaymentStatus,
  recharge,
  getPaymentMethods,
  calculatePaymentAmount,
  formatPaymentAmount,
  checkPaymentMethodAvailable
} = paymentService
