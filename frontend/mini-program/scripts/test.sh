#!/bin/bash

# 跨平台小程序测试脚本

set -e

echo "🧪 开始跨平台小程序测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Node.js版本
check_node_version() {
    echo -e "${BLUE}📋 检查Node.js版本...${NC}"
    node_version=$(node -v | cut -d'v' -f2)
    required_version="16.0.0"
    
    if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
        echo -e "${GREEN}✅ Node.js版本符合要求: $node_version${NC}"
    else
        echo -e "${RED}❌ Node.js版本过低，需要 >= $required_version，当前版本: $node_version${NC}"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装依赖...${NC}"
    if [ ! -d "node_modules" ]; then
        npm install
    else
        echo -e "${GREEN}✅ 依赖已安装${NC}"
    fi
}

# 类型检查
type_check() {
    echo -e "${BLUE}🔍 TypeScript类型检查...${NC}"
    npm run type-check
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 类型检查通过${NC}"
    else
        echo -e "${RED}❌ 类型检查失败${NC}"
        exit 1
    fi
}

# 代码规范检查
lint_check() {
    echo -e "${BLUE}📏 代码规范检查...${NC}"
    npm run lint
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 代码规范检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  代码规范检查有警告，尝试自动修复...${NC}"
        npm run lint:fix
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 代码规范问题已修复${NC}"
        else
            echo -e "${RED}❌ 代码规范检查失败${NC}"
            exit 1
        fi
    fi
}

# 单元测试
unit_test() {
    echo -e "${BLUE}🧪 运行单元测试...${NC}"
    npm run test:ci
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 单元测试通过${NC}"
    else
        echo -e "${RED}❌ 单元测试失败${NC}"
        exit 1
    fi
}

# 构建测试
build_test() {
    echo -e "${BLUE}🏗️  构建测试...${NC}"
    
    # 清理旧的构建文件
    npm run clean
    
    # 构建微信小程序
    echo -e "${BLUE}📱 构建微信小程序...${NC}"
    npm run build:weapp
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 微信小程序构建成功${NC}"
    else
        echo -e "${RED}❌ 微信小程序构建失败${NC}"
        exit 1
    fi
    
    # 构建支付宝小程序
    echo -e "${BLUE}💰 构建支付宝小程序...${NC}"
    npm run build:alipay
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 支付宝小程序构建成功${NC}"
    else
        echo -e "${RED}❌ 支付宝小程序构建失败${NC}"
        exit 1
    fi
}

# 检查构建产物
check_build_output() {
    echo -e "${BLUE}📂 检查构建产物...${NC}"
    
    # 检查微信小程序构建产物
    if [ -d "dist/weapp" ] && [ -f "dist/weapp/app.js" ]; then
        echo -e "${GREEN}✅ 微信小程序构建产物正常${NC}"
    else
        echo -e "${RED}❌ 微信小程序构建产物异常${NC}"
        exit 1
    fi
    
    # 检查支付宝小程序构建产物
    if [ -d "dist/alipay" ] && [ -f "dist/alipay/app.js" ]; then
        echo -e "${GREEN}✅ 支付宝小程序构建产物正常${NC}"
    else
        echo -e "${RED}❌ 支付宝小程序构建产物异常${NC}"
        exit 1
    fi
    
    # 显示构建产物大小
    echo -e "${BLUE}📊 构建产物大小:${NC}"
    du -sh dist/weapp | sed "s/dist\/weapp/微信小程序/"
    du -sh dist/alipay | sed "s/dist\/alipay/支付宝小程序/"
}

# 生成测试报告
generate_report() {
    echo -e "${BLUE}📋 生成测试报告...${NC}"
    
    # 创建报告目录
    mkdir -p reports
    
    # 生成测试覆盖率报告
    npm run test:coverage > reports/test-coverage.txt 2>&1
    
    # 生成构建报告
    cat > reports/build-report.md << EOF
# 跨平台小程序构建报告

## 构建时间
$(date)

## 构建产物
- 微信小程序: dist/weapp/
- 支付宝小程序: dist/alipay/

## 构建产物大小
$(du -sh dist/weapp | sed "s/dist\/weapp/微信小程序/")
$(du -sh dist/alipay | sed "s/dist\/alipay/支付宝小程序/")

## 文件列表
### 微信小程序
\`\`\`
$(find dist/weapp -type f | head -20)
\`\`\`

### 支付宝小程序
\`\`\`
$(find dist/alipay -type f | head -20)
\`\`\`
EOF
    
    echo -e "${GREEN}✅ 测试报告已生成到 reports/ 目录${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}🚀 跨平台小程序测试开始${NC}"
    echo "=================================="
    
    # 检查当前目录
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 执行测试步骤
    check_node_version
    install_dependencies
    type_check
    lint_check
    unit_test
    build_test
    check_build_output
    generate_report
    
    echo "=================================="
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    echo -e "${GREEN}✅ 微信小程序构建成功${NC}"
    echo -e "${GREEN}✅ 支付宝小程序构建成功${NC}"
    echo -e "${BLUE}📋 测试报告: reports/${NC}"
    echo -e "${BLUE}🔗 下一步: 使用开发者工具导入构建产物进行真机测试${NC}"
}

# 处理命令行参数
case "${1:-all}" in
    "node")
        check_node_version
        ;;
    "deps")
        install_dependencies
        ;;
    "type")
        type_check
        ;;
    "lint")
        lint_check
        ;;
    "test")
        unit_test
        ;;
    "build")
        build_test
        check_build_output
        ;;
    "report")
        generate_report
        ;;
    "all"|"")
        main
        ;;
    *)
        echo "用法: $0 [node|deps|type|lint|test|build|report|all]"
        echo "  node  - 检查Node.js版本"
        echo "  deps  - 安装依赖"
        echo "  type  - TypeScript类型检查"
        echo "  lint  - 代码规范检查"
        echo "  test  - 运行单元测试"
        echo "  build - 构建测试"
        echo "  report- 生成测试报告"
        echo "  all   - 运行所有测试（默认）"
        exit 1
        ;;
esac
