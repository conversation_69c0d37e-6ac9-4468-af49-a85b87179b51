{"compilerOptions": {"target": "es2017", "lib": ["es2017", "es2018.promise"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/utils/*": ["src/utils/*"], "@/services/*": ["src/services/*"], "@/stores/*": ["src/stores/*"], "@/assets/*": ["src/assets/*"], "@/types/*": ["src/types/*"]}}, "include": ["src/**/*", "types/**/*"], "exclude": ["node_modules", "dist"]}