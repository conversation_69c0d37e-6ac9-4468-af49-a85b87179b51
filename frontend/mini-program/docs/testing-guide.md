# 跨平台小程序测试指南

## 📋 测试环境准备

### 1. 开发工具安装

#### 微信开发者工具
- 下载地址：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
- 版本要求：最新稳定版
- 登录：使用微信扫码登录

#### 支付宝小程序开发者工具
- 下载地址：https://opendocs.alipay.com/mini/ide/download
- 版本要求：最新稳定版
- 登录：使用支付宝账号登录

### 2. 项目配置

#### 微信小程序配置
```json
// project.config.json (微信)
{
  "miniprogramRoot": "dist/weapp/",
  "projectname": "mahjong-miniprogram-wechat",
  "description": "自助麻将室微信小程序",
  "appid": "your_wechat_appid",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true
  }
}
```

#### 支付宝小程序配置
```json
// mini.project.json (支付宝)
{
  "miniprogramRoot": "dist/alipay/",
  "projectname": "mahjong-miniprogram-alipay",
  "description": "自助麻将室支付宝小程序",
  "appid": "your_alipay_appid",
  "enableAppxNg": true
}
```

## 🚀 本地开发测试

### 1. 启动开发服务

```bash
# 安装依赖
cd frontend/mini-program
npm install

# 启动微信小程序开发
npm run dev:weapp

# 启动支付宝小程序开发
npm run dev:alipay
```

### 2. 开发者工具导入

#### 微信小程序
1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择：`frontend/mini-program/dist/weapp`
4. AppID：填入测试AppID或选择"测试号"
5. 点击"导入"

#### 支付宝小程序
1. 打开支付宝小程序开发者工具
2. 选择"打开项目"
3. 项目目录选择：`frontend/mini-program/dist/alipay`
4. AppID：填入测试AppID
5. 点击"打开"

### 3. 开发调试

#### 控制台调试
```javascript
// 在页面中添加调试代码
console.log('当前平台:', getCurrentPlatform())
console.log('用户信息:', authService.getCurrentUser())
console.log('房间列表:', rooms)
```

#### 网络请求调试
```javascript
// 在 utils/request.ts 中添加调试
console.log('请求URL:', url)
console.log('请求参数:', data)
console.log('响应数据:', response)
```

## 🔧 功能测试清单

### 1. 用户认证测试

#### 微信平台测试
- [ ] 微信登录功能
- [ ] 用户信息获取
- [ ] 登录状态维护
- [ ] 自动登录功能
- [ ] 退出登录功能

#### 支付宝平台测试
- [ ] 支付宝登录功能
- [ ] 用户信息获取
- [ ] 登录状态维护
- [ ] 自动登录功能
- [ ] 退出登录功能

### 2. 房间管理测试

- [ ] 房间列表加载
- [ ] 房间状态显示
- [ ] 房间详情查看
- [ ] 房间搜索功能
- [ ] 下拉刷新功能
- [ ] 缓存机制测试

### 3. 扫码功能测试

#### 扫码权限测试
```javascript
// 测试扫码权限
const hasPermission = await scanService.checkScanPermission()
console.log('扫码权限:', hasPermission)
```

#### 二维码识别测试
- [ ] 正常房间二维码识别
- [ ] 无效二维码处理
- [ ] 房间不存在处理
- [ ] 房间不可用处理
- [ ] 手动输入房间号

### 4. 支付功能测试

#### 微信支付测试
```javascript
// 测试微信支付
const paymentParams = {
  orderId: 1,
  amount: 2000, // 20元
  paymentMethod: 'wechat'
}
await paymentService.pay(paymentParams)
```

#### 支付宝支付测试
```javascript
// 测试支付宝支付
const paymentParams = {
  orderId: 1,
  amount: 2000, // 20元
  paymentMethod: 'alipay'
}
await paymentService.pay(paymentParams)
```

#### 其他支付方式测试
- [ ] 余额支付
- [ ] 套餐抵扣
- [ ] 支付失败处理
- [ ] 支付取消处理

### 5. 页面功能测试

#### 首页测试
- [ ] 用户信息显示
- [ ] 房间状态展示
- [ ] 当前订单显示
- [ ] 快速扫码按钮
- [ ] 快捷功能菜单

#### 订单页面测试
- [ ] 当前订单显示
- [ ] 历史订单列表
- [ ] 订单详情跳转
- [ ] 续费功能
- [ ] 结算功能

#### 个人中心测试
- [ ] 用户信息显示
- [ ] 余额显示
- [ ] 功能菜单
- [ ] 登录/退出功能

## 📱 真机测试

### 1. 微信小程序真机测试

#### 预览测试
1. 在微信开发者工具中点击"预览"
2. 使用微信扫描二维码
3. 在手机微信中打开小程序
4. 测试各项功能

#### 真机调试
1. 在微信开发者工具中点击"真机调试"
2. 使用微信扫描二维码
3. 开启真机调试模式
4. 查看控制台日志

### 2. 支付宝小程序真机测试

#### 预览测试
1. 在支付宝开发者工具中点击"预览"
2. 使用支付宝扫描二维码
3. 在手机支付宝中打开小程序
4. 测试各项功能

#### 真机调试
1. 在支付宝开发者工具中点击"真机调试"
2. 使用支付宝扫描二维码
3. 开启真机调试模式
4. 查看控制台日志

## 🧪 自动化测试

### 1. 单元测试

```bash
# 安装测试依赖
npm install --save-dev jest @testing-library/react @testing-library/jest-dom

# 运行单元测试
npm run test
```

#### 测试用例示例
```javascript
// __tests__/utils/platform.test.ts
import { getCurrentPlatform, isWeapp, isAlipay } from '@/utils/platform'

describe('平台工具测试', () => {
  test('获取当前平台', () => {
    const platform = getCurrentPlatform()
    expect(['wechat', 'alipay']).toContain(platform)
  })

  test('微信平台判断', () => {
    process.env.TARO_ENV = 'weapp'
    expect(isWeapp()).toBe(true)
    expect(isAlipay()).toBe(false)
  })

  test('支付宝平台判断', () => {
    process.env.TARO_ENV = 'alipay'
    expect(isWeapp()).toBe(false)
    expect(isAlipay()).toBe(true)
  })
})
```

### 2. 组件测试

```javascript
// __tests__/components/RoomCard.test.tsx
import React from 'react'
import { render, fireEvent } from '@testing-library/react'
import RoomCard from '@/components/RoomCard'

const mockRoom = {
  id: 1,
  room_number: '001',
  name: '测试房间',
  status: 'available',
  price_per_hour: 20
}

describe('RoomCard组件测试', () => {
  test('正常渲染', () => {
    const { getByText } = render(<RoomCard room={mockRoom} />)
    expect(getByText('001')).toBeInTheDocument()
    expect(getByText('测试房间')).toBeInTheDocument()
  })

  test('点击事件', () => {
    const handleClick = jest.fn()
    const { container } = render(
      <RoomCard room={mockRoom} onClick={handleClick} />
    )
    
    fireEvent.click(container.firstChild)
    expect(handleClick).toHaveBeenCalledWith(mockRoom)
  })
})
```

## 🔍 性能测试

### 1. 页面性能测试

#### 加载时间测试
```javascript
// 在页面中添加性能监控
const startTime = Date.now()

useLoad(() => {
  const loadTime = Date.now() - startTime
  console.log('页面加载时间:', loadTime, 'ms')
})
```

#### 内存使用测试
```javascript
// 监控内存使用
const memoryInfo = wx.getSystemInfoSync()
console.log('设备内存:', memoryInfo)
```

### 2. 网络性能测试

```javascript
// 网络请求性能监控
class PerformanceMonitor {
  static trackApiCall(apiName) {
    const startTime = Date.now()
    return () => {
      const duration = Date.now() - startTime
      console.log(`API ${apiName} 耗时:`, duration, 'ms')
    }
  }
}

// 使用示例
const endTrack = PerformanceMonitor.trackApiCall('getRoomList')
await roomService.getRoomList()
endTrack()
```

## 🐛 常见问题排查

### 1. 编译问题

#### 依赖安装失败
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install
```

#### TypeScript编译错误
```bash
# 检查类型定义
npm run type-check
```

### 2. 运行时问题

#### 平台API调用失败
```javascript
// 检查平台环境
console.log('当前环境:', process.env.TARO_ENV)
console.log('平台适配器:', platformAdapter)
```

#### 网络请求失败
```javascript
// 检查网络配置
console.log('API基础URL:', process.env.API_BASE_URL)
console.log('请求头:', headers)
```

### 3. 样式问题

#### 样式不生效
```bash
# 检查SCSS编译
npm run build:weapp -- --watch
```

#### 平台样式差异
```scss
// 使用条件编译
/* #ifdef MP-WEIXIN */
.weixin-specific-style {
  // 微信特定样式
}
/* #endif */

/* #ifdef MP-ALIPAY */
.alipay-specific-style {
  // 支付宝特定样式
}
/* #endif */
```

## 📊 测试报告

### 测试完成检查清单

#### 功能测试
- [ ] 用户登录/注册
- [ ] 房间列表加载
- [ ] 扫码开台流程
- [ ] 支付功能
- [ ] 订单管理
- [ ] 个人中心

#### 兼容性测试
- [ ] 微信小程序正常运行
- [ ] 支付宝小程序正常运行
- [ ] 不同设备适配
- [ ] 不同系统版本兼容

#### 性能测试
- [ ] 页面加载速度 < 3秒
- [ ] 接口响应时间 < 2秒
- [ ] 内存使用合理
- [ ] 无明显卡顿

#### 用户体验测试
- [ ] 界面美观友好
- [ ] 操作流程顺畅
- [ ] 错误提示清晰
- [ ] 加载状态明确

## 🚀 发布前测试

### 1. 构建测试

```bash
# 构建微信小程序
npm run build:weapp

# 构建支付宝小程序
npm run build:alipay

# 检查构建产物
ls -la dist/weapp/
ls -la dist/alipay/
```

### 2. 上传测试

#### 微信小程序
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传到微信公众平台
4. 提交审核

#### 支付宝小程序
1. 在支付宝开发者工具中点击"上传"
2. 填写版本信息
3. 上传到支付宝开放平台
4. 提交审核

### 3. 线上测试

- [ ] 体验版功能测试
- [ ] 真实用户测试
- [ ] 数据统计检查
- [ ] 错误日志监控

---

通过以上完整的测试流程，可以确保跨平台小程序在两个平台上都能稳定运行，提供良好的用户体验。
