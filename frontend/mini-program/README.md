# 自助麻将室跨平台小程序

基于 Taro + React + TypeScript 开发的跨平台小程序，支持微信小程序和支付宝小程序。

## 🎯 项目特性

- **跨平台支持**：一套代码，同时支持微信小程序和支付宝小程序
- **现代化技术栈**：Taro 3.x + React 18 + TypeScript
- **完整业务功能**：扫码开台、订单管理、支付集成、预约系统
- **平台适配**：统一的平台适配器，处理不同平台的API差异
- **优秀体验**：响应式设计、流畅动画、友好交互

## 🛠️ 技术栈

- **框架**：Taro 3.6.8
- **语言**：TypeScript 4.x
- **UI库**：React 18 + 自定义组件
- **状态管理**：Zustand
- **样式**：Sass + CSS Modules
- **构建工具**：Webpack 5
- **代码规范**：ESLint + Prettier

## 📱 支持平台

- ✅ 微信小程序
- ✅ 支付宝小程序
- 🔄 其他平台（可扩展）

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发调试

```bash
# 微信小程序
npm run dev:weapp

# 支付宝小程序
npm run dev:alipay
```

### 构建发布

```bash
# 构建微信小程序
npm run build:weapp

# 构建支付宝小程序
npm run build:alipay
```

## 📁 项目结构

```
src/
├── app.tsx                 # 应用入口
├── app.config.ts          # 应用配置
├── app.scss               # 全局样式
├── pages/                 # 页面目录
│   ├── index/             # 首页
│   ├── scan/              # 扫码页
│   ├── order/             # 订单页
│   ├── reservation/       # 预约页
│   ├── profile/           # 个人中心
│   └── login/             # 登录页
├── components/            # 公共组件
│   ├── RoomCard/          # 房间卡片
│   ├── OrderItem/         # 订单项
│   └── PaymentModal/      # 支付弹窗
├── services/              # 业务服务层
│   ├── auth.ts            # 认证服务
│   ├── room.ts            # 房间服务
│   ├── scan.ts            # 扫码服务
│   └── payment.ts         # 支付服务
├── utils/                 # 工具函数
│   ├── platform.ts        # 平台判断
│   ├── adapter.ts         # 平台适配器
│   ├── request.ts         # 网络请求
│   ├── storage.ts         # 存储管理
│   └── constants.ts       # 常量定义
├── types/                 # TypeScript类型定义
├── styles/                # 样式文件
│   ├── variables.scss     # 样式变量
│   └── mixins.scss        # 样式混入
└── assets/                # 静态资源
    ├── images/            # 图片资源
    └── icons/             # 图标资源
```

## 🔧 核心功能

### 用户认证
- 微信/支付宝一键登录
- 自动登录和状态维护
- 用户信息管理

### 房间管理
- 房间状态实时展示
- 房间详情查看
- 房间搜索和筛选

### 扫码开台
- 二维码扫描识别
- 房间信息确认
- 多种支付方式

### 订单管理
- 当前订单实时监控
- 历史订单查询
- 订单续费和结算

### 支付系统
- 微信支付/支付宝支付
- 余额支付
- 套餐抵扣

### 预约系统
- 时段预约
- 预约管理
- 预约提醒

## 🎨 设计规范

### 色彩系统
- 主色调：#1E88E5（深蓝色）
- 成功色：#52C41A（绿色）
- 警告色：#FAAD14（橙色）
- 错误色：#FF4D4F（红色）

### 字体规范
- 基础字体：28px
- 小字体：24px
- 大字体：32px
- 标题字体：36px

### 间距规范
- 基础间距：24px
- 小间距：16px
- 大间距：32px
- 超大间距：48px

## 🔌 平台适配

### 适配器模式
项目采用适配器模式处理不同平台的API差异：

```typescript
// 平台适配器接口
interface PlatformAdapter {
  login(): Promise<LoginResult>
  requestPayment(params: PaymentParams): Promise<PaymentResult>
  scanCode(): Promise<ScanResult>
  // ...其他方法
}

// 微信适配器
class WechatAdapter implements PlatformAdapter {
  // 微信特定实现
}

// 支付宝适配器
class AlipayAdapter implements PlatformAdapter {
  // 支付宝特定实现
}
```

### 条件编译
使用 Taro 的条件编译处理平台差异：

```typescript
// 平台判断
if (process.env.TARO_ENV === 'weapp') {
  // 微信小程序特定逻辑
} else if (process.env.TARO_ENV === 'alipay') {
  // 支付宝小程序特定逻辑
}
```

## 📝 开发规范

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码
- 组件和函数添加注释

### 命名规范
- 组件：PascalCase（如 `RoomCard`）
- 文件：kebab-case（如 `room-card.tsx`）
- 变量：camelCase（如 `roomList`）
- 常量：UPPER_SNAKE_CASE（如 `API_BASE_URL`）

### Git 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🧪 测试

```bash
# 运行测试
npm run test

# 测试覆盖率
npm run test:coverage
```

## 📦 构建部署

### 微信小程序
1. 运行 `npm run build:weapp`
2. 使用微信开发者工具打开 `dist/weapp` 目录
3. 预览和上传代码

### 支付宝小程序
1. 运行 `npm run build:alipay`
2. 使用支付宝小程序开发者工具打开 `dist/alipay` 目录
3. 预览和上传代码

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：<EMAIL>

---

**自助麻将室小程序** - 让麻将娱乐更便捷 🀄️
