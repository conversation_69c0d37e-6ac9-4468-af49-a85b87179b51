{"name": "mahjong-miniprogram", "version": "1.0.0", "private": true, "description": "自助麻将室跨平台小程序", "templateInfo": {"name": "taro", "typescript": true, "css": "sass"}, "scripts": {"build:weapp": "taro build --type weapp", "build:alipay": "taro build --type alipay", "build:swan": "taro build --type swan", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "Mahjong System Team", "dependencies": {"@babel/runtime": "^7.21.0", "@tarojs/components": "3.6.8", "@tarojs/helper": "3.6.8", "@tarojs/plugin-platform-weapp": "3.6.8", "@tarojs/plugin-platform-alipay": "3.6.8", "@tarojs/runtime": "3.6.8", "@tarojs/shared": "3.6.8", "@tarojs/taro": "3.6.8", "react": "^18.0.0", "react-dom": "^18.0.0", "zustand": "^4.3.8", "dayjs": "^1.11.7"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/preset-react": "^7.22.0", "@tarojs/cli": "3.6.8", "@tarojs/webpack5-runner": "3.6.8", "@types/webpack-env": "^1.13.6", "@types/react": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "babel-preset-taro": "3.6.8", "eslint": "^8.12.0", "eslint-config-taro": "3.6.8", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-import": "^2.12.0", "prettier": "^2.8.0", "sass": "^1.62.1", "typescript": "^4.1.0", "webpack": "5.78.0"}, "engines": {"node": ">=16"}}