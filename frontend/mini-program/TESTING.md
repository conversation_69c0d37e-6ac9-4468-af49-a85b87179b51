# 🧪 快速测试指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保Node.js版本 >= 16.0.0
node -v

# 进入项目目录
cd frontend/mini-program

# 安装依赖
npm install
```

### 2. 一键测试
```bash
# 运行完整测试套件
./scripts/test.sh

# 或者分步骤测试
./scripts/test.sh type    # 类型检查
./scripts/test.sh lint    # 代码规范
./scripts/test.sh test    # 单元测试
./scripts/test.sh build   # 构建测试
```

## 📱 开发者工具测试

### 微信小程序测试

1. **下载微信开发者工具**
   - 地址：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html

2. **启动开发模式**
   ```bash
   npm run dev:weapp
   ```

3. **导入项目**
   - 打开微信开发者工具
   - 选择"导入项目"
   - 项目目录：`frontend/mini-program/dist/weapp`
   - AppID：选择"测试号"或填入你的AppID

4. **功能测试清单**
   - [ ] 登录页面正常显示
   - [ ] 首页房间列表加载
   - [ ] 扫码功能（模拟器中可能无法测试）
   - [ ] 订单页面显示
   - [ ] 个人中心功能
   - [ ] 页面跳转正常

### 支付宝小程序测试

1. **下载支付宝小程序开发者工具**
   - 地址：https://opendocs.alipay.com/mini/ide/download

2. **启动开发模式**
   ```bash
   npm run dev:alipay
   ```

3. **导入项目**
   - 打开支付宝小程序开发者工具
   - 选择"打开项目"
   - 项目目录：`frontend/mini-program/dist/alipay`
   - AppID：填入测试AppID

4. **功能测试清单**
   - [ ] 登录页面正常显示
   - [ ] 首页房间列表加载
   - [ ] 扫码功能（模拟器中可能无法测试）
   - [ ] 订单页面显示
   - [ ] 个人中心功能
   - [ ] 页面跳转正常

## 🔧 调试技巧

### 1. 控制台调试
```javascript
// 在页面中添加调试信息
console.log('当前平台:', getCurrentPlatform())
console.log('用户信息:', authService.getCurrentUser())
```

### 2. 网络请求调试
```javascript
// 在 utils/request.ts 中添加
console.log('API请求:', { url, method, data })
console.log('API响应:', response)
```

### 3. 平台差异调试
```javascript
// 检查平台特定功能
if (isWeapp()) {
  console.log('微信小程序特定逻辑')
} else if (isAlipay()) {
  console.log('支付宝小程序特定逻辑')
}
```

## 🧪 单元测试

### 运行测试
```bash
# 运行所有测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

### 测试文件结构
```
src/
├── utils/__tests__/
│   ├── platform.test.ts
│   └── index.test.ts
├── components/__tests__/
│   └── RoomCard.test.tsx
└── services/__tests__/
    └── auth.test.ts
```

### 编写测试用例
```typescript
// 组件测试示例
import { render, fireEvent } from '@testing-library/react'
import RoomCard from '../RoomCard'

test('房间卡片点击事件', () => {
  const handleClick = jest.fn()
  const { container } = render(
    <RoomCard room={mockRoom} onClick={handleClick} />
  )
  
  fireEvent.click(container.firstChild!)
  expect(handleClick).toHaveBeenCalledWith(mockRoom)
})
```

## 📱 真机测试

### 微信小程序真机测试

1. **预览测试**
   - 在微信开发者工具中点击"预览"
   - 使用微信扫描二维码
   - 在手机微信中测试

2. **真机调试**
   - 点击"真机调试"
   - 扫码连接手机
   - 查看真机控制台

3. **测试重点**
   - [ ] 扫码功能
   - [ ] 支付功能（需要真实支付配置）
   - [ ] 性能表现
   - [ ] 网络请求

### 支付宝小程序真机测试

1. **预览测试**
   - 在支付宝开发者工具中点击"预览"
   - 使用支付宝扫描二维码
   - 在手机支付宝中测试

2. **真机调试**
   - 点击"真机调试"
   - 扫码连接手机
   - 查看真机控制台

## 🐛 常见问题解决

### 1. 编译错误

**问题：TypeScript编译错误**
```bash
# 解决方案
npm run type-check
# 根据错误信息修复类型问题
```

**问题：依赖安装失败**
```bash
# 解决方案
rm -rf node_modules package-lock.json
npm install
```

### 2. 运行时错误

**问题：平台API调用失败**
```javascript
// 检查平台环境
console.log('当前环境:', process.env.TARO_ENV)
```

**问题：网络请求失败**
```javascript
// 检查网络配置
console.log('API地址:', process.env.API_BASE_URL)
```

### 3. 样式问题

**问题：样式不生效**
```bash
# 重新构建
npm run build:weapp -- --watch
```

**问题：平台样式差异**
```scss
/* 使用条件编译 */
/* #ifdef MP-WEIXIN */
.weixin-style { }
/* #endif */

/* #ifdef MP-ALIPAY */
.alipay-style { }
/* #endif */
```

## 📊 性能测试

### 1. 页面加载性能
```javascript
// 监控页面加载时间
const startTime = Date.now()
useLoad(() => {
  console.log('页面加载时间:', Date.now() - startTime, 'ms')
})
```

### 2. 内存使用监控
```javascript
// 监控内存使用
const memoryInfo = Taro.getSystemInfoSync()
console.log('设备内存信息:', memoryInfo)
```

### 3. 网络请求性能
```javascript
// API调用性能监控
const startTime = Date.now()
await api.call()
console.log('API耗时:', Date.now() - startTime, 'ms')
```

## 🚀 发布前检查

### 1. 构建检查
```bash
# 构建生产版本
npm run build:weapp
npm run build:alipay

# 检查构建产物
ls -la dist/weapp/
ls -la dist/alipay/
```

### 2. 功能检查清单
- [ ] 所有页面正常加载
- [ ] 用户登录流程正常
- [ ] 扫码功能正常
- [ ] 支付流程正常
- [ ] 数据存储正常
- [ ] 错误处理正常
- [ ] 性能表现良好

### 3. 兼容性检查
- [ ] 微信小程序正常运行
- [ ] 支付宝小程序正常运行
- [ ] 不同设备适配正常
- [ ] 不同网络环境正常

## 📋 测试报告

测试完成后，查看生成的报告：
- `reports/test-coverage.txt` - 测试覆盖率报告
- `reports/build-report.md` - 构建报告
- `coverage/` - 详细覆盖率报告（HTML格式）

## 🎯 测试最佳实践

1. **测试驱动开发**：先写测试，再写实现
2. **持续集成**：每次提交都运行测试
3. **真机测试**：定期在真实设备上测试
4. **性能监控**：关注页面加载和API响应时间
5. **错误处理**：测试各种异常情况
6. **用户体验**：从用户角度测试完整流程

---

通过以上测试流程，可以确保跨平台小程序的质量和稳定性。如有问题，请查看详细的测试指南文档。
