<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
        }
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
            margin-top: 15px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .menu-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 菜单功能测试页面</h1>
        
        <div class="test-section">
            <div class="test-title">🔧 问题诊断结果</div>
            <div class="test-item">
                <span>菜单路由过滤逻辑</span>
                <span class="status success">✅ 已修复</span>
            </div>
            <div class="test-item">
                <span>单级菜单显示</span>
                <span class="status success">✅ 已修复</span>
            </div>
            <div class="test-item">
                <span>多级菜单显示</span>
                <span class="status success">✅ 已修复</span>
            </div>
            <div class="test-item">
                <span>隐藏路由过滤</span>
                <span class="status success">✅ 已修复</span>
            </div>
            <div class="test-item">
                <span>路由跳转功能</span>
                <span class="status success">✅ 已修复</span>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 修复的问题</div>
            <div class="test-item">
                <span><strong>问题1:</strong> 菜单路由过滤逻辑错误</span>
                <span class="status error">❌ 原问题</span>
            </div>
            <div style="padding-left: 20px; color: #666; font-size: 14px; margin-bottom: 10px;">
                原因: menuRoutes 计算函数过滤条件不正确，导致路由无法正确显示
            </div>
            
            <div class="test-item">
                <span><strong>问题2:</strong> 单级菜单路径错误</span>
                <span class="status error">❌ 原问题</span>
            </div>
            <div style="padding-left: 20px; color: #666; font-size: 14px; margin-bottom: 10px;">
                原因: 单级菜单的路径处理逻辑有误，导致点击无反应
            </div>
            
            <div class="test-item">
                <span><strong>问题3:</strong> 菜单标题显示错误</span>
                <span class="status error">❌ 原问题</span>
            </div>
            <div style="padding-left: 20px; color: #666; font-size: 14px; margin-bottom: 10px;">
                原因: 菜单标题的获取逻辑复杂，导致显示不正确
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 修复方案</div>
            <div class="test-item">
                <span><strong>修复1:</strong> 重写菜单路由过滤逻辑</span>
                <span class="status success">✅ 已完成</span>
            </div>
            <div style="padding-left: 20px; color: #666; font-size: 14px; margin-bottom: 10px;">
                - 正确过滤Layout路由<br>
                - 处理单子路由的情况<br>
                - 保持路由结构完整性
            </div>
            
            <div class="test-item">
                <span><strong>修复2:</strong> 简化菜单项显示逻辑</span>
                <span class="status success">✅ 已完成</span>
            </div>
            <div style="padding-left: 20px; color: #666; font-size: 14px; margin-bottom: 10px;">
                - 直接使用route.meta.title<br>
                - 简化图标获取逻辑<br>
                - 统一路径处理方式
            </div>
            
            <div class="test-item">
                <span><strong>修复3:</strong> 添加隐藏路由过滤</span>
                <span class="status success">✅ 已完成</span>
            </div>
            <div style="padding-left: 20px; color: #666; font-size: 14px; margin-bottom: 10px;">
                - 过滤meta.hidden为true的路由<br>
                - 保持菜单结构清晰
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 菜单功能测试</div>
            <p>点击下面的按钮测试各个菜单功能：</p>
            
            <div class="menu-test-grid">
                <button class="test-button" onclick="testMenu('/dashboard')">📈 仪表盘</button>
                <button class="test-button" onclick="testMenu('/rooms/list')">🏠 房间列表</button>
                <button class="test-button" onclick="testMenu('/rooms/create')">➕ 添加房间</button>
                <button class="test-button" onclick="testMenu('/orders/list')">📋 订单列表</button>
                <button class="test-button" onclick="testMenu('/users/list')">👥 用户列表</button>
                <button class="test-button" onclick="testMenu('/devices/list')">🔧 设备列表</button>
                <button class="test-button" onclick="testMenu('/devices/control')">🎛️ 设备控制</button>
                <button class="test-button" onclick="testMenu('/finance/report')">💰 财务报表</button>
                <button class="test-button" onclick="testMenu('/finance/income')">📊 收入分析</button>
                <button class="test-button" onclick="testMenu('/platform/orders')">🛒 平台订单</button>
                <button class="test-button" onclick="testMenu('/platform/verification')">✅ 订单核销</button>
                <button class="test-button" onclick="testMenu('/system/settings')">⚙️ 系统设置</button>
                <button class="test-button" onclick="testMenu('/system/pricing')">💲 计费规则</button>
            </div>
            
            <button class="test-button" onclick="loadFullInterface()" style="background: #52c41a; margin-top: 15px;">
                🖥️ 加载完整管理界面
            </button>
            
            <div class="iframe-container">
                <iframe id="interface-frame" src="about:blank"></iframe>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📝 测试说明</div>
            <div style="color: #666; line-height: 1.6;">
                <p><strong>测试步骤：</strong></p>
                <ol>
                    <li>点击"加载完整管理界面"按钮</li>
                    <li>在加载的界面中点击左侧菜单项</li>
                    <li>验证菜单是否能正确跳转到对应页面</li>
                    <li>检查菜单激活状态是否正确</li>
                    <li>测试子菜单的展开和收起功能</li>
                </ol>
                
                <p><strong>预期结果：</strong></p>
                <ul>
                    <li>✅ 所有菜单项都能正常点击</li>
                    <li>✅ 页面能正确跳转到对应路由</li>
                    <li>✅ 菜单激活状态正确显示</li>
                    <li>✅ 子菜单正常展开和收起</li>
                    <li>✅ 面包屑导航正确更新</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testMenu(path) {
            const iframe = document.getElementById('interface-frame');
            if (iframe.src.includes('localhost:3000')) {
                // 如果已经加载了管理界面，直接跳转
                iframe.contentWindow.postMessage({
                    type: 'navigate',
                    path: path
                }, '*');
            } else {
                // 先加载管理界面，然后跳转
                iframe.src = `http://localhost:3000${path}`;
            }
        }

        function loadFullInterface() {
            document.getElementById('interface-frame').src = 'http://localhost:3000';
        }

        // 监听iframe加载完成
        document.getElementById('interface-frame').onload = function() {
            console.log('管理界面加载完成');
        };
    </script>
</body>
</html>
