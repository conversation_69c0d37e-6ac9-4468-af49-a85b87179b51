import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘', icon: 'DataBoard' }
      }
    ]
  },
  {
    path: '/rooms',
    component: Layout,
    redirect: '/rooms/list',
    meta: { title: '房间管理', icon: 'House' },
    children: [
      {
        path: 'list',
        name: 'RoomList',
        component: () => import('@/views/rooms/List.vue'),
        meta: { title: '房间列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'RoomCreate',
        component: () => import('@/views/rooms/Create.vue'),
        meta: { title: '添加房间', icon: 'Plus' }
      },
      {
        path: 'edit/:id',
        name: 'RoomEdit',
        component: () => import('@/views/rooms/Edit.vue'),
        meta: { title: '编辑房间', hidden: true }
      }
    ]
  },
  {
    path: '/orders',
    component: Layout,
    redirect: '/orders/list',
    meta: { title: '订单管理', icon: 'Document' },
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/orders/List.vue'),
        meta: { title: '订单列表', icon: 'List' }
      },
      {
        path: 'detail/:id',
        name: 'OrderDetail',
        component: () => import('@/views/orders/Detail.vue'),
        meta: { title: '订单详情', hidden: true }
      }
    ]
  },
  {
    path: '/users',
    component: Layout,
    redirect: '/users/list',
    meta: { title: '用户管理', icon: 'User' },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/users/List.vue'),
        meta: { title: '用户列表', icon: 'List' }
      }
    ]
  },
  {
    path: '/devices',
    component: Layout,
    redirect: '/devices/list',
    meta: { title: '设备管理', icon: 'Monitor' },
    children: [
      {
        path: 'list',
        name: 'DeviceList',
        component: () => import('@/views/devices/List.vue'),
        meta: { title: '设备管理', icon: 'Monitor' }
      }
    ]
  },
  {
    path: '/packages',
    component: Layout,
    redirect: '/packages/list',
    meta: { title: '套餐管理', icon: 'Box' },
    children: [
      {
        path: 'list',
        name: 'PackageList',
        component: () => import('@/views/packages/List.vue'),
        meta: { title: '套餐列表', icon: 'List' }
      },
      {
        path: 'create',
        name: 'PackageCreate',
        component: () => import('@/views/packages/Create.vue'),
        meta: { title: '新增套餐', icon: 'Plus' }
      },
      {
        path: 'edit/:id',
        name: 'PackageEdit',
        component: () => import('@/views/packages/Edit.vue'),
        meta: { title: '编辑套餐', hidden: true }
      },
      {
        path: 'detail/:id',
        name: 'PackageDetail',
        component: () => import('@/views/packages/Detail.vue'),
        meta: { title: '套餐详情', hidden: true }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/analysis',
    meta: { title: '财务管理', icon: 'Money' },
    children: [
      {
        path: 'analysis',
        name: 'FinanceAnalysis',
        component: () => import('@/views/finance/Report.vue'),
        meta: { title: '财务分析', icon: 'DataAnalysis' }
      }
    ]
  },
  {
    path: '/platform',
    component: Layout,
    redirect: '/platform/orders',
    meta: { title: '外卖平台', icon: 'ShoppingCart' },
    children: [
      {
        path: 'orders',
        name: 'PlatformOrders',
        component: () => import('@/views/platform/Orders.vue'),
        meta: { title: '平台订单', icon: 'List' }
      },
      {
        path: 'verification',
        name: 'OrderVerification',
        component: () => import('@/views/platform/Verification.vue'),
        meta: { title: '订单核销', icon: 'Check' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/settings',
    meta: { title: '系统设置', icon: 'Setting' },
    children: [
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/system/Settings.vue'),
        meta: { title: '系统配置', icon: 'Tools' }
      },
      {
        path: 'pricing',
        name: 'PricingRules',
        component: () => import('@/views/system/Pricing.vue'),
        meta: { title: '计费规则', icon: 'PriceTag' }
      }
    ]
  },
  // 404 页面处理
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/dashboard'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 自助麻将室管理系统`
  }

  // 临时禁用登录检查，用于测试菜单功能
  // TODO: 生产环境需要启用登录验证
  next()

  // 检查登录状态 (已禁用)
  // const token = localStorage.getItem('admin_token')
  // if (to.path !== '/login' && !token) {
  //   next('/login')
  // } else if (to.path === '/login' && token) {
  //   next('/')
  // } else {
  //   next()
  // }
})

export default router
