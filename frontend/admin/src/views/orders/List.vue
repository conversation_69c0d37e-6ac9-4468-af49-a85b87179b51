<template>
  <div class="order-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>订单管理</h2>
        <p>管理所有订单信息和状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="exportOrders">
          <el-icon><Download /></el-icon>
          导出订单
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNumber"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="房间">
          <el-select
            v-model="searchForm.roomId"
            placeholder="请选择房间"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="room in roomOptions"
              :key="room.id"
              :label="room.name"
              :value="room.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="使用中" value="using" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ orderStats.total }}</div>
            <div class="stats-label">总订单数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">¥{{ formatMoney(orderStats.totalAmount) }}</div>
            <div class="stats-label">总金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ orderStats.todayOrders }}</div>
            <div class="stats-label">今日订单</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">¥{{ formatMoney(orderStats.todayAmount) }}</div>
            <div class="stats-label">今日收入</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="orderList"
        stripe
        style="width: 100%"
      >
        <el-table-column label="订单号" width="140">
          <template #default="{ row }">
            <span class="order-number">{{ formatOrderNumber(row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="房间" width="120">
          <template #default="{ row }">
            <span v-if="row.room?.name" class="room-name">{{ row.room.name }}</span>
            <span v-else-if="row.room_id === null" class="room-deleted">房间已删除</span>
            <span v-else class="room-unknown">-</span>
          </template>
        </el-table-column>
        <el-table-column label="用户" width="120">
          <template #default="{ row }">
            {{ row.user?.nickname || row.user?.phone || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="金额" width="100">
          <template #default="{ row }">
            ¥{{ formatMoney(row.total_amount) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusType(row.status)" size="small">
              {{ getOrderStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.start_time) }}
          </template>
        </el-table-column>
        <el-table-column label="结束时间" width="160">
          <template #default="{ row }">
            {{ formatEndTime(row) }}
          </template>
        </el-table-column>
        <el-table-column label="使用时长" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <el-button
              v-if="row.status === 'paid'"
              type="success"
              size="small"
              text
              @click="handleStartOrder(row)"
            >
              开始使用
            </el-button>
            <el-button
              v-if="row.status === 'in_use'"
              type="warning"
              size="small"
              text
              @click="handleCompleteOrder(row)"
            >
              结束订单
            </el-button>
            <el-button
              v-if="row.status === 'paid' || row.status === 'in_use'"
              type="info"
              size="small"
              text
              @click="handleExtendOrder(row)"
            >
              续费
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="danger"
              size="small"
              text
              @click="handleCancelOrder(row)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderList, cancelOrder, completeOrder, extendOrder } from '@/api/orders.js'
import { getRoomList } from '@/api/rooms.js'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const roomOptions = ref([])

// 搜索表单
const searchForm = reactive({
  orderNumber: '',
  roomId: '',
  status: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 订单统计
const orderStats = reactive({
  total: 0,
  totalAmount: 0,
  todayOrders: 0,
  todayAmount: 0
})

// 获取订单列表
const fetchOrderList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      order_number: searchForm.orderNumber,
      room_id: searchForm.roomId,
      status: searchForm.status
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    const response = await getOrderList(params)
    if (response.code === 200) {
      orderList.value = response.data.data || []
      pagination.total = response.data.total || 0

      // 更新统计数据
      orderStats.total = response.data.stats?.total || 0
      orderStats.totalAmount = response.data.stats?.totalAmount || 0
      orderStats.todayOrders = response.data.stats?.todayOrders || 0
      orderStats.todayAmount = response.data.stats?.todayAmount || 0
    } else {
      throw new Error('API响应格式异常')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error(`获取订单列表失败: ${error.message || '网络错误'}`)

    orderList.value = []
    pagination.total = 0
    orderStats.total = 0
    orderStats.totalAmount = 0
    orderStats.todayOrders = 0
    orderStats.todayAmount = 0
  } finally {
    loading.value = false
  }
}

// 获取房间选项
const fetchRoomOptions = async () => {
  try {
    const response = await getRoomList({ page: 1, page_size: 100 })
    if (response && response.code === 200 && response.data) {
      roomOptions.value = response.data.data.map(room => ({
        id: room.id,
        name: room.name,
        room_number: room.room_number
      }))
    } else {
      throw new Error('获取房间选项失败')
    }
  } catch (error) {
    console.error('获取房间选项失败:', error)
    ElMessage.warning('获取房间选项失败，请刷新页面重试')
    roomOptions.value = []
  }
}




// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchOrderList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    orderNumber: '',
    roomId: '',
    status: '',
    dateRange: []
  })
  pagination.page = 1
  fetchOrderList()
}

// 查看详情
const handleViewDetail = (row) => {
  router.push(`/orders/detail/${row.id}`)
}

// 完成订单
const handleCompleteOrder = (row) => {
  ElMessageBox.confirm(
    `确定要结束订单 "${row.orderNumber}" 吗？`,
    '结束确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await completeOrder(row.id)
      ElMessage.success('订单已结束')
      fetchOrderList()
    } catch (error) {
      console.error('结束订单失败:', error)
      ElMessage.error(`结束订单失败: ${error.message || '网络错误'}`)
    }
  })
}

// 取消订单
const handleCancelOrder = (row) => {
  ElMessageBox.confirm(
    `确定要取消订单 "${row.orderNumber}" 吗？`,
    '取消确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await cancelOrder(row.id)
      ElMessage.success('订单已取消')
      fetchOrderList()
    } catch (error) {
      console.error('取消订单失败:', error)
      ElMessage.error(`取消订单失败: ${error.message || '网络错误'}`)
    }
  })
}

// 开始使用订单
const handleStartOrder = (row) => {
  ElMessageBox.confirm(
    `确定要开始使用订单 "#${row.id}" 吗？`,
    '开始确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(async () => {
    try {
      // 这里需要调用开始使用的API
      ElMessage.success('订单已开始使用')
      fetchOrderList()
    } catch (error) {
      console.error('开始使用失败:', error)
      ElMessage.error(`开始使用失败: ${error.message || '网络错误'}`)
    }
  })
}

// 续费订单
const handleExtendOrder = (row) => {
  ElMessageBox.prompt(
    '请输入续费时长（小时）',
    '订单续费',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^\d+$/,
      inputErrorMessage: '请输入有效的小时数'
    }
  ).then(async ({ value }) => {
    try {
      await extendOrder(row.id, { extend_hours: parseInt(value) })
      ElMessage.success('续费成功')
      fetchOrderList()
    } catch (error) {
      console.error('续费失败:', error)
      ElMessage.error(`续费失败: ${error.message || '网络错误'}`)
    }
  })
}

// 导出订单
const exportOrders = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.page = 1
  fetchOrderList()
}

const handleCurrentChange = (val) => {
  pagination.page = val
  fetchOrderList()
}

// 工具函数
const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',    // 待支付 - 橙色
    paid: 'info',         // 已支付 - 灰色
    in_use: 'success',    // 进行中 - 绿色
    completed: 'primary', // 已完成 - 蓝色
    cancelled: 'danger'   // 已取消 - 红色
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    in_use: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const formatMoney = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toFixed(2)
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-')
}

// 格式化结束时间，对进行中的订单显示特殊状态
const formatEndTime = (row) => {
  if (row.end_time) {
    return formatDateTime(row.end_time)
  }

  if (row.status === 'paid' || row.status === 'in_use') {
    return '进行中'
  }

  return '-'
}

// 格式化持续时长
const formatDuration = (minutes) => {
  if (!minutes || minutes <= 0) return '-'

  // 对于异常长的时间（超过24小时），可能是测试数据问题
  if (minutes > 1440) { // 超过24小时
    return '数据异常'
  }

  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60

  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  } else {
    return `${mins}分钟`
  }
}

// 格式化订单号
const formatOrderNumber = (row) => {
  if (row.order_number && row.order_number.trim() !== '') {
    return row.order_number
  }

  // 如果没有订单号，生成一个规范的格式
  const date = new Date(row.created_at)
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '')
  const sequence = String(row.id).padStart(3, '0')
  return `MJ${dateStr}${sequence}`
}

// 初始化
onMounted(() => {
  fetchRoomOptions()
  fetchOrderList()
})
</script>

<style scoped>
.order-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 500;
  color: #262626;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #8c8c8c;
}

.table-card {
  margin-bottom: 20px;
}

.order-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: #1890ff;
}

.room-name {
  color: #262626;
  font-weight: 500;
}

.room-deleted {
  color: #ff4d4f;
  font-style: italic;
  font-size: 12px;
}

.room-unknown {
  color: #8c8c8c;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
  }

  .stats-row .el-col {
    margin-bottom: 16px;
  }
}
</style>
