<template>
  <div class="room-list">
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <div class="header-left">
        <h2>房间管理</h2>
        <p>管理所有房间信息和状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/rooms/create')">
          <el-icon><Plus /></el-icon>
          添加房间
        </el-button>
      </div>
    </div>



    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="房间号">
          <el-input
            v-model="searchForm.roomNumber"
            placeholder="请输入房间号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="房间名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入房间名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="可用" value="available" />
            <el-option label="占用" value="occupied" />
            <el-option label="维护" value="maintenance" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 房间列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="roomList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="roomNumber" label="房间号" width="100" />
        <el-table-column prop="name" label="房间名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getRoomStatusType(row.status)"
              size="small"
            >
              {{ getRoomStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="计费规则" width="120">
          <template #default="{ row }">
            <span v-if="row.pricingRule">
              ¥{{ row.pricingRule.pricePerHour }}/小时
            </span>
            <span v-else class="text-muted">未设置</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="info"
              size="small"
              text
              @click="handleViewDevices(row)"
            >
              设备
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button type="primary" size="small" text>
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="status">修改状态</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 修改状态对话框 -->
    <el-dialog
      v-model="statusDialogVisible"
      title="修改房间状态"
      width="400px"
    >
      <el-form :model="statusForm" label-width="80px">
        <el-form-item label="当前状态">
          <el-tag :type="getRoomStatusType(currentRoom?.status)">
            {{ getRoomStatusText(currentRoom?.status) }}
          </el-tag>
        </el-form-item>
        <el-form-item label="新状态">
          <el-select v-model="statusForm.status" placeholder="请选择状态">
            <el-option label="可用" value="available" />
            <el-option label="占用" value="occupied" />
            <el-option label="维护" value="maintenance" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="statusDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateStatus">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { getRoomList, deleteRoom, updateRoomStatus } from '@/api/rooms.js'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const roomList = ref([])
const currentRoom = ref(null)
const statusDialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  roomNumber: '',
  name: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 状态表单
const statusForm = reactive({
  status: ''
})

// 获取房间列表
const fetchRoomList = async (retryCount = 0) => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      room_number: searchForm.roomNumber,
      name: searchForm.name,
      status: searchForm.status
    }

    const response = await getRoomList(params)

    if (response && response.code === 200 && response.data) {
      // 转换后端数据格式为前端期望格式
      const rooms = response.data.data.map(room => ({
        id: room.id,
        roomNumber: room.room_number,
        name: room.name,
        description: room.description,
        status: room.status,
        pricingRule: room.pricing_rule ? {
          id: room.pricing_rule.id,
          pricePerHour: room.pricing_rule.price_per_hour, // 转换字段名
          name: room.pricing_rule.name
        } : {
          id: 1,
          pricePerHour: 120,
          name: '标准计费'
        },
        facilities: ['空调', '茶具', 'WiFi', '电视'], // 后续可从后端获取
        createdAt: room.created_at,
        updatedAt: room.updated_at
      }))

      roomList.value = rooms
      pagination.total = response.data.total || 0
    } else {
      throw new Error('API响应格式异常')
    }
  } catch (error) {
    console.error('获取房间列表失败:', error)

    // 重试机制
    if (retryCount < 2) {
      console.log(`重试获取房间列表 (${retryCount + 1}/2)`)
      setTimeout(() => {
        fetchRoomList(retryCount + 1)
      }, 1000)
      return
    }

    // 显示错误信息和重试按钮
    ElMessage({
      type: 'error',
      message: `获取房间列表失败: ${error.message || '网络错误'}`,
      duration: 0,
      showClose: true,
      dangerouslyUseHTMLString: true,
      message: `
        <div>获取房间列表失败: ${error.message || '网络错误'}</div>
        <div style="margin-top: 8px;">
          <button onclick="window.location.reload()" style="background: #409eff; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">
            刷新页面
          </button>
        </div>
      `
    })

    roomList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}



// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchRoomList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    roomNumber: '',
    name: '',
    status: ''
  })
  pagination.page = 1
  fetchRoomList()
}

// 编辑房间
const handleEdit = (row) => {
  router.push(`/rooms/edit/${row.id}`)
}

// 查看设备
const handleViewDevices = (row) => {
  console.log('设备按钮被点击，房间信息:', row)
  ElMessage.success(`设备按钮被点击！房间ID: ${row.id}, 房间名称: ${row.name}`)

  // 延迟跳转，先显示消息
  setTimeout(() => {
    try {
      console.log('准备跳转到:', `/devices/list?room_id=${row.id}`)
      router.push(`/devices/list?room_id=${row.id}`)
    } catch (error) {
      console.error('路由跳转失败:', error)
      ElMessage.error('跳转到设备管理页面失败: ' + error.message)
    }
  }, 1000)
}

// 处理下拉菜单命令
const handleCommand = (command, row) => {
  switch (command) {
    case 'status':
      currentRoom.value = row
      statusForm.status = row.status
      statusDialogVisible.value = true
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

// 删除房间
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除房间 "${row.name}" 吗？删除后将无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await deleteRoom(row.id)
      if (response.code === 200) {
        ElMessage.success('房间删除成功')
        // 延迟刷新列表，确保删除操作完成
        setTimeout(() => {
          fetchRoomList()
        }, 500)
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除房间失败:', error)

      // 根据错误状态码提供不同的提示
      let errorMessage = '删除房间失败'

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        if (status === 409) {
          // 冲突错误（有关联数据）
          errorMessage = data.message || '房间有关联数据，无法删除'
        } else if (status === 404) {
          // 房间不存在
          errorMessage = '房间不存在'
        } else if (data && data.message) {
          // 其他业务错误
          errorMessage = data.message
        } else {
          // 网络或其他错误
          errorMessage = `删除失败 (${status})`
        }
      } else {
        errorMessage = '网络连接失败，请检查网络后重试'
      }

      ElMessage.error(errorMessage)
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 更新房间状态
const handleUpdateStatus = async () => {
  try {
    const response = await updateRoomStatus(currentRoom.value.id, statusForm.status)
    if (response.code === 200) {
      ElMessage.success('状态更新成功')
      statusDialogVisible.value = false
      // 延迟刷新列表，确保更新操作完成
      setTimeout(() => {
        fetchRoomList()
      }, 500)
    } else {
      throw new Error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新房间状态失败:', error)
    // 修复：正确显示错误信息
    ElMessage.error(`状态更新失败: ${error.message || '网络错误'}`)
    statusDialogVisible.value = false
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.page = 1
  fetchRoomList()
}

const handleCurrentChange = (val) => {
  pagination.page = val
  fetchRoomList()
}

// 工具函数
const getRoomStatusType = (status) => {
  const types = {
    available: 'success',
    occupied: 'warning',
    maintenance: 'danger'
  }
  return types[status] || 'info'
}

const getRoomStatusText = (status) => {
  const texts = {
    available: '可用',
    occupied: '占用',
    maintenance: '维护中'
  }
  return texts[status] || '未知'
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatMoney = (amount) => {
  if (!amount) return '0'
  return amount.toString()
}

// 初始化
onMounted(() => {
  fetchRoomList()
})
</script>

<style scoped>
.room-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 500;
  color: #262626;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.text-muted {
  color: #8c8c8c;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
  }
  
  .header-right .el-button {
    width: 100%;
  }
}
</style>
