<template>
  <div class="room-create">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>添加房间</h2>
        <p>创建新的房间信息</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 房间表单 -->
    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="roomForm"
        :rules="formRules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房间号" prop="roomNumber">
              <el-input
                v-model="roomForm.roomNumber"
                placeholder="请输入房间号"
                maxlength="10"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房间名称" prop="name">
              <el-input
                v-model="roomForm.name"
                placeholder="请输入房间名称"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="房间描述" prop="description">
          <el-input
            v-model="roomForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入房间描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房间状态" prop="status">
              <el-select v-model="roomForm.status" placeholder="请选择状态">
                <el-option label="可用" value="available" />
                <el-option label="维护中" value="maintenance" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每小时价格" prop="pricePerHour">
              <el-input-number
                v-model="roomForm.pricePerHour"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="房间设施">
          <el-checkbox-group v-model="roomForm.facilities">
            <el-checkbox label="空调">空调</el-checkbox>
            <el-checkbox label="茶具">茶具</el-checkbox>
            <el-checkbox label="按摩椅">按摩椅</el-checkbox>
            <el-checkbox label="电视">电视</el-checkbox>
            <el-checkbox label="WiFi">WiFi</el-checkbox>
            <el-checkbox label="充电器">充电器</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            创建房间
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createRoom } from '@/api/rooms.js'

const router = useRouter()
const formRef = ref(null)
const submitting = ref(false)

// 表单数据
const roomForm = reactive({
  roomNumber: '',
  name: '',
  description: '',
  status: 'available',
  pricePerHour: 120,
  facilities: []
})

// 表单验证规则
const formRules = {
  roomNumber: [
    { required: true, message: '请输入房间号', trigger: 'blur' },
    { min: 1, max: 10, message: '房间号长度在 1 到 10 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入房间名称', trigger: 'blur' },
    { min: 2, max: 50, message: '房间名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择房间状态', trigger: 'change' }
  ],
  pricePerHour: [
    { required: true, message: '请输入每小时价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 构造提交数据 - 匹配后端API格式
    const submitData = {
      room_number: roomForm.roomNumber,
      name: roomForm.name,
      description: roomForm.description,
      pricing_rule_id: 1 // 使用默认计费规则ID
    }

    const response = await createRoom(submitData)
    if (response.code === 200) {
      ElMessage.success('房间创建成功')
      router.push('/rooms/list')
    } else {
      throw new Error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建房间失败:', error)
    ElMessage.error(`创建房间失败: ${error.message || '网络错误，请检查网络连接或联系管理员'}`)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value.resetFields()
  roomForm.facilities = []
}
</script>

<style scoped>
.room-create {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 500;
  color: #262626;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.form-card {
  max-width: 800px;
}

.el-form-item {
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
  }

  .el-row .el-col {
    margin-bottom: 0;
  }
}
</style>
