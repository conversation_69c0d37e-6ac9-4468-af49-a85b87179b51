<template>
  <div class="room-edit">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>编辑房间</h2>
        <p>修改房间信息</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 房间表单 -->
    <el-card v-loading="loading" class="form-card">
      <el-form
        ref="formRef"
        :model="roomForm"
        :rules="formRules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房间号" prop="roomNumber">
              <el-input
                v-model="roomForm.roomNumber"
                placeholder="房间号（编辑时不可修改）"
                maxlength="10"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房间名称" prop="name">
              <el-input
                v-model="roomForm.name"
                placeholder="请输入房间名称"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="房间描述" prop="description">
          <el-input
            v-model="roomForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入房间描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房间状态" prop="status">
              <el-select v-model="roomForm.status" placeholder="请选择状态">
                <el-option label="可用" value="available" />
                <el-option label="占用" value="occupied" />
                <el-option label="维护中" value="maintenance" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每小时价格" prop="pricePerHour">
              <el-input-number
                v-model="roomForm.pricePerHour"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="房间设施">
          <el-checkbox-group v-model="roomForm.facilities">
            <el-checkbox label="空调">空调</el-checkbox>
            <el-checkbox label="茶具">茶具</el-checkbox>
            <el-checkbox label="按摩椅">按摩椅</el-checkbox>
            <el-checkbox label="电视">电视</el-checkbox>
            <el-checkbox label="WiFi">WiFi</el-checkbox>
            <el-checkbox label="充电器">充电器</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            保存修改
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getRoomDetail, updateRoom } from '@/api/rooms.js'
import { createPricingRule } from '@/api/pricing.js'

const route = useRoute()
const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const roomForm = reactive({
  roomNumber: '',
  name: '',
  description: '',
  status: 'available',
  pricePerHour: 120,
  facilities: []
})

// 原始数据备份
let originalData = {}

// 表单验证规则
const formRules = {
  roomNumber: [
    { required: true, message: '请输入房间号', trigger: 'blur' },
    { min: 1, max: 10, message: '房间号长度在 1 到 10 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入房间名称', trigger: 'blur' },
    { min: 2, max: 50, message: '房间名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择房间状态', trigger: 'change' }
  ],
  pricePerHour: [
    { required: true, message: '请输入每小时价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ]
}

// 获取房间详情
const fetchRoomDetail = async () => {
  try {
    loading.value = true
    const roomId = route.params.id

    const response = await getRoomDetail(roomId)
    if (response && response.code === 200 && response.data) {
      const room = response.data
      // 正确处理后端数据格式
      Object.assign(roomForm, {
        roomNumber: room.room_number || '',  // 后端字段是 room_number
        name: room.name || '',
        description: room.description || '',
        status: room.status || 'available',
        pricePerHour: room.pricing_rule?.price_per_hour || 120,  // 后端字段是 price_per_hour
        facilities: room.facilities || []
      })

      // 备份原始数据
      originalData = { ...roomForm }
    } else {
      throw new Error('获取房间详情失败：API响应格式异常')
    }
  } catch (error) {
    console.error('获取房间详情失败:', error)
    ElMessage.error(`获取房间详情失败: ${error.message || '网络错误'}`)
    router.push('/rooms/list')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true
    const roomId = route.params.id

    // 检查是否需要创建新的计费规则
    let pricingRuleId = 1 // 默认计费规则ID

    // 如果价格发生变化，创建新的计费规则
    if (roomForm.pricePerHour !== originalData.pricePerHour) {
      try {
        const pricingRuleData = {
          name: `${roomForm.name}专用计费`,
          price_per_hour: roomForm.pricePerHour,
          overnight_price: 0,
          start_time: "",
          end_time: "",
          is_weekend: false,
          is_holiday: false
        }

        console.log('创建新计费规则:', pricingRuleData)
        const pricingResponse = await createPricingRule(pricingRuleData)

        if (pricingResponse && pricingResponse.code === 200) {
          pricingRuleId = pricingResponse.data.id
          ElMessage.success('已创建新的计费规则')
        } else {
          throw new Error(pricingResponse?.message || '创建计费规则失败')
        }
      } catch (error) {
        console.error('创建计费规则失败:', error)
        ElMessage.error(`创建计费规则失败: ${error.message || '网络错误'}`)
        // 如果创建失败，继续使用默认计费规则
        ElMessage.warning('将使用默认计费规则')
      }
    }

    // 构造提交数据 - 匹配后端API格式
    const submitData = {
      name: roomForm.name,
      description: roomForm.description,
      status: roomForm.status,
      pricing_rule_id: pricingRuleId
    }

    const response = await updateRoom(roomId, submitData)
    if (response.code === 200) {
      ElMessage.success('房间更新成功')
      router.push('/rooms/list')
    } else {
      throw new Error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新房间失败:', error)
    ElMessage.error(`更新房间失败: ${error.message || '网络错误，请检查网络连接或联系管理员'}`)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  Object.assign(roomForm, originalData)
}

// 初始化
onMounted(() => {
  fetchRoomDetail()
})
</script>

<style scoped>
.room-edit {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 500;
  color: #262626;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.form-card {
  max-width: 800px;
}

.el-form-item {
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
  }

  .el-row .el-col {
    margin-bottom: 0;
  }
}
</style>
