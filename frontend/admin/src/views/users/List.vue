<template>
  <div class="user-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>用户管理</h2>
        <p>管理所有用户信息和统计</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="exportUsers">
          <el-icon><Download /></el-icon>
          导出用户
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户昵称">
          <el-input
            v-model="searchForm.nickName"
            placeholder="请输入用户昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ userStats.total }}</div>
            <div class="stats-label">总用户数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ userStats.todayNew }}</div>
            <div class="stats-label">今日新增</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ userStats.activeUsers }}</div>
            <div class="stats-label">活跃用户</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">¥{{ formatMoney(userStats.avgConsumption) }}</div>
            <div class="stats-label">平均消费</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
      >
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar
              :src="row.avatarUrl"
              :alt="row.nickName"
              size="small"
            >
              {{ row.nickName?.charAt(0) || '用' }}
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="昵称" min-width="120" />
        <el-table-column prop="phone" label="手机号" width="140">
          <template #default="{ row }">
            {{ row.phone || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="订单数" width="100">
          <template #default="{ row }">
            {{ row.orderCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="总消费" width="120">
          <template #default="{ row }">
            ¥{{ formatMoney(row.totalConsumption || 0) }}
          </template>
        </el-table-column>
        <el-table-column label="最后登录" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastLoginAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <el-button
              type="info"
              size="small"
              text
              @click="handleViewOrders(row)"
            >
              订单
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="600px"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="头像">
            <el-avatar :src="currentUser.avatarUrl" size="large">
              {{ currentUser.nickName?.charAt(0) || '用' }}
            </el-avatar>
          </el-descriptions-item>
          <el-descriptions-item label="昵称">
            {{ currentUser.nickName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ currentUser.phone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ currentUser.gender === 1 ? '男' : currentUser.gender === 2 ? '女' : '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="订单数量">
            {{ currentUser.orderCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="总消费">
            ¥{{ formatMoney(currentUser.totalConsumption || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ formatDateTime(currentUser.lastLoginAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDateTime(currentUser.createdAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserList } from '@/api/users.js'
import { handleErrorWithRetry, createRetryableApi } from '@/utils/errorHandler.js'

const router = useRouter()

// 创建带重试的API调用（优化配置）
const getUserListWithRetry = createRetryableApi(getUserList, '获取用户列表')

// 响应式数据
const loading = ref(false)
const userList = ref([])
const currentUser = ref(null)
const detailDialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  nickName: '',
  phone: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 用户统计
const userStats = reactive({
  total: 0,
  todayNew: 0,
  activeUsers: 0,
  avgConsumption: 0
})

// 获取用户列表（优化版本，带重试机制）
const fetchUserList = async (showLoading = true) => {
  try {
    if (showLoading) {
      loading.value = true
    }

    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      nickname: searchForm.nickName,  // 修正参数名
      phone: searchForm.phone
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    // 使用带重试的API调用
    const response = await getUserListWithRetry(params)

    if (response.code === 200) {
      // 映射后端字段到前端字段
      const rawUsers = response.data.data || []
      userList.value = rawUsers.map(user => ({
        id: user.id,
        nickName: user.nickname,
        phone: user.phone,
        avatarUrl: user.avatar_url,
        balance: user.balance,
        createdAt: user.created_at,
        orderCount: user.order_count || 0,
        totalConsumption: user.total_consumption || 0,
        lastLoginAt: user.last_login_at
      }))

      pagination.total = response.data.total || 0

      // 更新统计数据
      userStats.total = response.data.stats?.total || 0
      userStats.todayNew = response.data.stats?.todayNew || 0
      userStats.activeUsers = response.data.stats?.activeUsers || 0
      userStats.avgConsumption = response.data.stats?.avgConsumption || 0
    } else {
      throw new Error('API响应格式异常')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)

    // 只有在真正的错误情况下才重置数据
    // 如果是网络延迟等临时问题，保持当前数据不变
    if (error.response?.status >= 500 || !error.response) {
      userList.value = []
      pagination.total = 0
      userStats.total = 0
      userStats.todayNew = 0
      userStats.activeUsers = 0
      userStats.avgConsumption = 0
    }

    // 对于分页请求失败，显示更友好的提示
    if (pagination.page > 1) {
      ElMessage.warning('页面加载失败，请稍后重试')
    }
  } finally {
    if (showLoading) {
      loading.value = false
    }
  }
}




// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchUserList()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    nickName: '',
    phone: '',
    dateRange: []
  })
  pagination.page = 1
  fetchUserList()
}

// 查看详情
const handleViewDetail = (row) => {
  currentUser.value = row
  detailDialogVisible.value = true
}

// 查看订单
const handleViewOrders = (row) => {
  router.push(`/orders?user_id=${row.id}`)
}

// 导出用户
const exportUsers = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.page = 1
  fetchUserList()
}

const handleCurrentChange = (val) => {
  pagination.page = val
  fetchUserList()
}

// 工具函数
const formatMoney = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toFixed(2)
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    pagination.page = 1
    fetchUserList()
  }, 500) // 500ms防抖
}

// 监听搜索条件变化
watch([() => searchForm.nickName, () => searchForm.phone], () => {
  if (searchForm.nickName || searchForm.phone) {
    debouncedSearch()
  }
}, { deep: true })

// 自动刷新功能
let refreshInterval = null
const startAutoRefresh = () => {
  refreshInterval = setInterval(() => {
    fetchUserList(false) // 静默刷新，不显示loading
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}

// 初始化
onMounted(() => {
  fetchUserList()
  startAutoRefresh()
})

// 组件卸载时清理
onUnmounted(() => {
  stopAutoRefresh()
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
})
</script>

<style scoped>
.user-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 500;
  color: #262626;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #8c8c8c;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.user-detail {
  padding: 20px 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
  }

  .stats-row .el-col {
    margin-bottom: 16px;
  }
}
</style>
