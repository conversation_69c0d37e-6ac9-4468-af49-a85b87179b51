<template>
  <div class="finance-analysis">
    <!-- 页面标题和操作区 -->
    <div class="page-header">
      <div class="header-left">
        <h2>财务分析</h2>
        <p class="page-description">全面掌握麻将室经营状况，助力科学决策</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
          class="date-picker"
        />
        <el-button type="primary" @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 核心财务指标卡片 -->
    <el-row :gutter="20" class="metrics-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card revenue">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32"><Money /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">¥{{ formatCurrency(financeData.totalRevenue) }}</div>
              <div class="metric-label">总收入</div>
              <div class="metric-trend" :class="financeData.revenueTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ financeData.revenueTrend > 0 ? '+' : '' }}{{ financeData.revenueTrend.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card orders">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ financeData.totalOrders }}</div>
              <div class="metric-label">总订单数</div>
              <div class="metric-trend" :class="financeData.ordersTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ financeData.ordersTrend > 0 ? '+' : '' }}{{ financeData.ordersTrend.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card avg-order">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32"><PieChart /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">¥{{ formatCurrency(financeData.avgOrderValue) }}</div>
              <div class="metric-label">客单价</div>
              <div class="metric-trend" :class="financeData.avgOrderTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ financeData.avgOrderTrend > 0 ? '+' : '' }}{{ financeData.avgOrderTrend.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card profit">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32"><TrendCharts /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">¥{{ formatCurrency(financeData.estimatedProfit) }}</div>
              <div class="metric-label">预估利润</div>
              <div class="metric-trend" :class="financeData.profitTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ financeData.profitTrend > 0 ? '+' : '' }}{{ financeData.profitTrend.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 收入分析图表区 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 收入趋势图 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">收入趋势分析</span>
              <el-radio-group v-model="revenueChartType" size="small">
                <el-radio-button label="daily">日收入</el-radio-button>
                <el-radio-button label="weekly">周收入</el-radio-button>
                <el-radio-button label="monthly">月收入</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <RevenueChart
              :data="revenueChartData"
              :type="revenueChartType"
              :loading="chartLoading"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 收入来源分布 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">收入来源分布</span>
            </div>
          </template>
          <div class="chart-container">
            <RevenueSourceChart
              :data="revenueSourceData"
              :loading="chartLoading"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 房间经营分析 -->
    <el-row :gutter="20" class="analysis-row">
      <!-- 房间收入排行 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">房间收入排行</span>
              <el-tag type="info" size="small">{{ selectedPeriod }}</el-tag>
            </div>
          </template>
          <div class="room-ranking">
            <div
              v-for="(room, index) in roomRankingData"
              :key="room.roomId"
              class="ranking-item"
              :class="{ 'top-performer': index < 3 }"
            >
              <div class="ranking-info">
                <div class="ranking-number">{{ index + 1 }}</div>
                <div class="room-info">
                  <div class="room-name">{{ room.roomName }}</div>
                  <div class="room-stats">
                    <span class="orders-count">{{ room.ordersCount }}单</span>
                    <span class="usage-rate">使用率{{ room.usageRate }}%</span>
                  </div>
                </div>
              </div>
              <div class="ranking-revenue">
                <div class="revenue-amount">¥{{ formatCurrency(room.revenue) }}</div>
                <div class="revenue-trend" :class="room.trend > 0 ? 'positive' : 'negative'">
                  {{ room.trend > 0 ? '+' : '' }}{{ room.trend.toFixed(1) }}%
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 支出管理 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">支出管理</span>
              <el-button type="primary" size="small" @click="showExpenseDialog">
                <el-icon><Plus /></el-icon>
                添加支出
              </el-button>
            </div>
          </template>
          <div class="expense-summary">
            <!-- 支出分类统计 -->
            <div class="expense-categories">
              <div
                v-for="category in expenseCategories"
                :key="category.type"
                class="expense-category"
              >
                <div class="category-info">
                  <div class="category-icon" :class="category.type">
                    <el-icon><component :is="category.icon" /></el-icon>
                  </div>
                  <div class="category-details">
                    <div class="category-name">{{ category.name }}</div>
                    <div class="category-amount">¥{{ formatCurrency(category.amount) }}</div>
                  </div>
                </div>
                <div class="category-percentage">
                  {{ ((category.amount / financeData.totalExpenses) * 100).toFixed(1) }}%
                </div>
              </div>
            </div>

            <!-- 支出趋势 -->
            <div class="expense-trend">
              <div class="trend-header">
                <span>本期总支出</span>
                <span class="trend-amount">¥{{ formatCurrency(financeData.totalExpenses) }}</span>
              </div>
              <div class="trend-chart">
                <ExpenseTrendChart
                  :data="expenseTrendData"
                  :loading="chartLoading"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 利润分析和经营指标 -->
    <el-row :gutter="20" class="profit-analysis-row">
      <!-- 利润分析 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">利润分析</span>
              <el-radio-group v-model="profitAnalysisType" size="small">
                <el-radio-button label="monthly">月度分析</el-radio-button>
                <el-radio-button label="quarterly">季度分析</el-radio-button>
                <el-radio-button label="yearly">年度分析</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="profit-analysis">
            <!-- 利润构成 -->
            <div class="profit-composition">
              <div class="composition-item revenue-item">
                <div class="item-label">总收入</div>
                <div class="item-value positive">+¥{{ formatCurrency(financeData.totalRevenue) }}</div>
              </div>
              <div class="composition-item expense-item">
                <div class="item-label">总支出</div>
                <div class="item-value negative">-¥{{ formatCurrency(financeData.totalExpenses) }}</div>
              </div>
              <div class="composition-item profit-item">
                <div class="item-label">净利润</div>
                <div class="item-value" :class="financeData.netProfit > 0 ? 'positive' : 'negative'">
                  {{ financeData.netProfit > 0 ? '+' : '' }}¥{{ formatCurrency(Math.abs(financeData.netProfit)) }}
                </div>
              </div>
              <div class="composition-item margin-item">
                <div class="item-label">利润率</div>
                <div class="item-value" :class="financeData.profitMargin > 0 ? 'positive' : 'negative'">
                  {{ financeData.profitMargin.toFixed(2) }}%
                </div>
              </div>
            </div>

            <!-- 利润趋势图 -->
            <div class="profit-chart">
              <ProfitAnalysisChart
                :data="profitAnalysisData"
                :type="profitAnalysisType"
                :loading="chartLoading"
              />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 关键经营指标 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">关键经营指标</span>
            </div>
          </template>
          <div class="key-metrics">
            <div class="metric-item">
              <div class="metric-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-name">平均使用时长</div>
                <div class="metric-number">{{ financeData.avgUsageDuration }}小时</div>
                <div class="metric-desc">每单平均时长</div>
              </div>
            </div>

            <div class="metric-item">
              <div class="metric-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-name">客户复购率</div>
                <div class="metric-number">{{ financeData.customerRetentionRate }}%</div>
                <div class="metric-desc">回头客比例</div>
              </div>
            </div>

            <div class="metric-item">
              <div class="metric-icon">
                <el-icon><House /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-name">房间利用率</div>
                <div class="metric-number">{{ financeData.roomUtilizationRate }}%</div>
                <div class="metric-desc">整体使用效率</div>
              </div>
            </div>

            <div class="metric-item">
              <div class="metric-icon">
                <el-icon><Coin /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-name">每平米收入</div>
                <div class="metric-number">¥{{ formatCurrency(financeData.revenuePerSqm) }}</div>
                <div class="metric-desc">空间收益效率</div>
              </div>
            </div>

            <div class="metric-item">
              <div class="metric-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-name">高峰时段收入占比</div>
                <div class="metric-number">{{ financeData.peakHourRevenueRatio }}%</div>
                <div class="metric-desc">黄金时段效益</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 支出记录对话框 -->
    <el-dialog
      v-model="expenseDialogVisible"
      title="添加支出记录"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="expenseForm" :rules="expenseRules" ref="expenseFormRef" label-width="100px">
        <el-form-item label="支出类型" prop="type">
          <el-select v-model="expenseForm.type" placeholder="请选择支出类型">
            <el-option label="房租费用" value="rent" />
            <el-option label="水电费" value="utilities" />
            <el-option label="设备维护" value="maintenance" />
            <el-option label="人工成本" value="labor" />
            <el-option label="营销推广" value="marketing" />
            <el-option label="其他费用" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="支出金额" prop="amount">
          <el-input-number
            v-model="expenseForm.amount"
            :min="0"
            :precision="2"
            placeholder="请输入支出金额"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="支出日期" prop="date">
          <el-date-picker
            v-model="expenseForm.date"
            type="date"
            placeholder="请选择支出日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注说明" prop="description">
          <el-input
            v-model="expenseForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入支出说明"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="expenseDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitExpense" :loading="expenseSubmitting">
          确定添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Money, Document, PieChart, TrendCharts, Refresh, Download, Plus,
  Timer, User, House, Coin, Clock
} from '@element-plus/icons-vue'
import RevenueChart from '@/components/charts/RevenueChart.vue'
import RevenueSourceChart from '@/components/charts/RevenueSourceChart.vue'
import ExpenseTrendChart from '@/components/charts/ExpenseTrendChart.vue'
import ProfitAnalysisChart from '@/components/charts/ProfitAnalysisChart.vue'
import request from '@/api/request'

// 响应式数据
const loading = ref(false)
const chartLoading = ref(false)
const dateRange = ref([])
const revenueChartType = ref('daily')
const profitAnalysisType = ref('monthly')

// 财务数据
const financeData = reactive({
  totalRevenue: 0,
  totalOrders: 0,
  avgOrderValue: 0,
  estimatedProfit: 0,
  totalExpenses: 0,
  netProfit: 0,
  profitMargin: 0,
  revenueTrend: 0,
  ordersTrend: 0,
  avgOrderTrend: 0,
  profitTrend: 0,
  avgUsageDuration: 0,
  customerRetentionRate: 0,
  roomUtilizationRate: 0,
  revenuePerSqm: 0,
  peakHourRevenueRatio: 0
})

// 图表数据
const revenueChartData = ref([])
const revenueSourceData = ref([])
const expenseTrendData = ref([])
const profitAnalysisData = ref([])
const roomRankingData = ref([])

// 支出分类数据
const expenseCategories = ref([
  { type: 'rent', name: '房租费用', amount: 0, icon: 'House' },
  { type: 'utilities', name: '水电费', amount: 0, icon: 'Lightning' },
  { type: 'maintenance', name: '设备维护', amount: 0, icon: 'Tools' },
  { type: 'labor', name: '人工成本', amount: 0, icon: 'User' },
  { type: 'marketing', name: '营销推广', amount: 0, icon: 'Promotion' },
  { type: 'other', name: '其他费用', amount: 0, icon: 'More' }
])

// 支出对话框
const expenseDialogVisible = ref(false)
const expenseSubmitting = ref(false)
const expenseFormRef = ref(null)
const expenseForm = reactive({
  type: '',
  amount: 0,
  date: '',
  description: ''
})

const expenseRules = {
  type: [{ required: true, message: '请选择支出类型', trigger: 'change' }],
  amount: [{ required: true, message: '请输入支出金额', trigger: 'blur' }],
  date: [{ required: true, message: '请选择支出日期', trigger: 'change' }]
}

// 计算属性
const selectedPeriod = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`
  }
  return '最近30天'
})

// 工具函数
const formatCurrency = (amount) => {
  if (amount === 0) return '0.00'
  return (amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 获取财务数据
const fetchFinanceData = async () => {
  loading.value = true
  try {
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await request({
      url: '/admin/finance/report',
      method: 'get',
      params
    })

    if (response.code === 200) {
      Object.assign(financeData, response.data.summary)
      revenueChartData.value = response.data.revenueChart || []
      revenueSourceData.value = response.data.revenueSource || []
      roomRankingData.value = response.data.roomRanking || []

      // 更新支出分类数据
      if (response.data.expenseCategories) {
        expenseCategories.value.forEach(category => {
          const found = response.data.expenseCategories.find(item => item.type === category.type)
          if (found) {
            category.amount = found.amount
          }
        })
      }
    }
  } catch (error) {
    console.error('获取财务数据失败:', error)
    ElMessage.error('获取财务数据失败')
  } finally {
    loading.value = false
  }
}

// 获取图表数据
const fetchChartData = async () => {
  chartLoading.value = true
  try {
    const params = {
      revenueType: revenueChartType.value,
      profitType: profitAnalysisType.value
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await request({
      url: '/admin/finance/charts',
      method: 'get',
      params
    })

    if (response.code === 200) {
      expenseTrendData.value = response.data.expenseTrend || []
      profitAnalysisData.value = response.data.profitAnalysis || []
    }
  } catch (error) {
    console.error('获取图表数据失败:', error)
  } finally {
    chartLoading.value = false
  }
}

// 事件处理函数
const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    fetchFinanceData()
    fetchChartData()
  }
}

const refreshData = () => {
  fetchFinanceData()
  fetchChartData()
}

const exportReport = async () => {
  try {
    ElMessage.info('正在生成财务报表...')

    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await request({
      url: '/admin/finance/export',
      method: 'get',
      params,
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `财务报表_${selectedPeriod.value}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('财务报表导出成功')
  } catch (error) {
    console.error('导出财务报表失败:', error)
    ElMessage.error('导出财务报表失败')
  }
}

const showExpenseDialog = () => {
  expenseForm.type = ''
  expenseForm.amount = 0
  expenseForm.date = new Date().toISOString().split('T')[0]
  expenseForm.description = ''
  expenseDialogVisible.value = true
}

const submitExpense = async () => {
  try {
    await expenseFormRef.value.validate()

    expenseSubmitting.value = true

    const response = await request({
      url: '/admin/finance/expenses',
      method: 'post',
      data: expenseForm
    })

    if (response.code === 200) {
      ElMessage.success('支出记录添加成功')
      expenseDialogVisible.value = false
      // 刷新数据
      fetchFinanceData()
      fetchChartData()
    }
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      console.error('添加支出记录失败:', error)
      ElMessage.error('添加支出记录失败')
    }
  } finally {
    expenseSubmitting.value = false
  }
}

// 监听图表类型变化
const handleRevenueChartTypeChange = () => {
  fetchChartData()
}

const handleProfitAnalysisTypeChange = () => {
  fetchChartData()
}

// 初始化
onMounted(() => {
  // 设置默认日期范围（最近30天）
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 30)

  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]

  // 获取初始数据
  fetchFinanceData()
  fetchChartData()
})
</script>

<style scoped>
.finance-analysis {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.date-picker {
  width: 280px;
}

/* 核心指标卡片 */
.metrics-row {
  margin-bottom: 24px;
}

.metric-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.metric-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.metric-card.revenue .metric-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.orders .metric-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-card.avg-order .metric-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-card.profit .metric-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
}

.metric-trend.positive {
  color: #10b981;
}

.metric-trend.negative {
  color: #ef4444;
}

.metric-trend .el-icon {
  margin-right: 4px;
}

/* 图表区域 */
.charts-row, .analysis-row, .profit-analysis-row {
  margin-bottom: 24px;
}

.chart-card, .analysis-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.chart-container {
  height: 350px;
  padding: 16px 0;
}

/* 房间排行 */
.room-ranking {
  max-height: 400px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ranking-item:hover {
  background: #e2e8f0;
  transform: translateX(4px);
}

.ranking-item.top-performer {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
}

.ranking-info {
  display: flex;
  align-items: center;
}

.ranking-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
}

.top-performer .ranking-number {
  background: #f59e0b;
}

.room-info {
  flex: 1;
}

.room-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.room-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.ranking-revenue {
  text-align: right;
}

.revenue-amount {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.revenue-trend {
  font-size: 12px;
  font-weight: 600;
}

.revenue-trend.positive {
  color: #10b981;
}

.revenue-trend.negative {
  color: #ef4444;
}

/* 支出管理 */
.expense-summary {
  padding: 8px 0;
}

.expense-categories {
  margin-bottom: 24px;
}

.expense-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
}

.expense-category:last-child {
  border-bottom: none;
}

.category-info {
  display: flex;
  align-items: center;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
}

.category-icon.rent {
  background: #8b5cf6;
}

.category-icon.utilities {
  background: #f59e0b;
}

.category-icon.maintenance {
  background: #ef4444;
}

.category-icon.labor {
  background: #10b981;
}

.category-icon.marketing {
  background: #3b82f6;
}

.category-icon.other {
  background: #6b7280;
}

.category-details {
  flex: 1;
}

.category-name {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.category-amount {
  font-size: 16px;
  font-weight: 700;
  color: #374151;
}

.category-percentage {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
}

.expense-trend {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.trend-amount {
  font-size: 20px;
  font-weight: 700;
  color: #ef4444;
}

.trend-chart {
  height: 120px;
}

/* 利润分析 */
.profit-analysis {
  padding: 8px 0;
}

.profit-composition {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
}

.composition-item {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.item-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.item-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.item-value.positive {
  color: #10b981;
}

.item-value.negative {
  color: #ef4444;
}

.profit-chart {
  height: 300px;
  padding: 16px 0;
}

/* 关键经营指标 */
.key-metrics {
  padding: 8px 0;
}

.metric-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-item .metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.metric-item .metric-content {
  flex: 1;
}

.metric-name {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.metric-number {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2px;
}

.metric-desc {
  font-size: 12px;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .finance-analysis {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .date-picker {
    width: 100%;
  }

  .metric-content {
    flex-direction: column;
    text-align: center;
  }

  .metric-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .profit-composition {
    grid-template-columns: 1fr;
  }

  .ranking-item {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .ranking-info {
    flex-direction: column;
    gap: 8px;
  }

  .chart-container {
    height: 250px;
  }
}

/* 动画效果 */
.metric-card, .chart-card, .analysis-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.room-ranking::-webkit-scrollbar {
  width: 6px;
}

.room-ranking::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.room-ranking::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.room-ranking::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
