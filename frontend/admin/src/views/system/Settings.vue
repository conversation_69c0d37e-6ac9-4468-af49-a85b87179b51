<template>
  <div class="system-settings">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>系统设置</h2>
        <p>管理系统配置和参数</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="saveAllSettings" :loading="saving">
          <el-icon><Check /></el-icon>
          保存所有设置
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 基础设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>基础设置</span>
            </div>
          </template>

          <el-form :model="basicSettings" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="basicSettings.systemName" placeholder="请输入系统名称" />
            </el-form-item>

            <el-form-item label="联系电话">
              <el-input v-model="basicSettings.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>

            <el-form-item label="营业时间">
              <el-time-picker
                v-model="basicSettings.businessHours"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>

            <el-form-item label="自动结算">
              <el-switch
                v-model="basicSettings.autoSettlement"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="预约提前时间">
              <el-input-number
                v-model="basicSettings.reservationAdvanceTime"
                :min="1"
                :max="24"
                controls-position="right"
              />
              <span class="unit">小时</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 计费设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Money /></el-icon>
              <span>计费设置</span>
            </div>
          </template>

          <el-form :model="pricingSettings" label-width="120px">
            <el-form-item label="默认单价">
              <el-input-number
                v-model="pricingSettings.defaultPrice"
                :min="0"
                :precision="2"
                controls-position="right"
              />
              <span class="unit">元/小时</span>
            </el-form-item>

            <el-form-item label="最低消费">
              <el-input-number
                v-model="pricingSettings.minimumConsumption"
                :min="0"
                :precision="2"
                controls-position="right"
              />
              <span class="unit">元</span>
            </el-form-item>

            <el-form-item label="超时费率">
              <el-input-number
                v-model="pricingSettings.overtimeRate"
                :min="1"
                :max="5"
                :precision="1"
                controls-position="right"
              />
              <span class="unit">倍</span>
            </el-form-item>

            <el-form-item label="会员折扣">
              <el-input-number
                v-model="pricingSettings.memberDiscount"
                :min="0.1"
                :max="1"
                :precision="2"
                controls-position="right"
              />
              <span class="unit">折</span>
            </el-form-item>

            <el-form-item label="节假日加价">
              <el-switch
                v-model="pricingSettings.holidayPremium"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 支付设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><CreditCard /></el-icon>
              <span>支付设置</span>
            </div>
          </template>

          <el-form :model="paymentSettings" label-width="120px">
            <el-form-item label="微信支付">
              <el-switch
                v-model="paymentSettings.wechatPay"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="支付宝">
              <el-switch
                v-model="paymentSettings.alipay"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="现金支付">
              <el-switch
                v-model="paymentSettings.cash"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="预付费比例">
              <el-input-number
                v-model="paymentSettings.prepayRatio"
                :min="0.1"
                :max="1"
                :precision="2"
                controls-position="right"
              />
              <span class="unit">倍</span>
            </el-form-item>

            <el-form-item label="退款时限">
              <el-input-number
                v-model="paymentSettings.refundTimeLimit"
                :min="1"
                :max="72"
                controls-position="right"
              />
              <span class="unit">小时</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 设备设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>设备设置</span>
            </div>
          </template>

          <el-form :model="deviceSettings" label-width="120px">
            <el-form-item label="自动开锁">
              <el-switch
                v-model="deviceSettings.autoUnlock"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="自动关灯">
              <el-switch
                v-model="deviceSettings.autoLightOff"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="设备检测间隔">
              <el-input-number
                v-model="deviceSettings.heartbeatInterval"
                :min="10"
                :max="300"
                controls-position="right"
              />
              <span class="unit">秒</span>
            </el-form-item>

            <el-form-item label="离线超时">
              <el-input-number
                v-model="deviceSettings.offlineTimeout"
                :min="60"
                :max="3600"
                controls-position="right"
              />
              <span class="unit">秒</span>
            </el-form-item>

            <el-form-item label="故障报警">
              <el-switch
                v-model="deviceSettings.faultAlert"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 通知设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Bell /></el-icon>
              <span>通知设置</span>
            </div>
          </template>

          <el-form :model="notificationSettings" label-width="120px">
            <el-form-item label="短信通知">
              <el-switch
                v-model="notificationSettings.sms"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="邮件通知">
              <el-switch
                v-model="notificationSettings.email"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="微信通知">
              <el-switch
                v-model="notificationSettings.wechat"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="订单提醒">
              <el-switch
                v-model="notificationSettings.orderAlert"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="设备报警">
              <el-switch
                v-model="notificationSettings.deviceAlert"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 外卖平台设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><ShoppingBag /></el-icon>
              <span>外卖平台</span>
            </div>
          </template>

          <el-form :model="platformSettings" label-width="120px">
            <el-form-item label="美团外卖">
              <el-switch
                v-model="platformSettings.meituan"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="饿了么">
              <el-switch
                v-model="platformSettings.eleme"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="自动核销">
              <el-switch
                v-model="platformSettings.autoVerify"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="核销码长度">
              <el-input-number
                v-model="platformSettings.verifyCodeLength"
                :min="4"
                :max="8"
                controls-position="right"
              />
              <span class="unit">位</span>
            </el-form-item>

            <el-form-item label="订单同步">
              <el-switch
                v-model="platformSettings.orderSync"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getSystemConfig, updateSystemConfig } from '@/api/system.js'

// 响应式数据
const saving = ref(false)

// 基础设置
const basicSettings = reactive({
  systemName: '自助麻将室管理系统',
  contactPhone: '************',
  businessHours: ['09:00', '23:00'],
  autoSettlement: true,
  reservationAdvanceTime: 2
})

// 计费设置
const pricingSettings = reactive({
  defaultPrice: 120.00,
  minimumConsumption: 50.00,
  overtimeRate: 1.5,
  memberDiscount: 0.9,
  holidayPremium: true
})

// 支付设置
const paymentSettings = reactive({
  wechatPay: true,
  alipay: true,
  cash: false,
  prepayRatio: 0.5,
  refundTimeLimit: 24
})

// 设备设置
const deviceSettings = reactive({
  autoUnlock: true,
  autoLightOff: true,
  heartbeatInterval: 30,
  offlineTimeout: 300,
  faultAlert: true
})

// 通知设置
const notificationSettings = reactive({
  sms: true,
  email: false,
  wechat: true,
  orderAlert: true,
  deviceAlert: true
})

// 外卖平台设置
const platformSettings = reactive({
  meituan: true,
  eleme: true,
  autoVerify: false,
  verifyCodeLength: 6,
  orderSync: true
})

// 获取系统配置
const fetchSystemConfig = async () => {
  try {
    const response = await getSystemConfig()

    if (response.code === 200) {
      const config = response.data

      // 更新各个设置对象
      Object.assign(basicSettings, config.basic || {})
      Object.assign(pricingSettings, config.pricing || {})
      Object.assign(paymentSettings, config.payment || {})
      Object.assign(deviceSettings, config.device || {})
      Object.assign(notificationSettings, config.notification || {})
      Object.assign(platformSettings, config.platform || {})
    }
  } catch (error) {
    console.error('获取系统配置失败:', error)
    ElMessage.warning('获取系统配置失败，使用默认配置')
  }
}

// 保存所有设置
const saveAllSettings = async () => {
  try {
    saving.value = true

    const config = {
      basic: basicSettings,
      pricing: pricingSettings,
      payment: paymentSettings,
      device: deviceSettings,
      notification: notificationSettings,
      platform: platformSettings
    }

    const response = await updateSystemConfig(config)

    if (response.code === 200) {
      ElMessage.success('系统配置保存成功')
    } else {
      throw new Error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存系统配置失败:', error)
    ElMessage.error(`保存系统配置失败: ${error.message || '网络错误'}`)
  } finally {
    saving.value = false
  }
}

// 初始化
onMounted(() => {
  fetchSystemConfig()
})
</script>

<style scoped>
.system-settings {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 500;
  color: #262626;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.settings-card {
  margin-bottom: 20px;
  height: fit-content;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.card-header .el-icon {
  color: #409eff;
}

.unit {
  margin-left: 8px;
  color: #8c8c8c;
  font-size: 14px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input-number {
  width: 120px;
}

.el-time-picker {
  width: 100%;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
  }

  .el-row .el-col {
    margin-bottom: 20px;
  }
}
</style>
