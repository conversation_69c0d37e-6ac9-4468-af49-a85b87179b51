<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>计费规则</h2>
        <p>管理房间计费规则和价格策略</p>
      </div>
      <div class="header-right">
        <el-button type="primary">
          <el-icon><Plus /></el-icon>
          添加规则
        </el-button>
      </div>
    </div>

    <!-- 计费规则列表 -->
    <el-card class="table-card">
      <el-table :data="pricingRules" v-loading="loading">
        <el-table-column prop="name" label="规则名称" width="200" />
        <el-table-column prop="roomType" label="房间类型" width="120" />
        <el-table-column prop="pricePerHour" label="每小时价格" width="120">
          <template #default="{ row }">
            ¥{{ row.pricePerHour }}
          </template>
        </el-table-column>
        <el-table-column prop="timeSlot" label="时间段" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editRule(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteRule(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const pricingRules = ref([
  {
    id: 1,
    name: '标准计费',
    roomType: '普通房',
    pricePerHour: 30,
    timeSlot: '全天',
    status: 'active'
  },
  {
    id: 2,
    name: '高峰计费',
    roomType: '豪华房',
    pricePerHour: 50,
    timeSlot: '18:00-22:00',
    status: 'active'
  }
])

// 方法
const editRule = (rule) => {
  console.log('编辑规则:', rule)
}

const deleteRule = (rule) => {
  console.log('删除规则:', rule)
}

onMounted(() => {
  // 页面加载时的初始化
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.table-card {
  margin-top: 20px;
}
</style>
