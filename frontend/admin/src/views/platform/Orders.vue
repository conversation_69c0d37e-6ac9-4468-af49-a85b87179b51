<template>
  <div class="platform-orders">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>外卖订单管理</h2>
        <p class="page-description">管理美团、饿了么等外卖平台订单，提供便捷的核销和统计功能</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" @click="showBatchVerifyDialog">
          <el-icon><Check /></el-icon>
          批量核销
        </el-button>
        <el-button @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon meituan">
              <el-icon><ShoppingCart /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ orderStats.meituan.count }}</div>
              <div class="stat-label">美团订单</div>
              <div class="stat-amount">¥{{ formatCurrency(orderStats.meituan.amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon eleme">
              <el-icon><ShoppingBag /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ orderStats.eleme.count }}</div>
              <div class="stat-label">饿了么订单</div>
              <div class="stat-amount">¥{{ formatCurrency(orderStats.eleme.amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ orderStats.pending.count }}</div>
              <div class="stat-label">待核销</div>
              <div class="stat-amount">¥{{ formatCurrency(orderStats.pending.amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon verified">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ orderStats.verified.count }}</div>
              <div class="stat-label">已核销</div>
              <div class="stat-amount">¥{{ formatCurrency(orderStats.verified.amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" :inline="true" class="filter-form">
        <el-form-item label="平台类型">
          <el-select v-model="filterForm.platformType" placeholder="全部平台" clearable>
            <el-option label="美团" value="meituan" />
            <el-option label="饿了么" value="eleme" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="filterForm.orderStatus" placeholder="全部状态" clearable>
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="核销状态">
          <el-select v-model="filterForm.verificationStatus" placeholder="全部状态" clearable>
            <el-option label="待核销" value="pending" />
            <el-option label="已核销" value="verified" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>
        <el-form-item label="房间号">
          <el-input v-model="filterForm.roomNumber" placeholder="输入房间号" clearable />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="filterForm.platformOrderId" placeholder="输入平台订单号" clearable />
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchOrders">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilter">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">订单列表</span>
          <div class="table-actions">
            <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
            <el-button
              type="danger"
              size="small"
              :disabled="selectedOrders.length === 0"
              @click="batchDelete"
            >
              批量删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="orderList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="platformOrderId" label="平台订单号" width="180">
          <template #default="{ row }">
            <div class="order-id">
              <el-tag :type="getPlatformTagType(row.platformType)" size="small">
                {{ getPlatformName(row.platformType) }}
              </el-tag>
              <span class="order-number">{{ row.platformOrderId }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="roomInfo" label="房间信息" width="120">
          <template #default="{ row }">
            <div class="room-info">
              <div class="room-number">{{ row.roomNumber }}号房</div>
              <div class="room-status" :class="row.roomStatus">
                {{ getRoomStatusText(row.roomStatus) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="orderAmount" label="订单金额" width="120">
          <template #default="{ row }">
            <div class="amount-info">
              <div class="original-amount">原价: ¥{{ formatCurrency(row.originalAmount) }}</div>
              <div class="paid-amount">实付: ¥{{ formatCurrency(row.paidAmount) }}</div>
              <div v-if="row.discountAmount > 0" class="discount-amount">
                优惠: -¥{{ formatCurrency(row.discountAmount) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" label="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusType(row.orderStatus)">
              {{ getOrderStatusText(row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="verificationStatus" label="核销状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getVerificationStatusType(row.verificationStatus)">
              {{ getVerificationStatusText(row.verificationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="customerInfo" label="客户信息" width="150">
          <template #default="{ row }">
            <div class="customer-info">
              <div class="customer-name">{{ row.customerName || '未知' }}</div>
              <div class="customer-phone">{{ row.customerPhone || '未提供' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="下单时间" width="160">
          <template #default="{ row }">
            <div class="time-info">
              <div class="create-time">{{ formatDateTime(row.createdAt) }}</div>
              <div v-if="row.verifiedAt" class="verify-time">
                核销: {{ formatDateTime(row.verifiedAt) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="row.verificationStatus === 'pending'"
                type="primary"
                size="small"
                @click="verifyOrder(row)"
              >
                核销
              </el-button>
              <el-button
                v-if="row.verificationStatus === 'verified'"
                type="warning"
                size="small"
                @click="refundOrder(row)"
              >
                退款
              </el-button>
              <el-button size="small" @click="viewOrderDetail(row)">
                详情
              </el-button>
              <el-dropdown @command="handleMoreAction">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'edit', row}">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{action: 'delete', row}" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量核销对话框 -->
    <el-dialog
      v-model="batchVerifyDialogVisible"
      title="批量核销订单"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-verify-content">
        <el-alert
          title="批量核销提醒"
          type="warning"
          description="请确认选中的订单都已完成配送，核销后将无法撤销"
          show-icon
          :closable="false"
        />
        <div class="selected-orders">
          <h4>选中的订单 ({{ selectedOrders.length }}单)</h4>
          <div class="order-list">
            <div v-for="order in selectedOrders" :key="order.id" class="order-item">
              <span class="platform-tag">{{ getPlatformName(order.platformType) }}</span>
              <span class="order-id">{{ order.platformOrderId }}</span>
              <span class="room-number">{{ order.roomNumber }}号房</span>
              <span class="amount">¥{{ formatCurrency(order.paidAmount) }}</span>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="batchVerifyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchVerify" :loading="batchVerifyLoading">
          确认核销
        </el-button>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="orderDetailDialogVisible"
      title="订单详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="平台订单号">
            {{ currentOrder.platformOrderId }}
          </el-descriptions-item>
          <el-descriptions-item label="平台类型">
            <el-tag :type="getPlatformTagType(currentOrder.platformType)">
              {{ getPlatformName(currentOrder.platformType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="房间信息">
            {{ currentOrder.roomNumber }}号房 ({{ getRoomStatusText(currentOrder.roomStatus) }})
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(currentOrder.orderStatus)">
              {{ getOrderStatusText(currentOrder.orderStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核销状态">
            <el-tag :type="getVerificationStatusType(currentOrder.verificationStatus)">
              {{ getVerificationStatusText(currentOrder.verificationStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="原价金额">
            ¥{{ formatCurrency(currentOrder.originalAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="优惠金额">
            ¥{{ formatCurrency(currentOrder.discountAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="实付金额">
            ¥{{ formatCurrency(currentOrder.paidAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="下单时间">
            {{ formatDateTime(currentOrder.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="核销时间">
            {{ currentOrder.verifiedAt ? formatDateTime(currentOrder.verifiedAt) : '未核销' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户姓名">
            {{ currentOrder.customerName || '未提供' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户电话">
            {{ currentOrder.customerPhone || '未提供' }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentOrder.orderItems && currentOrder.orderItems.length > 0" class="order-items">
          <h4>订单商品</h4>
          <el-table :data="currentOrder.orderItems" border>
            <el-table-column prop="name" label="商品名称" />
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column prop="price" label="单价" width="100">
              <template #default="{ row }">
                ¥{{ formatCurrency(row.price) }}
              </template>
            </el-table-column>
            <el-table-column prop="total" label="小计" width="100">
              <template #default="{ row }">
                ¥{{ formatCurrency(row.quantity * row.price) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <template #footer>
        <el-button @click="orderDetailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentOrder && currentOrder.verificationStatus === 'pending'"
          type="primary"
          @click="verifyOrder(currentOrder)"
        >
          立即核销
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Check, Download, Search, RefreshLeft, ShoppingCart, ShoppingBag,
  Clock, CircleCheck, ArrowDown
} from '@element-plus/icons-vue'
import request from '@/api/request'
import { createRetryableApi } from '@/utils/errorHandler'

// 创建带重试的API调用
const getPlatformOrdersWithRetry = createRetryableApi(
  (params) => request.get('/admin/platform/orders', { params }),
  '获取外卖平台订单列表'
)

// 响应式数据
const loading = ref(false)
const selectAll = ref(false)
const selectedOrders = ref([])
const batchVerifyDialogVisible = ref(false)
const batchVerifyLoading = ref(false)
const orderDetailDialogVisible = ref(false)
const currentOrder = ref(null)

// 订单统计数据
const orderStats = reactive({
  meituan: { count: 0, amount: 0 },
  eleme: { count: 0, amount: 0 },
  pending: { count: 0, amount: 0 },
  verified: { count: 0, amount: 0 }
})

// 筛选表单
const filterForm = reactive({
  platformType: '',
  orderStatus: '',
  verificationStatus: '',
  roomNumber: '',
  platformOrderId: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 订单列表
const orderList = ref([])

// 工具函数
const formatCurrency = (amount) => {
  if (amount === 0) return '0.00'
  return (amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getPlatformName = (type) => {
  const names = {
    meituan: '美团',
    eleme: '饿了么'
  }
  return names[type] || type
}

const getPlatformTagType = (type) => {
  const types = {
    meituan: 'warning',
    eleme: 'primary'
  }
  return types[type] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getOrderStatusType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getVerificationStatusText = (status) => {
  const texts = {
    pending: '待核销',
    verified: '已核销',
    refunded: '已退款'
  }
  return texts[status] || status
}

const getVerificationStatusType = (status) => {
  const types = {
    pending: 'warning',
    verified: 'success',
    refunded: 'danger'
  }
  return types[status] || 'info'
}

const getRoomStatusText = (status) => {
  const texts = {
    available: '空闲',
    occupied: '使用中',
    maintenance: '维护中'
  }
  return texts[status] || status
}

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...filterForm
    }

    // 处理日期范围
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_date = filterForm.dateRange[0]
      params.end_date = filterForm.dateRange[1]
    }

    const response = await getPlatformOrdersWithRetry(params)

    if (response.code === 200) {
      orderList.value = response.data.data || []
      pagination.total = response.data.total || 0

      // 更新统计数据
      updateOrderStats(response.data.stats)
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 更新订单统计
const updateOrderStats = (stats) => {
  if (stats) {
    Object.assign(orderStats, stats)
  }
}

// 事件处理函数
const refreshData = () => {
  fetchOrderList()
}

const searchOrders = () => {
  pagination.page = 1
  fetchOrderList()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    platformType: '',
    orderStatus: '',
    verificationStatus: '',
    roomNumber: '',
    platformOrderId: '',
    dateRange: []
  })
  pagination.page = 1
  fetchOrderList()
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedOrders.value = [...orderList.value]
  } else {
    selectedOrders.value = []
  }
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
  selectAll.value = selection.length === orderList.value.length
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchOrderList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchOrderList()
}

// 核销订单
const verifyOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确认核销订单 ${order.platformOrderId}？`,
      '核销确认',
      {
        confirmButtonText: '确认核销',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await request({
      url: '/admin/platform/verify',
      method: 'post',
      data: {
        platformOrderId: order.platformOrderId,
        verificationCode: order.verificationCode || ''
      }
    })

    if (response.code === 200) {
      ElMessage.success('订单核销成功')
      fetchOrderList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('核销订单失败:', error)
      ElMessage.error('核销订单失败')
    }
  }
}

// 退款订单
const refundOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确认退款订单 ${order.platformOrderId}？退款后将无法撤销。`,
      '退款确认',
      {
        confirmButtonText: '确认退款',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await request({
      url: `/admin/platform/orders/${order.id}/refund`,
      method: 'post'
    })

    if (response.code === 200) {
      ElMessage.success('订单退款成功')
      fetchOrderList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退款订单失败:', error)
      ElMessage.error('退款订单失败')
    }
  }
}

// 查看订单详情
const viewOrderDetail = async (order) => {
  try {
    const response = await request({
      url: `/admin/platform/orders/${order.id}`,
      method: 'get'
    })

    if (response.code === 200) {
      currentOrder.value = response.data
      orderDetailDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

// 批量核销
const showBatchVerifyDialog = () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要核销的订单')
    return
  }

  const pendingOrders = selectedOrders.value.filter(order => order.verificationStatus === 'pending')
  if (pendingOrders.length === 0) {
    ElMessage.warning('选中的订单中没有待核销的订单')
    return
  }

  selectedOrders.value = pendingOrders
  batchVerifyDialogVisible.value = true
}

const confirmBatchVerify = async () => {
  batchVerifyLoading.value = true
  try {
    const orderIds = selectedOrders.value.map(order => order.platformOrderId)

    const response = await request({
      url: '/admin/platform/batch-verify',
      method: 'post',
      data: { orderIds: orderIds }
    })

    if (response.code === 200) {
      ElMessage.success(`成功核销 ${orderIds.length} 个订单`)
      batchVerifyDialogVisible.value = false
      selectedOrders.value = []
      selectAll.value = false
      fetchOrderList()
    }
  } catch (error) {
    console.error('批量核销失败:', error)
    ElMessage.error('批量核销失败')
  } finally {
    batchVerifyLoading.value = false
  }
}

// 更多操作
const handleMoreAction = async ({ action, row }) => {
  switch (action) {
    case 'edit':
      // 编辑订单逻辑
      ElMessage.info('编辑功能开发中')
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确认删除订单 ${row.platformOrderId}？`,
          '删除确认',
          {
            confirmButtonText: '确认删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const response = await request({
          url: `/admin/platform/orders/${row.id}`,
          method: 'delete'
        })

        if (response.code === 200) {
          ElMessage.success('订单删除成功')
          fetchOrderList()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除订单失败:', error)
          ElMessage.error('删除订单失败')
        }
      }
      break
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要删除的订单')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedOrders.value.length} 个订单？`,
      '批量删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const orderIds = selectedOrders.value.map(order => order.id)

    const response = await request({
      url: '/admin/platform/orders/batch-delete',
      method: 'post',
      data: { order_ids: orderIds }
    })

    if (response.code === 200) {
      ElMessage.success(`成功删除 ${orderIds.length} 个订单`)
      selectedOrders.value = []
      selectAll.value = false
      fetchOrderList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 导出数据
const exportData = async () => {
  try {
    ElMessage.info('正在生成导出文件...')

    const params = { ...filterForm }
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_date = filterForm.dateRange[0]
      params.end_date = filterForm.dateRange[1]
    }

    const response = await request({
      url: '/admin/platform/orders/export',
      method: 'get',
      params,
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `外卖订单数据_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 初始化
onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped>
.platform-orders {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 24px;
}

.stat-icon.meituan {
  background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.stat-icon.eleme {
  background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ff7043 0%, #ff5722 100%);
}

.stat-icon.verified {
  background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-amount {
  font-size: 16px;
  font-weight: 600;
  color: #059669;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 24px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

/* 表格卡片 */
.table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格内容样式 */
.order-id {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-number {
  font-family: monospace;
  font-size: 12px;
  color: #6b7280;
}

.room-info {
  text-align: center;
}

.room-number {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.room-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.room-status.available {
  background: #d1fae5;
  color: #065f46;
}

.room-status.occupied {
  background: #fef3c7;
  color: #92400e;
}

.room-status.maintenance {
  background: #fee2e2;
  color: #991b1b;
}

.amount-info {
  text-align: right;
}

.original-amount {
  font-size: 12px;
  color: #6b7280;
}

.paid-amount {
  font-weight: 600;
  color: #1f2937;
  margin: 2px 0;
}

.discount-amount {
  font-size: 12px;
  color: #dc2626;
}

.customer-info {
  font-size: 12px;
}

.customer-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.customer-phone {
  color: #6b7280;
}

.time-info {
  font-size: 12px;
}

.create-time {
  color: #1f2937;
  margin-bottom: 2px;
}

.verify-time {
  color: #059669;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 对话框样式 */
.batch-verify-content {
  padding: 16px 0;
}

.selected-orders {
  margin-top: 20px;
}

.selected-orders h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
}

.order-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f3f4f6;
}

.order-item:last-child {
  border-bottom: none;
}

.platform-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #f3f4f6;
  color: #6b7280;
}

.order-detail {
  padding: 16px 0;
}

.order-items {
  margin-top: 24px;
}

.order-items h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .platform-orders {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    margin-bottom: 16px;
  }

  .table-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* 动画效果 */
.stat-card, .filter-card, .table-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
