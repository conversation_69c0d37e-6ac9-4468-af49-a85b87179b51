<template>
  <div class="platform-analytics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>平台数据分析</h2>
        <p class="page-description">分析外卖平台收入趋势，优化经营策略</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
          class="date-picker"
        />
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <el-row :gutter="20" class="metrics-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card revenue">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">¥{{ formatCurrency(analyticsData.totalRevenue) }}</div>
              <div class="metric-label">总收入</div>
              <div class="metric-trend" :class="analyticsData.revenueTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ analyticsData.revenueTrend > 0 ? '+' : '' }}{{ analyticsData.revenueTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card orders">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ analyticsData.totalOrders }}</div>
              <div class="metric-label">总订单数</div>
              <div class="metric-trend" :class="analyticsData.ordersTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ analyticsData.ordersTrend > 0 ? '+' : '' }}{{ analyticsData.ordersTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card avg-order">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><PieChart /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">¥{{ formatCurrency(analyticsData.avgOrderValue) }}</div>
              <div class="metric-label">客单价</div>
              <div class="metric-trend" :class="analyticsData.avgOrderTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ analyticsData.avgOrderTrend > 0 ? '+' : '' }}{{ analyticsData.avgOrderTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="metric-card conversion">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ analyticsData.conversionRate }}%</div>
              <div class="metric-label">转化率</div>
              <div class="metric-trend" :class="analyticsData.conversionTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                {{ analyticsData.conversionTrend > 0 ? '+' : '' }}{{ analyticsData.conversionTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表分析区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 收入趋势图 -->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">收入趋势分析</span>
              <el-radio-group v-model="revenueChartType" size="small">
                <el-radio-button label="daily">日收入</el-radio-button>
                <el-radio-button label="weekly">周收入</el-radio-button>
                <el-radio-button label="monthly">月收入</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <PlatformRevenueChart 
              :data="revenueChartData" 
              :type="revenueChartType"
              :loading="chartLoading"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 平台对比 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">平台收入对比</span>
            </div>
          </template>
          <div class="chart-container">
            <PlatformComparisonChart 
              :data="platformComparisonData"
              :loading="chartLoading"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细分析区域 -->
    <el-row :gutter="20" class="analysis-row">
      <!-- 平台表现分析 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">平台表现分析</span>
              <el-tag type="info" size="small">{{ selectedPeriod }}</el-tag>
            </div>
          </template>
          <div class="platform-performance">
            <div 
              v-for="platform in platformPerformance" 
              :key="platform.type"
              class="performance-item"
            >
              <div class="platform-info">
                <div class="platform-header">
                  <el-tag :type="getPlatformTagType(platform.type)" size="small">
                    {{ getPlatformName(platform.type) }}
                  </el-tag>
                  <span class="market-share">市场份额 {{ platform.marketShare }}%</span>
                </div>
                <div class="platform-metrics">
                  <div class="metric-item">
                    <span class="metric-name">订单数</span>
                    <span class="metric-value">{{ platform.orders }}单</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-name">收入</span>
                    <span class="metric-value">¥{{ formatCurrency(platform.revenue) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-name">客单价</span>
                    <span class="metric-value">¥{{ formatCurrency(platform.avgOrderValue) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-name">增长率</span>
                    <span class="metric-value" :class="platform.growthRate > 0 ? 'positive' : 'negative'">
                      {{ platform.growthRate > 0 ? '+' : '' }}{{ platform.growthRate }}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 时段分析 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">时段分析</span>
            </div>
          </template>
          <div class="time-analysis">
            <div class="time-chart">
              <TimeDistributionChart 
                :data="timeDistributionData"
                :loading="chartLoading"
              />
            </div>
            <div class="peak-hours">
              <h4>高峰时段</h4>
              <div class="peak-list">
                <div 
                  v-for="peak in peakHours" 
                  :key="peak.hour"
                  class="peak-item"
                >
                  <span class="peak-time">{{ peak.hour }}:00</span>
                  <span class="peak-orders">{{ peak.orders }}单</span>
                  <span class="peak-revenue">¥{{ formatCurrency(peak.revenue) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 房间关联分析 -->
    <el-card class="room-analysis-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">房间关联分析</span>
          <el-button size="small" @click="refreshRoomAnalysis">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table :data="roomAnalysisData" v-loading="roomAnalysisLoading" stripe>
        <el-table-column prop="roomNumber" label="房间号" width="100">
          <template #default="{ row }">
            {{ row.roomNumber }}号房
          </template>
        </el-table-column>
        <el-table-column prop="roomName" label="房间名称" width="150" />
        <el-table-column prop="totalOrders" label="外卖订单数" width="120">
          <template #default="{ row }">
            {{ row.totalOrders }}单
          </template>
        </el-table-column>
        <el-table-column prop="totalRevenue" label="外卖收入" width="120">
          <template #default="{ row }">
            ¥{{ formatCurrency(row.totalRevenue) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgOrderValue" label="平均客单价" width="120">
          <template #default="{ row }">
            ¥{{ formatCurrency(row.avgOrderValue) }}
          </template>
        </el-table-column>
        <el-table-column prop="orderFrequency" label="订餐频率" width="120">
          <template #default="{ row }">
            {{ row.orderFrequency }}%
          </template>
        </el-table-column>
        <el-table-column prop="preferredPlatform" label="偏好平台" width="120">
          <template #default="{ row }">
            <el-tag :type="getPlatformTagType(row.preferredPlatform)" size="small">
              {{ getPlatformName(row.preferredPlatform) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="peakHour" label="高峰时段" width="120" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewRoomDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, Download, Money, Document, PieChart, TrendCharts, DataAnalysis
} from '@element-plus/icons-vue'
import PlatformRevenueChart from '@/components/charts/PlatformRevenueChart.vue'
import PlatformComparisonChart from '@/components/charts/PlatformComparisonChart.vue'
import TimeDistributionChart from '@/components/charts/TimeDistributionChart.vue'
import request from '@/api/request'

// 响应式数据
const chartLoading = ref(false)
const roomAnalysisLoading = ref(false)
const dateRange = ref([])
const revenueChartType = ref('daily')

// 分析数据
const analyticsData = reactive({
  totalRevenue: 0,
  totalOrders: 0,
  avgOrderValue: 0,
  conversionRate: 0,
  revenueTrend: 0,
  ordersTrend: 0,
  avgOrderTrend: 0,
  conversionTrend: 0
})

// 图表数据
const revenueChartData = ref([])
const platformComparisonData = ref([])
const timeDistributionData = ref([])

// 平台表现数据
const platformPerformance = ref([])

// 高峰时段数据
const peakHours = ref([])

// 房间关联分析数据
const roomAnalysisData = ref([])

// 计算属性
const selectedPeriod = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`
  }
  return '最近30天'
})

// 工具函数
const formatCurrency = (amount) => {
  if (amount === 0) return '0.00'
  return (amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getPlatformName = (type) => {
  const names = {
    meituan: '美团',
    eleme: '饿了么'
  }
  return names[type] || type
}

const getPlatformTagType = (type) => {
  const types = {
    meituan: 'warning',
    eleme: 'primary'
  }
  return types[type] || 'info'
}

// 获取分析数据
const fetchAnalyticsData = async () => {
  chartLoading.value = true
  try {
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await request({
      url: '/admin/platform/analytics',
      method: 'get',
      params
    })

    if (response.code === 200) {
      const data = response.data
      Object.assign(analyticsData, data.summary)
      revenueChartData.value = data.revenueChart || []
      platformComparisonData.value = data.platformComparison || []
      timeDistributionData.value = data.timeDistribution || []
      platformPerformance.value = data.platformPerformance || []
      peakHours.value = data.peakHours || []
    }
  } catch (error) {
    console.error('获取分析数据失败:', error)
    ElMessage.error('获取分析数据失败')
  } finally {
    chartLoading.value = false
  }
}

// 获取房间关联分析数据
const fetchRoomAnalysisData = async () => {
  roomAnalysisLoading.value = true
  try {
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await request({
      url: '/admin/platform/room-analysis',
      method: 'get',
      params
    })

    if (response.code === 200) {
      roomAnalysisData.value = response.data || []
    }
  } catch (error) {
    console.error('获取房间分析数据失败:', error)
    ElMessage.error('获取房间分析数据失败')
  } finally {
    roomAnalysisLoading.value = false
  }
}

// 事件处理函数
const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    fetchAnalyticsData()
    fetchRoomAnalysisData()
  }
}

const refreshData = () => {
  fetchAnalyticsData()
  fetchRoomAnalysisData()
}

const refreshRoomAnalysis = () => {
  fetchRoomAnalysisData()
}

const exportReport = async () => {
  try {
    ElMessage.info('正在生成分析报表...')

    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await request({
      url: '/admin/platform/analytics/export',
      method: 'get',
      params,
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `平台数据分析报表_${selectedPeriod.value}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('分析报表导出成功')
  } catch (error) {
    console.error('导出分析报表失败:', error)
    ElMessage.error('导出分析报表失败')
  }
}

const viewRoomDetail = (room) => {
  ElMessage.info(`查看房间 ${room.roomNumber} 的详细分析`)
  // 这里可以跳转到房间详情页面或打开详情对话框
}

// 初始化
onMounted(() => {
  // 设置默认日期范围（最近30天）
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 30)

  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]

  // 获取初始数据
  fetchAnalyticsData()
  fetchRoomAnalysisData()
})
</script>

<style scoped>
.platform-analytics {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.date-picker {
  width: 280px;
}

/* 核心指标卡片 */
.metrics-row {
  margin-bottom: 24px;
}

.metric-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.metric-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 24px;
}

.metric-card.revenue .metric-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.orders .metric-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-card.avg-order .metric-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-card.conversion .metric-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  gap: 4px;
}

.metric-trend.positive {
  color: #10b981;
}

.metric-trend.negative {
  color: #ef4444;
}

/* 图表区域 */
.charts-row, .analysis-row {
  margin-bottom: 24px;
}

.chart-card, .analysis-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.chart-container {
  height: 350px;
  padding: 16px 0;
}

/* 平台表现分析 */
.platform-performance {
  padding: 8px 0;
}

.performance-item {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.performance-item:last-child {
  margin-bottom: 0;
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.market-share {
  font-size: 12px;
  color: #6b7280;
}

.platform-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-name {
  font-size: 12px;
  color: #6b7280;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.metric-value.positive {
  color: #10b981;
}

.metric-value.negative {
  color: #ef4444;
}

/* 时段分析 */
.time-analysis {
  padding: 8px 0;
}

.time-chart {
  height: 200px;
  margin-bottom: 20px;
}

.peak-hours h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
}

.peak-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.peak-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.peak-time {
  font-weight: 600;
  color: #1f2937;
}

.peak-orders {
  font-size: 14px;
  color: #6b7280;
}

.peak-revenue {
  font-weight: 600;
  color: #059669;
}

/* 房间关联分析 */
.room-analysis-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .platform-analytics {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .date-picker {
    width: 100%;
  }

  .metric-content {
    flex-direction: column;
    text-align: center;
  }

  .metric-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .platform-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .peak-item {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }

  .chart-container {
    height: 250px;
  }

  .time-chart {
    height: 150px;
  }
}

/* 动画效果 */
.metric-card, .chart-card, .analysis-card, .room-analysis-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
