<template>
  <div class="platform-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>平台配置管理</h2>
        <p class="page-description">管理外卖平台接入配置、商品信息和优惠策略</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="saveAllSettings" :loading="saving">
          <el-icon><Check /></el-icon>
          保存配置
        </el-button>
        <el-button @click="resetSettings">
          <el-icon><RefreshLeft /></el-icon>
          重置
        </el-button>
      </div>
    </div>

    <!-- 配置选项卡 -->
    <el-tabs v-model="activeTab" class="settings-tabs">
      <!-- 平台接入配置 -->
      <el-tab-pane label="平台接入" name="platform">
        <el-row :gutter="20">
          <!-- 美团配置 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card class="platform-config-card">
              <template #header>
                <div class="card-header">
                  <div class="platform-title">
                    <el-icon class="platform-icon meituan"><ShoppingCart /></el-icon>
                    <span>美团外卖</span>
                  </div>
                  <el-switch 
                    v-model="platformSettings.meituan.enabled" 
                    active-text="启用"
                    inactive-text="禁用"
                  />
                </div>
              </template>
              
              <el-form :model="platformSettings.meituan" label-width="100px">
                <el-form-item label="App ID">
                  <el-input 
                    v-model="platformSettings.meituan.appId" 
                    placeholder="输入美团App ID"
                    :disabled="!platformSettings.meituan.enabled"
                  />
                </el-form-item>
                <el-form-item label="App Secret">
                  <el-input 
                    v-model="platformSettings.meituan.appSecret" 
                    type="password" 
                    placeholder="输入美团App Secret"
                    :disabled="!platformSettings.meituan.enabled"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="回调地址">
                  <el-input 
                    v-model="platformSettings.meituan.callbackUrl" 
                    placeholder="自动生成"
                    :disabled="!platformSettings.meituan.enabled"
                    readonly
                  />
                </el-form-item>
                <el-form-item label="佣金比例">
                  <el-input-number 
                    v-model="platformSettings.meituan.commissionRate" 
                    :min="0" 
                    :max="100" 
                    :precision="2"
                    :disabled="!platformSettings.meituan.enabled"
                  />
                  <span class="unit">%</span>
                </el-form-item>
                <el-form-item label="连接状态">
                  <el-tag :type="platformSettings.meituan.connected ? 'success' : 'danger'">
                    {{ platformSettings.meituan.connected ? '已连接' : '未连接' }}
                  </el-tag>
                  <el-button 
                    size="small" 
                    type="primary" 
                    @click="testConnection('meituan')"
                    :disabled="!platformSettings.meituan.enabled"
                    style="margin-left: 12px;"
                  >
                    测试连接
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <!-- 饿了么配置 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card class="platform-config-card">
              <template #header>
                <div class="card-header">
                  <div class="platform-title">
                    <el-icon class="platform-icon eleme"><ShoppingBag /></el-icon>
                    <span>饿了么</span>
                  </div>
                  <el-switch 
                    v-model="platformSettings.eleme.enabled" 
                    active-text="启用"
                    inactive-text="禁用"
                  />
                </div>
              </template>
              
              <el-form :model="platformSettings.eleme" label-width="100px">
                <el-form-item label="App Key">
                  <el-input 
                    v-model="platformSettings.eleme.appKey" 
                    placeholder="输入饿了么App Key"
                    :disabled="!platformSettings.eleme.enabled"
                  />
                </el-form-item>
                <el-form-item label="App Secret">
                  <el-input 
                    v-model="platformSettings.eleme.appSecret" 
                    type="password" 
                    placeholder="输入饿了么App Secret"
                    :disabled="!platformSettings.eleme.enabled"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="回调地址">
                  <el-input 
                    v-model="platformSettings.eleme.callbackUrl" 
                    placeholder="自动生成"
                    :disabled="!platformSettings.eleme.enabled"
                    readonly
                  />
                </el-form-item>
                <el-form-item label="佣金比例">
                  <el-input-number 
                    v-model="platformSettings.eleme.commissionRate" 
                    :min="0" 
                    :max="100" 
                    :precision="2"
                    :disabled="!platformSettings.eleme.enabled"
                  />
                  <span class="unit">%</span>
                </el-form-item>
                <el-form-item label="连接状态">
                  <el-tag :type="platformSettings.eleme.connected ? 'success' : 'danger'">
                    {{ platformSettings.eleme.connected ? '已连接' : '未连接' }}
                  </el-tag>
                  <el-button 
                    size="small" 
                    type="primary" 
                    @click="testConnection('eleme')"
                    :disabled="!platformSettings.eleme.enabled"
                    style="margin-left: 12px;"
                  >
                    测试连接
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <!-- 商品管理 -->
      <el-tab-pane label="商品管理" name="products">
        <el-card class="products-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">商品列表</span>
              <el-button type="primary" @click="showAddProductDialog">
                <el-icon><Plus /></el-icon>
                添加商品
              </el-button>
            </div>
          </template>

          <el-table :data="productList" v-loading="productsLoading" stripe>
            <el-table-column prop="name" label="商品名称" width="200" />
            <el-table-column prop="category" label="分类" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getCategoryName(row.category) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="价格" width="100">
              <template #default="{ row }">
                ¥{{ formatCurrency(row.price) }}
              </template>
            </el-table-column>
            <el-table-column prop="stock" label="库存" width="80" />
            <el-table-column prop="platforms" label="上架平台" width="150">
              <template #default="{ row }">
                <el-tag 
                  v-for="platform in row.platforms" 
                  :key="platform"
                  :type="getPlatformTagType(platform)"
                  size="small"
                  style="margin-right: 4px;"
                >
                  {{ getPlatformName(platform) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'active' ? '上架' : '下架' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="editProduct(row)">编辑</el-button>
                <el-button 
                  size="small" 
                  :type="row.status === 'active' ? 'warning' : 'success'"
                  @click="toggleProductStatus(row)"
                >
                  {{ row.status === 'active' ? '下架' : '上架' }}
                </el-button>
                <el-button size="small" type="danger" @click="deleteProduct(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 优惠策略 -->
      <el-tab-pane label="优惠策略" name="promotions">
        <el-row :gutter="20">
          <!-- 满减活动 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card class="promotion-card">
              <template #header>
                <div class="card-header">
                  <span class="card-title">满减活动</span>
                  <el-button size="small" type="primary" @click="addPromotion('discount')">
                    <el-icon><Plus /></el-icon>
                    添加
                  </el-button>
                </div>
              </template>
              
              <div class="promotion-list">
                <div 
                  v-for="promotion in discountPromotions" 
                  :key="promotion.id"
                  class="promotion-item"
                >
                  <div class="promotion-info">
                    <div class="promotion-title">满{{ promotion.minAmount }}减{{ promotion.discountAmount }}</div>
                    <div class="promotion-desc">{{ promotion.description }}</div>
                    <div class="promotion-time">
                      {{ formatDate(promotion.startTime) }} - {{ formatDate(promotion.endTime) }}
                    </div>
                  </div>
                  <div class="promotion-actions">
                    <el-switch 
                      v-model="promotion.enabled" 
                      @change="updatePromotionStatus(promotion)"
                    />
                    <el-button size="small" @click="editPromotion(promotion)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deletePromotion(promotion)">删除</el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 新用户优惠 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card class="promotion-card">
              <template #header>
                <div class="card-header">
                  <span class="card-title">新用户优惠</span>
                  <el-button size="small" type="primary" @click="addPromotion('newuser')">
                    <el-icon><Plus /></el-icon>
                    添加
                  </el-button>
                </div>
              </template>
              
              <div class="promotion-list">
                <div 
                  v-for="promotion in newUserPromotions" 
                  :key="promotion.id"
                  class="promotion-item"
                >
                  <div class="promotion-info">
                    <div class="promotion-title">{{ promotion.title }}</div>
                    <div class="promotion-desc">{{ promotion.description }}</div>
                    <div class="promotion-time">
                      {{ formatDate(promotion.startTime) }} - {{ formatDate(promotion.endTime) }}
                    </div>
                  </div>
                  <div class="promotion-actions">
                    <el-switch 
                      v-model="promotion.enabled" 
                      @change="updatePromotionStatus(promotion)"
                    />
                    <el-button size="small" @click="editPromotion(promotion)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deletePromotion(promotion)">删除</el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <!-- 通知设置 -->
      <el-tab-pane label="通知设置" name="notifications">
        <el-card class="notification-card">
          <template #header>
            <span class="card-title">通知配置</span>
          </template>
          
          <el-form :model="notificationSettings" label-width="150px">
            <el-form-item label="新订单通知">
              <el-switch v-model="notificationSettings.newOrder.enabled" />
              <div v-if="notificationSettings.newOrder.enabled" class="notification-options">
                <el-checkbox-group v-model="notificationSettings.newOrder.methods">
                  <el-checkbox label="email">邮件</el-checkbox>
                  <el-checkbox label="sms">短信</el-checkbox>
                  <el-checkbox label="wechat">微信</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
            
            <el-form-item label="订单核销通知">
              <el-switch v-model="notificationSettings.verification.enabled" />
              <div v-if="notificationSettings.verification.enabled" class="notification-options">
                <el-checkbox-group v-model="notificationSettings.verification.methods">
                  <el-checkbox label="email">邮件</el-checkbox>
                  <el-checkbox label="sms">短信</el-checkbox>
                  <el-checkbox label="wechat">微信</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
            
            <el-form-item label="异常订单通知">
              <el-switch v-model="notificationSettings.exception.enabled" />
              <div v-if="notificationSettings.exception.enabled" class="notification-options">
                <el-checkbox-group v-model="notificationSettings.exception.methods">
                  <el-checkbox label="email">邮件</el-checkbox>
                  <el-checkbox label="sms">短信</el-checkbox>
                  <el-checkbox label="wechat">微信</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
            
            <el-form-item label="日报推送">
              <el-switch v-model="notificationSettings.dailyReport.enabled" />
              <div v-if="notificationSettings.dailyReport.enabled" class="notification-options">
                <el-time-picker 
                  v-model="notificationSettings.dailyReport.time"
                  placeholder="选择推送时间"
                  format="HH:mm"
                  value-format="HH:mm"
                />
                <el-checkbox-group v-model="notificationSettings.dailyReport.methods">
                  <el-checkbox label="email">邮件</el-checkbox>
                  <el-checkbox label="wechat">微信</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check, RefreshLeft, ShoppingCart, ShoppingBag, Plus
} from '@element-plus/icons-vue'
import request from '@/api/request'

// 响应式数据
const activeTab = ref('platform')
const saving = ref(false)
const productsLoading = ref(false)

// 平台设置
const platformSettings = reactive({
  meituan: {
    enabled: false,
    appId: '',
    appSecret: '',
    callbackUrl: '',
    commissionRate: 0,
    connected: false
  },
  eleme: {
    enabled: false,
    appKey: '',
    appSecret: '',
    callbackUrl: '',
    commissionRate: 0,
    connected: false
  }
})

// 商品列表
const productList = ref([])

// 优惠活动
const discountPromotions = ref([])
const newUserPromotions = ref([])

// 通知设置
const notificationSettings = reactive({
  newOrder: {
    enabled: false,
    methods: []
  },
  verification: {
    enabled: false,
    methods: []
  },
  exception: {
    enabled: false,
    methods: []
  },
  dailyReport: {
    enabled: false,
    time: '',
    methods: []
  }
})

// 工具函数
const formatCurrency = (amount) => {
  if (amount === 0) return '0.00'
  return (amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const getPlatformName = (type) => {
  const names = {
    meituan: '美团',
    eleme: '饿了么'
  }
  return names[type] || type
}

const getPlatformTagType = (type) => {
  const types = {
    meituan: 'warning',
    eleme: 'primary'
  }
  return types[type] || 'info'
}

const getCategoryName = (category) => {
  const names = {
    drink: '饮品',
    snack: '小食',
    meal: '正餐',
    dessert: '甜品'
  }
  return names[category] || category
}

// 获取平台设置
const fetchPlatformSettings = async () => {
  try {
    const response = await request({
      url: '/admin/platform/settings',
      method: 'get'
    })

    if (response.code === 200) {
      Object.assign(platformSettings, response.data)
    }
  } catch (error) {
    console.error('获取平台设置失败:', error)
  }
}

// 获取商品列表
const fetchProductList = async () => {
  productsLoading.value = true
  try {
    const response = await request({
      url: '/admin/platform/products',
      method: 'get'
    })

    if (response.code === 200) {
      productList.value = response.data || []
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    productsLoading.value = false
  }
}

// 获取优惠活动
const fetchPromotions = async () => {
  try {
    const response = await request({
      url: '/admin/platform/promotions',
      method: 'get'
    })

    if (response.code === 200) {
      const promotions = response.data || []
      discountPromotions.value = promotions.filter(p => p.type === 'discount')
      newUserPromotions.value = promotions.filter(p => p.type === 'newuser')
    }
  } catch (error) {
    console.error('获取优惠活动失败:', error)
  }
}

// 获取通知设置
const fetchNotificationSettings = async () => {
  try {
    const response = await request({
      url: '/admin/platform/notifications',
      method: 'get'
    })

    if (response.code === 200) {
      Object.assign(notificationSettings, response.data)
    }
  } catch (error) {
    console.error('获取通知设置失败:', error)
  }
}

// 事件处理函数
const saveAllSettings = async () => {
  saving.value = true
  try {
    // 保存平台设置
    await request({
      url: '/admin/platform/settings',
      method: 'post',
      data: platformSettings
    })

    // 保存通知设置
    await request({
      url: '/admin/platform/notifications',
      method: 'post',
      data: notificationSettings
    })

    ElMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  ElMessageBox.confirm('确认重置所有配置？', '重置确认', {
    confirmButtonText: '确认重置',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    fetchPlatformSettings()
    fetchNotificationSettings()
    ElMessage.success('配置已重置')
  }).catch(() => {})
}

const testConnection = async (platform) => {
  try {
    ElMessage.info(`正在测试${getPlatformName(platform)}连接...`)

    const response = await request({
      url: `/admin/platform/test-connection/${platform}`,
      method: 'post',
      data: platformSettings[platform]
    })

    if (response.code === 200) {
      platformSettings[platform].connected = true
      ElMessage.success(`${getPlatformName(platform)}连接测试成功`)
    }
  } catch (error) {
    platformSettings[platform].connected = false
    console.error('连接测试失败:', error)
    ElMessage.error(`${getPlatformName(platform)}连接测试失败`)
  }
}

// 商品管理
const showAddProductDialog = () => {
  ElMessage.info('添加商品功能开发中')
}

const editProduct = (product) => {
  ElMessage.info(`编辑商品: ${product.name}`)
}

const toggleProductStatus = async (product) => {
  try {
    const newStatus = product.status === 'active' ? 'inactive' : 'active'

    await request({
      url: `/admin/platform/products/${product.id}/status`,
      method: 'put',
      data: { status: newStatus }
    })

    product.status = newStatus
    ElMessage.success(`商品${newStatus === 'active' ? '上架' : '下架'}成功`)
  } catch (error) {
    console.error('更新商品状态失败:', error)
    ElMessage.error('更新商品状态失败')
  }
}

const deleteProduct = async (product) => {
  try {
    await ElMessageBox.confirm(`确认删除商品 ${product.name}？`, '删除确认', {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await request({
      url: `/admin/platform/products/${product.id}`,
      method: 'delete'
    })

    fetchProductList()
    ElMessage.success('商品删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除商品失败:', error)
      ElMessage.error('删除商品失败')
    }
  }
}

// 优惠活动管理
const addPromotion = (type) => {
  ElMessage.info(`添加${type === 'discount' ? '满减' : '新用户'}活动功能开发中`)
}

const editPromotion = (promotion) => {
  ElMessage.info(`编辑优惠活动: ${promotion.title}`)
}

const updatePromotionStatus = async (promotion) => {
  try {
    await request({
      url: `/admin/platform/promotions/${promotion.id}/status`,
      method: 'put',
      data: { enabled: promotion.enabled }
    })

    ElMessage.success(`优惠活动${promotion.enabled ? '启用' : '禁用'}成功`)
  } catch (error) {
    console.error('更新优惠活动状态失败:', error)
    ElMessage.error('更新优惠活动状态失败')
  }
}

const deletePromotion = async (promotion) => {
  try {
    await ElMessageBox.confirm(`确认删除优惠活动 ${promotion.title}？`, '删除确认', {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await request({
      url: `/admin/platform/promotions/${promotion.id}`,
      method: 'delete'
    })

    fetchPromotions()
    ElMessage.success('优惠活动删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除优惠活动失败:', error)
      ElMessage.error('删除优惠活动失败')
    }
  }
}

// 初始化
onMounted(() => {
  fetchPlatformSettings()
  fetchProductList()
  fetchPromotions()
  fetchNotificationSettings()
})
</script>

<style scoped>
.platform-settings {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 选项卡 */
.settings-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

/* 平台配置卡片 */
.platform-config-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.platform-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.platform-icon {
  font-size: 20px;
}

.platform-icon.meituan {
  color: #ffa726;
}

.platform-icon.eleme {
  color: #42a5f5;
}

.unit {
  margin-left: 8px;
  color: #6b7280;
  font-size: 14px;
}

/* 商品管理 */
.products-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 优惠策略 */
.promotion-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.promotion-list {
  max-height: 400px;
  overflow-y: auto;
}

.promotion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.promotion-item:hover {
  background: #e2e8f0;
}

.promotion-info {
  flex: 1;
}

.promotion-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.promotion-desc {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.promotion-time {
  font-size: 12px;
  color: #9ca3af;
}

.promotion-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 通知设置 */
.notification-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-options {
  margin-top: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
}

.notification-options .el-checkbox-group {
  margin-top: 8px;
}

.notification-options .el-time-picker {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .platform-settings {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .settings-tabs {
    padding: 12px;
  }

  .platform-config-card {
    margin-bottom: 16px;
  }

  .promotion-item {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .promotion-actions {
    justify-content: center;
  }

  .notification-options {
    padding: 8px;
  }
}

/* 动画效果 */
.platform-config-card, .products-card, .promotion-card, .notification-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.promotion-list::-webkit-scrollbar {
  width: 6px;
}

.promotion-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.promotion-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.promotion-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 20px;
}

.el-input, .el-select {
  width: 100%;
}

.el-input-number {
  width: 150px;
}

/* 标签样式 */
.el-tag {
  margin-right: 4px;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 开关样式 */
.el-switch {
  margin-right: 12px;
}
</style>
