<template>
  <div class="verification-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>订单核销中心</h2>
        <p class="page-description">快速核销外卖订单，支持扫码验证和手动输入</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshPendingOrders">
          <el-icon><Refresh /></el-icon>
          刷新待核销
        </el-button>
        <el-button @click="showScanDialog">
          <el-icon><Camera /></el-icon>
          扫码核销
        </el-button>
      </div>
    </div>

    <!-- 核销统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
        <el-card class="stat-card today">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ todayStats.verified }}</div>
              <div class="stat-label">今日已核销</div>
              <div class="stat-trend">
                <span class="trend-text">较昨日</span>
                <span class="trend-value" :class="todayStats.trend > 0 ? 'positive' : 'negative'">
                  {{ todayStats.trend > 0 ? '+' : '' }}{{ todayStats.trend }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
        <el-card class="stat-card pending">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ pendingStats.count }}</div>
              <div class="stat-label">待核销订单</div>
              <div class="stat-amount">¥{{ formatCurrency(pendingStats.amount) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
        <el-card class="stat-card efficiency">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ efficiencyStats.rate }}%</div>
              <div class="stat-label">核销效率</div>
              <div class="stat-desc">平均{{ efficiencyStats.avgTime }}分钟</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速核销区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 快速核销表单 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="quick-verify-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">快速核销</span>
              <el-tag type="info" size="small">手动输入</el-tag>
            </div>
          </template>

          <el-form :model="verifyForm" :rules="verifyRules" ref="verifyFormRef" label-width="100px">
            <el-form-item label="平台类型" prop="platformType">
              <el-select v-model="verifyForm.platformType" placeholder="选择平台">
                <el-option label="美团" value="meituan">
                  <div class="platform-option">
                    <el-icon><ShoppingCart /></el-icon>
                    <span>美团</span>
                  </div>
                </el-option>
                <el-option label="饿了么" value="eleme">
                  <div class="platform-option">
                    <el-icon><ShoppingBag /></el-icon>
                    <span>饿了么</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="订单号" prop="platformOrderId">
              <el-input
                v-model="verifyForm.platformOrderId"
                placeholder="输入平台订单号"
                clearable
                @keyup.enter="quickVerify"
              >
                <template #append>
                  <el-button @click="quickVerify" :loading="verifyLoading">
                    核销
                  </el-button>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="核销码" prop="verificationCode">
              <el-input
                v-model="verifyForm.verificationCode"
                placeholder="输入核销码（可选）"
                clearable
                @keyup.enter="quickVerify"
              />
            </el-form-item>

            <el-form-item label="房间号" prop="roomNumber">
              <el-select v-model="verifyForm.roomNumber" placeholder="选择房间" filterable>
                <el-option
                  v-for="room in roomList"
                  :key="room.id"
                  :label="`${room.number}号房 - ${room.name}`"
                  :value="room.number"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="quickVerify" :loading="verifyLoading" size="large">
                <el-icon><Check /></el-icon>
                立即核销
              </el-button>
              <el-button @click="resetVerifyForm" size="large">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 待核销订单列表 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="pending-orders-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">待核销订单</span>
              <el-tag type="warning" size="small">{{ pendingOrders.length }}单</el-tag>
            </div>
          </template>

          <div class="pending-orders-list">
            <div v-if="pendingOrders.length === 0" class="empty-state">
              <el-icon class="empty-icon"><DocumentRemove /></el-icon>
              <p>暂无待核销订单</p>
            </div>

            <div
              v-for="order in pendingOrders"
              :key="order.id"
              class="pending-order-item"
              @click="selectPendingOrder(order)"
            >
              <div class="order-header">
                <el-tag :type="getPlatformTagType(order.platformType)" size="small">
                  {{ getPlatformName(order.platformType) }}
                </el-tag>
                <span class="order-time">{{ formatTime(order.createdAt) }}</span>
              </div>

              <div class="order-content">
                <div class="order-id">{{ order.platformOrderId }}</div>
                <div class="order-amount">¥{{ formatCurrency(order.paidAmount) }}</div>
              </div>

              <div class="order-footer">
                <span class="room-info">{{ order.roomNumber }}号房</span>
                <el-button type="primary" size="small" @click.stop="verifyPendingOrder(order)">
                  核销
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 核销历史 -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">核销历史</span>
          <div class="history-actions">
            <el-select v-model="historyFilter" size="small" style="width: 120px;">
              <el-option label="今日" value="today" />
              <el-option label="本周" value="week" />
              <el-option label="本月" value="month" />
            </el-select>
            <el-button size="small" @click="refreshHistory">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="verificationHistory" v-loading="historyLoading" stripe>
        <el-table-column prop="platformOrderId" label="订单号" width="180">
          <template #default="{ row }">
            <div class="order-id-cell">
              <el-tag :type="getPlatformTagType(row.platformType)" size="small">
                {{ getPlatformName(row.platformType) }}
              </el-tag>
              <span class="order-number">{{ row.platformOrderId }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="roomNumber" label="房间" width="80">
          <template #default="{ row }">
            {{ row.roomNumber }}号房
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" label="金额" width="100">
          <template #default="{ row }">
            ¥{{ formatCurrency(row.paidAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="verifiedAt" label="核销时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.verifiedAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="verifiedBy" label="核销人" width="100" />
        <el-table-column prop="verificationMethod" label="核销方式" width="100">
          <template #default="{ row }">
            <el-tag :type="row.verificationMethod === 'scan' ? 'primary' : 'info'" size="small">
              {{ row.verificationMethod === 'scan' ? '扫码' : '手动' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewVerificationDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="historyPagination.page"
          v-model:page-size="historyPagination.pageSize"
          :page-sizes="[10, 20, 50]"
          :total="historyPagination.total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-card>

    <!-- 扫码核销对话框 -->
    <el-dialog
      v-model="scanDialogVisible"
      title="扫码核销"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="scan-content">
        <div class="scan-area">
          <div class="camera-container">
            <video ref="videoRef" autoplay playsinline></video>
            <canvas ref="canvasRef" style="display: none;"></canvas>
          </div>
          <div class="scan-tips">
            <el-icon><Camera /></el-icon>
            <p>请将二维码对准摄像头进行扫描</p>
          </div>
        </div>

        <div class="scan-result" v-if="scanResult">
          <el-alert
            :title="scanResult.success ? '扫描成功' : '扫描失败'"
            :type="scanResult.success ? 'success' : 'error'"
            :description="scanResult.message"
            show-icon
            :closable="false"
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="closeScanDialog">关闭</el-button>
        <el-button type="primary" @click="startScan" :disabled="scanning">
          {{ scanning ? '扫描中...' : '开始扫描' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 核销详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="核销详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentVerification" class="verification-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="平台订单号">
            {{ currentVerification.platformOrderId }}
          </el-descriptions-item>
          <el-descriptions-item label="平台类型">
            <el-tag :type="getPlatformTagType(currentVerification.platformType)">
              {{ getPlatformName(currentVerification.platformType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="房间号">
            {{ currentVerification.roomNumber }}号房
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">
            ¥{{ formatCurrency(currentVerification.paidAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="核销时间">
            {{ formatDateTime(currentVerification.verifiedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="核销人">
            {{ currentVerification.verifiedBy }}
          </el-descriptions-item>
          <el-descriptions-item label="核销方式">
            <el-tag :type="currentVerification.verificationMethod === 'scan' ? 'primary' : 'info'">
              {{ currentVerification.verificationMethod === 'scan' ? '扫码核销' : '手动核销' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核销码">
            {{ currentVerification.verificationCode || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Calendar, Clock, TrendCharts, Check, RefreshLeft,
  ShoppingCart, ShoppingBag, DocumentRemove, Camera
} from '@element-plus/icons-vue'
import request from '@/api/request'

// 响应式数据
const verifyLoading = ref(false)
const historyLoading = ref(false)
const scanning = ref(false)
const scanDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const scanResult = ref(null)
const currentVerification = ref(null)

// 表单引用
const verifyFormRef = ref(null)
const videoRef = ref(null)
const canvasRef = ref(null)

// 统计数据
const todayStats = reactive({
  verified: 0,
  trend: 0
})

const pendingStats = reactive({
  count: 0,
  amount: 0
})

const efficiencyStats = reactive({
  rate: 0,
  avgTime: 0
})

// 核销表单
const verifyForm = reactive({
  platformType: '',
  platformOrderId: '',
  verificationCode: '',
  roomNumber: ''
})

const verifyRules = {
  platformType: [{ required: true, message: '请选择平台类型', trigger: 'change' }],
  platformOrderId: [{ required: true, message: '请输入订单号', trigger: 'blur' }]
}

// 待核销订单
const pendingOrders = ref([])

// 房间列表
const roomList = ref([])

// 核销历史
const verificationHistory = ref([])
const historyFilter = ref('today')
const historyPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 工具函数
const formatCurrency = (amount) => {
  if (amount === 0) return '0.00'
  return (amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getPlatformName = (type) => {
  const names = {
    meituan: '美团',
    eleme: '饿了么'
  }
  return names[type] || type
}

const getPlatformTagType = (type) => {
  const types = {
    meituan: 'warning',
    eleme: 'primary'
  }
  return types[type] || 'info'
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await request({
      url: '/admin/platform/verification/stats',
      method: 'get'
    })

    if (response.code === 200) {
      const stats = response.data
      Object.assign(todayStats, stats.today)
      Object.assign(pendingStats, stats.pending)
      Object.assign(efficiencyStats, stats.efficiency)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取待核销订单
const fetchPendingOrders = async () => {
  try {
    const response = await request({
      url: '/admin/platform/orders/pending',
      method: 'get'
    })

    if (response.code === 200) {
      pendingOrders.value = response.data || []
    }
  } catch (error) {
    console.error('获取待核销订单失败:', error)
    ElMessage.error('获取待核销订单失败')
  }
}

// 获取房间列表
const fetchRoomList = async () => {
  try {
    const response = await request({
      url: '/admin/rooms',
      method: 'get',
      params: { page: 1, page_size: 100 }
    })

    if (response.code === 200) {
      roomList.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取房间列表失败:', error)
  }
}

// 获取核销历史
const fetchVerificationHistory = async () => {
  historyLoading.value = true
  try {
    const response = await request({
      url: '/admin/platform/verification/history',
      method: 'get',
      params: {
        page: historyPagination.page,
        page_size: historyPagination.pageSize,
        filter: historyFilter.value
      }
    })

    if (response.code === 200) {
      verificationHistory.value = response.data.data || []
      historyPagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取核销历史失败:', error)
    ElMessage.error('获取核销历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 事件处理函数
const refreshPendingOrders = () => {
  fetchPendingOrders()
  fetchStats()
}

const refreshHistory = () => {
  fetchVerificationHistory()
}

// 快速核销
const quickVerify = async () => {
  try {
    await verifyFormRef.value.validate()

    verifyLoading.value = true

    const response = await request({
      url: '/admin/platform/verify',
      method: 'post',
      data: {
        platform_type: verifyForm.platformType,
        platform_order_id: verifyForm.platformOrderId,
        verification_code: verifyForm.verificationCode,
        room_number: verifyForm.roomNumber,
        verification_method: 'manual'
      }
    })

    if (response.code === 200) {
      ElMessage.success('订单核销成功')
      resetVerifyForm()
      refreshPendingOrders()
      fetchVerificationHistory()
    }
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      console.error('核销失败:', error)
      ElMessage.error('核销失败')
    }
  } finally {
    verifyLoading.value = false
  }
}

// 重置表单
const resetVerifyForm = () => {
  Object.assign(verifyForm, {
    platformType: '',
    platformOrderId: '',
    verificationCode: '',
    roomNumber: ''
  })
  verifyFormRef.value?.clearValidate()
}

// 选择待核销订单
const selectPendingOrder = (order) => {
  verifyForm.platformType = order.platformType
  verifyForm.platformOrderId = order.platformOrderId
  verifyForm.roomNumber = order.roomNumber
}

// 核销待核销订单
const verifyPendingOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确认核销订单 ${order.platformOrderId}？`,
      '核销确认',
      {
        confirmButtonText: '确认核销',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await request({
      url: '/admin/platform/verify',
      method: 'post',
      data: {
        platform_type: order.platformType,
        platform_order_id: order.platformOrderId,
        room_number: order.roomNumber,
        verification_method: 'manual'
      }
    })

    if (response.code === 200) {
      ElMessage.success('订单核销成功')
      refreshPendingOrders()
      fetchVerificationHistory()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('核销失败:', error)
      ElMessage.error('核销失败')
    }
  }
}

// 扫码相关
const showScanDialog = () => {
  scanDialogVisible.value = true
  nextTick(() => {
    initCamera()
  })
}

const closeScanDialog = () => {
  stopCamera()
  scanDialogVisible.value = false
  scanResult.value = null
}

const initCamera = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      video: { facingMode: 'environment' }
    })
    if (videoRef.value) {
      videoRef.value.srcObject = stream
    }
  } catch (error) {
    console.error('摄像头初始化失败:', error)
    ElMessage.error('无法访问摄像头')
  }
}

const stopCamera = () => {
  if (videoRef.value && videoRef.value.srcObject) {
    const tracks = videoRef.value.srcObject.getTracks()
    tracks.forEach(track => track.stop())
    videoRef.value.srcObject = null
  }
}

const startScan = () => {
  scanning.value = true
  // 这里应该集成二维码扫描库，如 jsQR
  // 简化实现，模拟扫描结果
  setTimeout(() => {
    scanning.value = false
    scanResult.value = {
      success: true,
      message: '扫描成功，订单信息已自动填入'
    }
    // 模拟填入扫描结果
    verifyForm.platformType = 'meituan'
    verifyForm.platformOrderId = 'MT' + Date.now()
  }, 2000)
}

// 查看核销详情
const viewVerificationDetail = (verification) => {
  currentVerification.value = verification
  detailDialogVisible.value = true
}

// 分页处理
const handleHistorySizeChange = (size) => {
  historyPagination.pageSize = size
  historyPagination.page = 1
  fetchVerificationHistory()
}

const handleHistoryCurrentChange = (page) => {
  historyPagination.page = page
  fetchVerificationHistory()
}

// 初始化和清理
onMounted(() => {
  fetchStats()
  fetchPendingOrders()
  fetchRoomList()
  fetchVerificationHistory()
})

onUnmounted(() => {
  stopCamera()
})
</script>

<style scoped>
.verification-center {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 24px;
}

.stat-card.today .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.pending .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.efficiency .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-amount {
  font-size: 16px;
  font-weight: 600;
  color: #059669;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.trend-text {
  color: #6b7280;
}

.trend-value.positive {
  color: #10b981;
}

.trend-value.negative {
  color: #ef4444;
}

.stat-desc {
  font-size: 12px;
  color: #6b7280;
}

/* 主要内容区域 */
.main-content {
  margin-bottom: 24px;
}

/* 快速核销卡片 */
.quick-verify-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 待核销订单卡片 */
.pending-orders-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pending-orders-list {
  max-height: 500px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.pending-order-item {
  padding: 16px;
  margin-bottom: 12px;
  background: #f8fafc;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pending-order-item:hover {
  background: #e2e8f0;
  transform: translateX(4px);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.order-time {
  font-size: 12px;
  color: #6b7280;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-id {
  font-family: monospace;
  font-size: 14px;
  color: #1f2937;
  font-weight: 600;
}

.order-amount {
  font-size: 16px;
  font-weight: 700;
  color: #059669;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-info {
  font-size: 12px;
  color: #6b7280;
}

/* 核销历史卡片 */
.history-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.order-id-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-number {
  font-family: monospace;
  font-size: 12px;
  color: #6b7280;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 扫码对话框 */
.scan-content {
  text-align: center;
}

.scan-area {
  margin-bottom: 20px;
}

.camera-container {
  width: 300px;
  height: 200px;
  margin: 0 auto 16px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.camera-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scan-tips {
  color: #6b7280;
}

.scan-tips .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.scan-result {
  margin-top: 16px;
}

/* 核销详情 */
.verification-detail {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verification-center {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .main-content .el-col {
    margin-bottom: 20px;
  }

  .history-actions {
    flex-direction: column;
    gap: 8px;
  }

  .camera-container {
    width: 250px;
    height: 150px;
  }
}

/* 动画效果 */
.stat-card, .quick-verify-card, .pending-orders-card, .history-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.pending-orders-list::-webkit-scrollbar {
  width: 6px;
}

.pending-orders-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.pending-orders-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.pending-orders-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
