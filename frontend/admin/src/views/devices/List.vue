<template>
  <div class="device-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>设备管理</h2>
        <p>管理所有房间设备状态和控制</p>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button type="success" @click="showBatchControlDialog">
            <el-icon><SwitchButton /></el-icon>
            批量控制
          </el-button>
          <el-button type="info" @click="showGlobalAudioDialog">
            <el-icon><Microphone /></el-icon>
            全局广播
          </el-button>
          <el-button type="primary" @click="refreshAllDevices">
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 设备统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ deviceStats.total }}</div>
            <div class="stats-label">总设备数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card online">
          <div class="stats-content">
            <div class="stats-value">{{ deviceStats.online }}</div>
            <div class="stats-label">在线设备</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card offline">
          <div class="stats-content">
            <div class="stats-value">{{ deviceStats.offline }}</div>
            <div class="stats-label">离线设备</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card fault">
          <div class="stats-content">
            <div class="stats-value">{{ deviceStats.fault }}</div>
            <div class="stats-label">故障设备</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 店铺主门禁锁 -->
    <el-row v-if="mainLockDevice" :gutter="20" class="main-lock-section">
      <el-col :span="24">
        <el-card class="main-lock-card">
          <template #header>
            <div class="main-lock-header">
              <span class="main-lock-title">店铺主门禁锁</span>
              <el-tag
                :type="getDeviceStatusType(mainLockDevice.status)"
                size="small"
              >
                {{ getDeviceStatusText(mainLockDevice.status) }}
              </el-tag>
            </div>
          </template>

          <div class="main-lock-content">
            <div class="device-item">
              <div class="device-info">
                <el-icon class="device-icon"><Lock /></el-icon>
                <span class="device-name">店铺主门禁锁</span>
              </div>
              <div class="device-status">
                <el-tag
                  :type="getDeviceStatusType(mainLockDevice.status)"
                  size="small"
                >
                  {{ getDeviceStatusText(mainLockDevice.status) }}
                </el-tag>
              </div>
              <div class="device-controls">
                <el-button
                  v-if="mainLockDevice.status === 'online'"
                  type="primary"
                  size="small"
                  @click="handleControlMainLock('unlock')"
                >
                  开锁
                </el-button>
                <el-button
                  v-if="mainLockDevice.status === 'online'"
                  type="warning"
                  size="small"
                  @click="handleControlMainLock('lock')"
                >
                  锁定
                </el-button>
                <span v-else class="offline-text">设备离线</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 房间设备列表 -->
    <el-row :gutter="20" class="room-devices">
      <el-col
        v-for="room in roomDevices"
        :key="room.id"
        :span="12"
        class="room-card-col"
      >
        <el-card class="room-device-card" :data-room-id="room.id">
          <template #header>
            <div class="room-header">
              <span class="room-name">{{ room.name }}</span>
              <el-tag
                :type="getRoomStatusType(room.status)"
                size="small"
              >
                {{ getRoomStatusText(room.status) }}
              </el-tag>
            </div>
          </template>

          <div class="device-grid">
            <!-- 门锁设备 -->
            <div class="device-item">
              <div class="device-info">
                <el-icon class="device-icon"><Lock /></el-icon>
                <span class="device-name">房间门锁</span>
              </div>
              <div class="device-status">
                <el-tag
                  :type="getDeviceStatusType(getDeviceStatus(room, 'room_lock'))"
                  size="small"
                >
                  {{ getDeviceStatusText(getDeviceStatus(room, 'room_lock')) }}
                </el-tag>
              </div>
              <div class="device-controls">
                <template v-if="isDeviceOnline(room, 'room_lock')">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleControlDevice(room.id, 'room_lock', 'unlock')"
                  >
                    开锁
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleControlDevice(room.id, 'room_lock', 'lock')"
                  >
                    锁定
                  </el-button>
                </template>
                <span v-else class="offline-text">设备离线</span>
              </div>
            </div>

            <!-- 电源控制 -->
            <div class="device-item">
              <div class="device-info">
                <el-icon class="device-icon"><Switch /></el-icon>
                <span class="device-name">电源控制</span>
              </div>
              <div class="device-status">
                <el-tag
                  :type="getDeviceStatusType(getDeviceStatus(room, 'power'))"
                  size="small"
                >
                  {{ getDeviceStatusText(getDeviceStatus(room, 'power')) }}
                </el-tag>
              </div>
              <div class="device-controls">
                <template v-if="isDeviceOnline(room, 'power')">
                  <el-button
                    type="success"
                    size="small"
                    @click="handleControlDevice(room.id, 'power', 'on')"
                  >
                    开启
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleControlDevice(room.id, 'power', 'off')"
                  >
                    关闭
                  </el-button>
                </template>
                <span v-else class="offline-text">设备离线</span>
              </div>
            </div>

            <!-- 音响设备 -->
            <div class="device-item">
              <div class="device-info">
                <el-icon class="device-icon"><Microphone /></el-icon>
                <span class="device-name">音响设备</span>
              </div>
              <div class="device-status">
                <el-tag
                  :type="getDeviceStatusType(getDeviceStatus(room, 'speaker'))"
                  size="small"
                >
                  {{ getDeviceStatusText(getDeviceStatus(room, 'speaker')) }}
                </el-tag>
              </div>
              <div class="device-controls">
                <template v-if="isDeviceOnline(room, 'speaker')">
                  <el-button
                    type="primary"
                    size="small"
                    @click="showAudioDialog(room.id)"
                  >
                    发送消息
                  </el-button>
                </template>
                <span v-else class="offline-text">设备离线</span>
              </div>
            </div>


          </div>

          <div class="room-actions">
            <el-button
              type="primary"
              size="small"
              @click="viewRoomDetail(room)"
            >
              查看详情
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="refreshRoomDevices(room.id)"
            >
              刷新状态
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 音频消息对话框 -->
    <el-dialog v-model="audioDialogVisible" title="发送音频消息" width="500px">
      <el-form :model="audioForm" label-width="80px">
        <el-form-item label="目标房间">
          <el-input :value="`房间 ${selectedRoomId}`" disabled />
        </el-form-item>
        <el-form-item label="消息内容">
          <el-input
            v-model="audioForm.message"
            type="textarea"
            :rows="3"
            placeholder="请输入要发送的消息内容"
          />
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="audioForm.type" placeholder="选择消息类型">
            <el-option label="通知" value="notice" />
            <el-option label="警告" value="warning" />
            <el-option label="提醒" value="reminder" />
          </el-select>
        </el-form-item>
        <el-form-item label="音量">
          <el-slider v-model="audioForm.volume" :min="0" :max="100" show-input />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="audioDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sendAudioMessage" :loading="audioLoading">
          发送消息
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量控制对话框 -->
    <el-dialog
      v-model="batchControlDialogVisible"
      title="批量设备控制"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchControlForm" label-width="100px">
        <el-form-item label="控制类型">
          <el-select v-model="batchControlForm.type" placeholder="选择控制类型">
            <el-option label="批量开电" value="power_on" />
            <el-option label="批量关电" value="power_off" />
            <el-option label="批量开锁" value="unlock_all" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标房间">
          <el-checkbox-group v-model="batchControlForm.roomIds">
            <el-checkbox
              v-for="room in roomDevices"
              :key="room.id"
              :label="room.id"
            >
              {{ room.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchControlDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeBatchControl" :loading="batchControlLoading">
          执行批量控制
        </el-button>
      </template>
    </el-dialog>

    <!-- 全局音频广播对话框 -->
    <el-dialog
      v-model="globalAudioDialogVisible"
      title="全局音频广播"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="globalAudioForm" label-width="80px">
        <el-form-item label="广播内容">
          <el-input
            v-model="globalAudioForm.message"
            type="textarea"
            :rows="3"
            placeholder="请输入要广播的消息内容"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="globalAudioForm.type" placeholder="选择消息类型">
            <el-option label="通知" value="notice" />
            <el-option label="警告" value="warning" />
            <el-option label="提醒" value="reminder" />
          </el-select>
        </el-form-item>
        <el-form-item label="音量">
          <el-slider v-model="globalAudioForm.volume" :min="0" :max="100" show-input />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="globalAudioDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sendGlobalAudio" :loading="globalAudioLoading">
          发送全局广播
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRoomList } from '@/api/rooms.js'
import { controlRoomLock, controlRoomPower, getRoomDeviceStatus, getDevicesByRoom } from '@/api/devices.js'
import request from '@/api/request'
import { Refresh, Lock, Lightning, Microphone, Monitor, SwitchButton } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const roomDevices = ref([])
const mainLockDevice = ref(null)
let refreshTimer = null

// 设备统计
const deviceStats = reactive({
  total: 0,
  online: 0,
  offline: 0,
  fault: 0
})

// 音频对话框相关
const audioDialogVisible = ref(false)
const audioLoading = ref(false)
const selectedRoomId = ref(null)
const audioForm = reactive({
  message: '',
  type: 'notice',
  volume: 80
})

// 批量控制对话框相关
const batchControlDialogVisible = ref(false)
const batchControlLoading = ref(false)
const batchControlForm = reactive({
  type: '',
  roomIds: []
})

// 全局音频广播对话框相关
const globalAudioDialogVisible = ref(false)
const globalAudioLoading = ref(false)
const globalAudioForm = reactive({
  message: '',
  type: 'notice',
  volume: 80
})

// 获取主门禁锁设备
const fetchMainLockDevice = async () => {
  try {
    const response = await request({
      url: '/admin/devices/grouped',
      method: 'get'
    })

    if (response.code === 200) {
      const mainLockGroup = response.data.groups.find(group => group.type === 'main_lock')
      if (mainLockGroup && mainLockGroup.devices.length > 0) {
        mainLockDevice.value = mainLockGroup.devices[0]
      }
    }
  } catch (error) {
    console.warn('获取主门禁锁失败:', error)
  }
}

// 获取房间设备列表
const fetchRoomDevices = async () => {
  try {
    loading.value = true
    const response = await getRoomList({ page: 1, page_size: 100 })

    if (response && response.code === 200 && response.data) {
      const rooms = response.data.data || []
      // 为每个房间获取设备信息
      roomDevices.value = await Promise.all(
        rooms.map(async (room) => {
          try {
            const deviceResponse = await getDevicesByRoom(room.id)
            // 映射后端数据结构到前端期望的结构
            const backendDevices = deviceResponse.data || {}
            return {
              ...room,
              devices: {
                room_lock: backendDevices.room_lock || { status: 'offline', lastUpdate: new Date().toISOString() },
                power: backendDevices.power || { status: 'offline', lastUpdate: new Date().toISOString() },
                speaker: backendDevices.speaker || { status: 'offline', lastUpdate: new Date().toISOString() }
              }
            }
          } catch (error) {
            console.warn(`获取房间${room.id}设备失败:`, error)
            return {
              ...room,
              devices: {
                room_lock: { status: 'offline', lastUpdate: new Date().toISOString() },
                power: { status: 'offline', lastUpdate: new Date().toISOString() },
                speaker: { status: 'offline', lastUpdate: new Date().toISOString() }
              }
            }
          }
        })
      )
    } else {
      throw new Error('API响应格式异常')
    }

    // 获取主门禁锁设备
    await fetchMainLockDevice()

    updateDeviceStats()
  } catch (error) {
    console.error('获取房间设备失败:', error)
    ElMessage.error(`获取设备列表失败: ${error.message || '网络错误'}`)
    roomDevices.value = []
  } finally {
    loading.value = false
  }
}



// 更新设备统计
const updateDeviceStats = () => {
  let total = 0
  let online = 0
  let offline = 0
  let fault = 0

  // 统计房间设备
  roomDevices.value.forEach(room => {
    Object.values(room.devices).forEach(device => {
      total++
      switch (device.status) {
        case 'online':
          online++
          break
        case 'offline':
          offline++
          break
        case 'fault':
          fault++
          break
      }
    })
  })

  // 统计主门禁锁
  if (mainLockDevice.value) {
    total++
    switch (mainLockDevice.value.status) {
      case 'online':
        online++
        break
      case 'offline':
        offline++
        break
      case 'fault':
        fault++
        break
    }
  }

  deviceStats.total = total
  deviceStats.online = online
  deviceStats.offline = offline
  deviceStats.fault = fault
}

// 处理主门禁锁控制
const handleControlMainLock = async (action) => {
  try {
    if (!mainLockDevice.value) {
      ElMessage.error('主门禁锁设备不存在')
      return
    }

    ElMessageBox.confirm(
      `确定要${action === 'unlock' ? '开锁' : '锁定'}主门禁锁吗？`,
      '主门禁锁控制确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      await request({
        url: `/mqtt/device/${mainLockDevice.value.id}/control`,
        method: 'post',
        data: { action: action }
      })

      ElMessage.success('主门禁锁控制成功')

      // 刷新设备状态
      setTimeout(() => {
        fetchMainLockDevice()
        updateDeviceStats()
      }, 1000)
    })

  } catch (error) {
    console.error('主门禁锁控制失败:', error)
    ElMessage.error(`主门禁锁控制失败: ${error.message || '网络错误'}`)
  }
}

// 控制设备
const handleControlDevice = async (roomId, deviceType, action) => {
  try {
    ElMessageBox.confirm(
      `确定要执行 ${action} 操作吗？`,
      '设备控制确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      // 根据设备类型调用不同的API
      if (deviceType === 'room_lock') {
        await controlRoomLock(roomId, action)
      } else if (deviceType === 'power') {
        await controlRoomPower(roomId, action)
      } else if (deviceType === 'speaker') {
        // 音响设备控制
        await request({
          url: `/mqtt/room/${roomId}/audio`,
          method: 'post',
          data: { action: action }
        })
      }

      ElMessage.success('设备控制成功')

      // 刷新设备状态
      setTimeout(() => {
        refreshRoomDevices(roomId)
      }, 1000)
    })
  } catch (error) {
    console.error('设备控制失败:', error)
    ElMessage.error(`设备控制失败: ${error.message || '网络错误'}`)
  }
}

// 显示音频对话框
const showAudioDialog = (roomId) => {
  selectedRoomId.value = roomId
  audioForm.message = ''
  audioForm.type = 'notice'
  audioForm.volume = 80
  audioDialogVisible.value = true
}

// 显示批量控制对话框
const showBatchControlDialog = () => {
  batchControlForm.type = ''
  batchControlForm.roomIds = []
  batchControlDialogVisible.value = true
}

// 显示全局音频广播对话框
const showGlobalAudioDialog = () => {
  globalAudioForm.message = ''
  globalAudioForm.type = 'notice'
  globalAudioForm.volume = 80
  globalAudioDialogVisible.value = true
}

// 执行批量控制
const executeBatchControl = async () => {
  try {
    if (!batchControlForm.type) {
      ElMessage.error('请选择控制类型')
      return
    }
    if (batchControlForm.roomIds.length === 0) {
      ElMessage.error('请选择目标房间')
      return
    }

    batchControlLoading.value = true

    let apiUrl = ''
    switch (batchControlForm.type) {
      case 'power_off':
        apiUrl = '/admin/devices/batch/power-off'
        break
      case 'unlock_all':
        apiUrl = '/admin/devices/batch/lock'
        break
      default:
        ElMessage.error('不支持的控制类型')
        return
    }

    const response = await request({
      url: apiUrl,
      method: 'post',
      data: { roomIds: batchControlForm.roomIds }
    })

    if (response.code === 200) {
      ElMessage.success('批量控制执行成功')
      batchControlDialogVisible.value = false
      // 刷新设备状态
      setTimeout(() => {
        fetchRoomDevices()
      }, 1000)
    }
  } catch (error) {
    ElMessage.error('批量控制执行失败')
    console.error('批量控制失败:', error)
  } finally {
    batchControlLoading.value = false
  }
}

// 发送全局音频广播
const sendGlobalAudio = async () => {
  try {
    if (!globalAudioForm.message.trim()) {
      ElMessage.error('请输入广播内容')
      return
    }

    globalAudioLoading.value = true
    const response = await request({
      url: '/admin/devices/broadcast/audio',
      method: 'post',
      data: globalAudioForm
    })

    if (response.code === 200) {
      ElMessage.success('全局音频广播已发送')
      globalAudioDialogVisible.value = false
    }
  } catch (error) {
    ElMessage.error('发送全局音频广播失败')
    console.error('发送全局音频广播失败:', error)
  } finally {
    globalAudioLoading.value = false
  }
}

// 发送音频消息
const sendAudioMessage = async () => {
  if (!audioForm.message.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }

  try {
    audioLoading.value = true
    const response = await request({
      url: `/mqtt/room/${selectedRoomId.value}/audio`,
      method: 'post',
      data: audioForm
    })

    if (response.data.code === 200) {
      ElMessage.success('音频消息已发送')
      audioDialogVisible.value = false
    }
  } catch (error) {
    ElMessage.error('发送音频消息失败')
    console.error('发送音频消息失败:', error)
  } finally {
    audioLoading.value = false
  }
}

// 刷新房间设备状态
const refreshRoomDevices = async (roomId) => {
  try {
    const response = await getRoomDeviceStatus(roomId)

    if (response.code === 200) {
      const roomIndex = roomDevices.value.findIndex(room => room.id === roomId)
      if (roomIndex !== -1) {
        roomDevices.value[roomIndex].devices = response.data
      }
    } else {
      throw new Error('刷新设备状态失败')
    }

    updateDeviceStats()
    ElMessage.success('设备状态已刷新')
  } catch (error) {
    console.error('刷新设备状态失败:', error)
    ElMessage.error(`刷新设备状态失败: ${error.message || '网络错误'}`)
  }
}

// 刷新所有设备
const refreshAllDevices = () => {
  fetchRoomDevices()
  ElMessage.success('正在刷新所有设备状态...')
}

// 查看房间详情
const viewRoomDetail = (room) => {
  router.push(`/rooms/edit/${room.id}`)
}

// 工具函数
const getRoomStatusType = (status) => {
  const types = {
    available: 'success',
    occupied: 'warning',
    maintenance: 'danger'
  }
  return types[status] || 'info'
}

const getRoomStatusText = (status) => {
  const texts = {
    available: '可用',
    occupied: '占用',
    maintenance: '维护'
  }
  return texts[status] || '未知'
}

const getDeviceStatusType = (status) => {
  const types = {
    online: 'success',
    offline: 'info',
    fault: 'danger'
  }
  return types[status] || 'info'
}

const getDeviceStatusText = (status) => {
  const texts = {
    online: '在线',
    offline: '离线',
    fault: '故障'
  }
  return texts[status] || '未知'
}

// 安全获取设备状态
const getDeviceStatus = (room, deviceType) => {
  return room?.devices?.[deviceType]?.status || 'offline'
}

// 安全检查设备是否在线
const isDeviceOnline = (room, deviceType) => {
  return getDeviceStatus(room, deviceType) === 'online'
}

// 定时刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    fetchRoomDevices()
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 初始化
onMounted(() => {
  fetchRoomDevices()
  startAutoRefresh()

  // 处理URL参数，如果有room_id则滚动到对应房间
  const roomId = route.query.room_id
  if (roomId) {
    console.log('从房间列表跳转，房间ID:', roomId)
    // 等待数据加载完成后滚动到对应房间
    setTimeout(() => {
      const roomElement = document.querySelector(`[data-room-id="${roomId}"]`)
      if (roomElement) {
        roomElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
        // 高亮显示该房间
        roomElement.style.border = '2px solid #409eff'
        setTimeout(() => {
          roomElement.style.border = ''
        }, 3000)
      }
    }, 1000)
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.device-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 500;
  color: #262626;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-card.online .stats-value {
  color: #67C23A;
}

.stats-card.offline .stats-value {
  color: #909399;
}

.stats-card.fault .stats-value {
  color: #F56C6C;
}

.stats-content {
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #8c8c8c;
}

.main-lock-section {
  margin-top: 20px;
  margin-bottom: 20px;
}

.main-lock-card {
  border: 2px solid #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.main-lock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main-lock-title {
  font-weight: 600;
  font-size: 18px;
  color: #409eff;
}

.main-lock-content {
  padding: 10px 0;
}

.room-devices {
  margin-bottom: 20px;
}

.room-card-col {
  margin-bottom: 20px;
}

.room-device-card {
  height: 100%;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-name {
  font-weight: 500;
  font-size: 16px;
}

.device-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.device-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
}

.device-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.device-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
}

.device-status {
  margin-bottom: 8px;
}

.device-controls {
  display: flex;
  gap: 8px;
}

.device-controls .el-button {
  flex: 1;
  font-size: 12px;
  padding: 4px 8px;
}

.offline-text {
  color: #909399;
  font-size: 12px;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
}

.room-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
  }

  .stats-row .el-col {
    margin-bottom: 16px;
  }

  .room-devices .el-col {
    span: 24;
  }

  .device-grid {
    grid-template-columns: 1fr;
  }
}
</style>
