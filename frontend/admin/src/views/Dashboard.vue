<template>
  <div class="dashboard">
    <!-- 核心数据概览区 -->
    <div class="overview-section">
      <el-row :gutter="20" class="overview-row">
        <!-- 核心业务指标 -->
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
          <el-card class="metrics-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span class="header-title">
                  <el-icon><DataBoard /></el-icon>
                  实时业务概览
                </span>
                <div class="header-actions">
                  <el-button type="text" size="small" @click="refreshAllData">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </div>
              </div>
            </template>

            <div class="metrics-grid">
              <div class="metric-item primary">
                <div class="metric-icon income">
                  <el-icon size="28"><Money /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value" data-testid="today-income">¥{{ dashboardData.todayIncome.toFixed(2) }}</div>
                  <div class="metric-label">今日收入</div>
                  <div class="metric-change positive">
                    <el-icon><TrendCharts /></el-icon>
                    <span>较昨日</span>
                  </div>
                </div>
              </div>

              <div class="metric-item">
                <div class="metric-icon orders">
                  <el-icon size="24"><Document /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value" data-testid="active-orders">{{ dashboardData.activeOrders || 0 }}</div>
                  <div class="metric-label">活跃订单</div>
                  <div class="metric-status">
                    <el-tag size="small" :type="dashboardData.activeOrders > 0 ? 'success' : 'info'">
                      {{ dashboardData.activeOrders > 0 ? '有订单' : '无订单' }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <div class="metric-item">
                <div class="metric-icon rooms">
                  <el-icon size="24"><House /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">
                    <span data-testid="available-rooms">{{ dashboardData.availableRooms || 0 }}</span>
                    <span class="metric-total">/ {{ dashboardData.totalRooms || 0 }}</span>
                  </div>
                  <div class="metric-label">空闲房间</div>
                  <div class="metric-status">
                    <el-tag size="small" :type="getRoomStatusType()">
                      {{ getRoomStatusText() }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <div class="metric-item">
                <div class="metric-icon devices">
                  <el-icon size="24"><Monitor /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">
                    <span data-testid="online-devices">{{ dashboardData.onlineDevices || 0 }}</span>
                    <span class="metric-total">/ {{ (dashboardData.onlineDevices || 0) + (dashboardData.offlineDevices || 0) }}</span>
                  </div>
                  <div class="metric-label">在线设备</div>
                  <div class="metric-status">
                    <el-tag size="small" :type="getDeviceStatusType()">
                      {{ getDeviceStatusText() }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 快捷操作区 -->
        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <el-card class="actions-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span class="header-title">
                  <el-icon><Operation /></el-icon>
                  快捷操作
                </span>
              </div>
            </template>

            <div class="quick-actions-grid">
              <div class="action-item primary" @click="$router.push('/orders/list')">
                <el-icon size="20"><Document /></el-icon>
                <span>订单管理</span>
              </div>

              <div class="action-item" @click="$router.push('/rooms/list')">
                <el-icon size="20"><House /></el-icon>
                <span>房间管理</span>
              </div>

              <div class="action-item" @click="$router.push('/devices/list')">
                <el-icon size="20"><Monitor /></el-icon>
                <span>设备管理</span>
              </div>

              <div class="action-item" @click="$router.push('/finance/report')">
                <el-icon size="20"><DataAnalysis /></el-icon>
                <span>财务报表</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 实时监控图表区 -->
    <div class="monitoring-section">
      <el-card class="realtime-chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="header-title">
              <el-icon><TrendCharts /></el-icon>
              实时数据趋势
            </span>
            <div class="header-actions">
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                size="small"
                @change="toggleAutoRefresh"
              />
              <el-button type="text" size="small" @click="refreshRealtimeData">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>
        </template>

        <div class="realtime-chart-container">
          <RealTimeChart />
        </div>
      </el-card>
    </div>

    <!-- 业务分析图表区 -->
    <div class="analytics-section">
      <el-row :gutter="20">
        <!-- 房间状态分布 -->
        <el-col :xs="24" :sm="24" :md="10" :lg="8" :xl="8">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span class="header-title">
                  <el-icon><PieChart /></el-icon>
                  房间状态分布
                </span>
                <el-button type="text" size="small" @click="refreshRoomStats">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-content">
              <RoomStatusChart :data="roomStats" />
            </div>
          </el-card>
        </el-col>

        <!-- 收入趋势分析 -->
        <el-col :xs="24" :sm="24" :md="14" :lg="16" :xl="16">
          <el-card class="chart-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span class="header-title">
                  <el-icon><LineChart /></el-icon>
                  收入趋势分析
                </span>
                <div class="header-actions">
                  <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    @change="handleDateChange"
                  />
                </div>
              </div>
            </template>
            <div class="chart-content">
              <IncomeChart :data="incomeChartData" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import RoomStatusChart from '@/components/charts/RoomStatusChart.vue'
import IncomeChart from '@/components/charts/IncomeChart.vue'
import RealTimeChart from '@/components/charts/RealTimeChart.vue'

const dashboardStore = useDashboardStore()

// 响应式数据
const dateRange = ref([])
const incomeChartData = ref([])
const autoRefresh = ref(true)
let refreshTimer = null

// 计算属性
const dashboardData = computed(() => dashboardStore.dashboardData)
const roomStats = computed(() => dashboardStore.roomStats)
const loading = computed(() => dashboardStore.loading)

// 获取房间状态类型
const getRoomStatusType = () => {
  const available = dashboardData.value.availableRooms || 0
  const total = dashboardData.value.totalRooms || 0
  const ratio = total > 0 ? available / total : 0

  if (ratio >= 0.8) return 'success'
  if (ratio >= 0.5) return 'warning'
  return 'danger'
}

// 获取房间状态文本
const getRoomStatusText = () => {
  const available = dashboardData.value.availableRooms || 0
  const total = dashboardData.value.totalRooms || 0
  const ratio = total > 0 ? available / total : 0

  if (ratio >= 0.8) return '充足'
  if (ratio >= 0.5) return '适中'
  return '紧张'
}

// 获取设备状态类型
const getDeviceStatusType = () => {
  const online = dashboardData.value.onlineDevices || 0
  const total = (dashboardData.value.onlineDevices || 0) + (dashboardData.value.offlineDevices || 0)
  const ratio = total > 0 ? online / total : 0

  if (ratio >= 0.8) return 'success'
  if (ratio >= 0.3) return 'warning'
  return 'danger'
}

// 获取设备状态文本
const getDeviceStatusText = () => {
  const online = dashboardData.value.onlineDevices || 0
  const total = (dashboardData.value.onlineDevices || 0) + (dashboardData.value.offlineDevices || 0)
  const ratio = total > 0 ? online / total : 0

  if (ratio >= 0.8) return '良好'
  if (ratio >= 0.3) return '一般'
  return '异常'
}

// 刷新所有数据
const refreshAllData = async () => {
  await dashboardStore.refreshAll()
}

// 刷新房间统计
const refreshRoomStats = async () => {
  await dashboardStore.fetchRoomStatistics()
}

// 刷新实时数据
const refreshRealtimeData = async () => {
  await dashboardStore.fetchDashboardData()
}

// 切换自动刷新
const toggleAutoRefresh = (value) => {
  if (value) {
    refreshTimer = setInterval(refreshAllData, 30000) // 每30秒刷新
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 处理日期范围变化
const handleDateChange = async (dates) => {
  if (dates && dates.length === 2) {
    const startDate = dates[0].toISOString().split('T')[0]
    const endDate = dates[1].toISOString().split('T')[0]
    await dashboardStore.fetchIncomeReport(startDate, endDate)
    incomeChartData.value = dashboardStore.incomeStats.chartData
  }
}

// 初始化
onMounted(async () => {
  // 设置默认日期范围（最近7天）
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)
  dateRange.value = [startDate, endDate]

  // 加载数据
  await dashboardStore.refreshAll()
  await handleDateChange(dateRange.value)

  // 启动自动刷新
  if (autoRefresh.value) {
    toggleAutoRefresh(true)
  }
})

// 清理
onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
.dashboard {
  padding: 0;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 概览区域 */
.overview-section {
  margin-bottom: 24px;
}

.overview-row {
  margin-bottom: 0;
}

.metrics-card,
.actions-card {
  height: 100%;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.metrics-card:hover,
.actions-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #262626;
  font-size: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 8px 0;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-item:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.metric-item.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.metric-item.primary .metric-value,
.metric-item.primary .metric-label {
  color: white;
}

/* 指标图标 */
.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-icon.income {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.metric-icon.orders {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.metric-icon.rooms {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.metric-icon.devices {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.metric-item.primary .metric-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 指标内容 */
.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.2;
}

.metric-total {
  font-size: 16px;
  font-weight: 400;
  color: #8c8c8c;
  margin-left: 4px;
}

.metric-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.metric-status,
.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.metric-change.positive {
  color: rgba(255, 255, 255, 0.8);
}

/* 快捷操作网格 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 16px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.action-item:hover {
  border-color: #1890ff;
  background: #f6ffed;
  transform: translateY(-2px);
}

.action-item.primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  border: none;
}

.action-item.primary:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
}

.action-item span {
  font-size: 13px;
  font-weight: 500;
  color: #666;
}

.action-item.primary span {
  color: white;
}

/* 监控区域 */
.monitoring-section {
  margin-bottom: 24px;
}

.realtime-chart-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.realtime-chart-container {
  min-height: 300px;
}

/* 分析区域 */
.analytics-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 420px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.chart-content {
  height: calc(100% - 60px);
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0 8px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .metric-item {
    padding: 16px;
  }

  .metric-value {
    font-size: 20px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .chart-card {
    height: 350px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .metric-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .metric-icon {
    width: 48px;
    height: 48px;
  }

  .action-item {
    padding: 16px 12px;
  }
}
</style>
