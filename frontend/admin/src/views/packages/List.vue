<template>
  <div class="packages-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>套餐管理</h2>
        <p>管理系统中的所有套餐</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增套餐
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total }}</div>
              <div class="stat-label">总套餐数</div>
            </div>
            <div class="stat-icon">
              <el-icon><Box /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.active }}</div>
              <div class="stat-label">启用套餐</div>
            </div>
            <div class="stat-icon">
              <el-icon><Check /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total_sales }}</div>
              <div class="stat-label">总销量</div>
            </div>
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ stats.total_revenue }}</div>
              <div class="stat-label">总收入</div>
            </div>
            <div class="stat-icon">
              <el-icon><Money /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="套餐类型">
          <el-select v-model="filters.type" placeholder="全部类型" clearable @change="handleFilter">
            <el-option label="固定时长" value="fixed_duration" />
            <el-option label="灵活续费" value="flexible_recharge" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.is_active" placeholder="全部状态" clearable @change="handleFilter">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐名称">
          <el-input
            v-model="filters.name"
            placeholder="请输入套餐名称"
            clearable
            @input="handleFilter"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 套餐列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="packages"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="套餐名称" min-width="150" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="row.type === 'fixed_duration' ? 'primary' : 'success'">
              {{ row.type === 'fixed_duration' ? '固定时长' : '灵活续费' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration_hours" label="时长" width="100">
          <template #default="{ row }">
            {{ row.duration_hours ? `${row.duration_hours}小时` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="original_price" label="原价" width="100">
          <template #default="{ row }">
            ¥{{ row.original_price }}
          </template>
        </el-table-column>
        <el-table-column prop="sale_price" label="售价" width="100">
          <template #default="{ row }">
            ¥{{ row.sale_price }}
          </template>
        </el-table-column>
        <el-table-column prop="discount_rate" label="折扣" width="100">
          <template #default="{ row }">
            {{ row.discount_rate }}%
          </template>
        </el-table-column>
        <el-table-column prop="sales_count" label="销量" width="80">
          <template #default="{ row }">
            {{ row.sales_count || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="revenue" label="收入" width="100">
          <template #default="{ row }">
            ¥{{ row.revenue || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleView(row)">
              详情
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Box, Check, TrendCharts, Money } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getPackageList, getPackageStats, updatePackageStatus, deletePackage } from '@/api/packages'
import { createRetryableApi } from '@/utils/errorHandler'

const router = useRouter()

// 创建带重试的API调用
const getPackageListWithRetry = createRetryableApi(getPackageList, '获取套餐列表')
const getPackageStatsWithRetry = createRetryableApi(getPackageStats, '获取套餐统计')

// 响应式数据
const loading = ref(false)
const packages = ref([])
const stats = ref({
  total: 0,
  active: 0,
  total_sales: 0,
  total_revenue: 0
})

// 筛选条件
const filters = reactive({
  type: '',
  is_active: null,
  name: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取套餐列表
const fetchPackages = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...filters
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key]
      }
    })

    const response = await getPackageListWithRetry(params)
    if (response.code === 200) {
      packages.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      throw new Error(response.message || '获取套餐列表失败')
    }
  } catch (error) {
    console.error('获取套餐列表失败:', error)

    // 只有在真正的错误情况下才重置数据
    if (error.response?.status >= 500 || !error.response) {
      packages.value = []
      pagination.total = 0
    }

    // 对于分页请求失败，显示更友好的提示
    if (pagination.page > 1) {
      ElMessage.warning('页面加载失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await getPackageStatsWithRetry()
    if (response.code === 200) {
      stats.value = response.data || {
        total: 0,
        active: 0,
        total_sales: 0,
        total_revenue: 0
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 统计数据失败不影响主要功能，静默处理
  }
}

// 处理筛选
const handleFilter = () => {
  pagination.page = 1
  fetchPackages()
}

// 重置筛选
const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'is_active' ? null : ''
  })
  pagination.page = 1
  fetchPackages()
}

// 处理状态变更
const handleStatusChange = async (row) => {
  try {
    await updatePackageStatus(row.id, row.is_active)
    ElMessage.success('状态更新成功')
    fetchStats()
  } catch (error) {
    ElMessage.error('状态更新失败')
    row.is_active = !row.is_active // 回滚状态
  }
}

// 处理创建
const handleCreate = () => {
  router.push('/packages/create')
}

// 处理编辑
const handleEdit = (row) => {
  router.push(`/packages/edit/${row.id}`)
}

// 处理查看详情
const handleView = (row) => {
  router.push(`/packages/detail/${row.id}`)
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除套餐"${row.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deletePackage(row.id)
    ElMessage.success('删除成功')
    fetchPackages()
    fetchStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.page = 1
  fetchPackages()
}

const handleCurrentChange = (val) => {
  pagination.page = val
  fetchPackages()
}

// 初始化
onMounted(() => {
  fetchPackages()
  fetchStats()
})
</script>

<style scoped>
.packages-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  font-size: 40px;
  color: #409EFF;
  opacity: 0.8;
}

.filter-card,
.table-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
