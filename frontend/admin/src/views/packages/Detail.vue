<template>
  <div class="package-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>套餐详情</h2>
        <p>查看套餐的详细信息和销售数据</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
      </div>
    </div>

    <div v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="info-card" v-if="packageInfo">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="packageInfo.is_active ? 'success' : 'danger'">
              {{ packageInfo.is_active ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>套餐名称：</label>
              <span>{{ packageInfo.name }}</span>
            </div>
            <div class="info-item">
              <label>套餐类型：</label>
              <el-tag :type="packageInfo.type === 'fixed_duration' ? 'primary' : 'success'">
                {{ packageInfo.type === 'fixed_duration' ? '固定时长' : '灵活续费' }}
              </el-tag>
            </div>
            <div class="info-item" v-if="packageInfo.duration_hours">
              <label>套餐时长：</label>
              <span>{{ packageInfo.duration_hours }}小时</span>
            </div>
            <div class="info-item" v-if="packageInfo.type === 'flexible_recharge'">
              <label>续费范围：</label>
              <span>{{ packageInfo.min_recharge_hours }}-{{ packageInfo.max_recharge_hours }}小时</span>
            </div>
            <div class="info-item">
              <label>原价：</label>
              <span class="price">¥{{ packageInfo.original_price }}</span>
            </div>
            <div class="info-item">
              <label>售价：</label>
              <span class="price sale-price">¥{{ packageInfo.sale_price }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>折扣率：</label>
              <span class="discount">{{ packageInfo.discount_rate }}%</span>
            </div>
            <div class="info-item">
              <label>有效期：</label>
              <span>{{ packageInfo.valid_days }}天</span>
            </div>
            <div class="info-item">
              <label>排序：</label>
              <span>{{ packageInfo.sort_order }}</span>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(packageInfo.created_at) }}</span>
            </div>
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ formatDate(packageInfo.updated_at) }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="info-item full-width">
          <label>套餐描述：</label>
          <p class="description">{{ packageInfo.description || '暂无描述' }}</p>
        </div>

        <div class="info-item full-width">
          <label>特色功能：</label>
          <div class="features">
            <el-tag
              v-for="feature in packageInfo.features"
              :key="feature"
              class="feature-tag"
              type="info"
            >
              {{ feature }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 销售统计 -->
      <el-card class="stats-card" v-if="packageInfo">
        <template #header>
          <span>销售统计</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ packageInfo.sales_count || 0 }}</div>
              <div class="stat-label">总销量</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">¥{{ packageInfo.revenue || 0 }}</div>
              <div class="stat-label">总收入</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">
                ¥{{ packageInfo.sales_count ? (packageInfo.revenue / packageInfo.sales_count).toFixed(2) : 0 }}
              </div>
              <div class="stat-label">平均单价</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 价格对比 -->
      <el-card class="price-card" v-if="packageInfo">
        <template #header>
          <span>价格分析</span>
        </template>
        
        <div class="price-comparison">
          <div class="price-item">
            <div class="price-label">原价</div>
            <div class="price-value original">¥{{ packageInfo.original_price }}</div>
          </div>
          <div class="price-arrow">
            <el-icon><Right /></el-icon>
          </div>
          <div class="price-item">
            <div class="price-label">售价</div>
            <div class="price-value sale">¥{{ packageInfo.sale_price }}</div>
          </div>
          <div class="price-arrow">
            <el-icon><Right /></el-icon>
          </div>
          <div class="price-item">
            <div class="price-label">优惠</div>
            <div class="price-value discount">
              ¥{{ (packageInfo.original_price - packageInfo.sale_price).toFixed(2) }}
            </div>
          </div>
        </div>

        <div class="price-analysis">
          <div class="analysis-item">
            <span class="label">折扣率：</span>
            <span class="value">{{ packageInfo.discount_rate }}%</span>
          </div>
          <div class="analysis-item" v-if="packageInfo.duration_hours">
            <span class="label">每小时价格：</span>
            <span class="value">¥{{ (packageInfo.sale_price / packageInfo.duration_hours).toFixed(2) }}</span>
          </div>
          <div class="analysis-item">
            <span class="label">性价比：</span>
            <span class="value">
              {{ packageInfo.discount_rate > 20 ? '高' : packageInfo.discount_rate > 10 ? '中' : '低' }}
            </span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit, Right } from '@element-plus/icons-vue'
import { getPackageDetail } from '@/api/packages'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const packageInfo = ref(null)

// 获取套餐详情
const fetchPackageDetail = async () => {
  try {
    loading.value = true
    const response = await getPackageDetail(route.params.id)
    packageInfo.value = response.data
  } catch (error) {
    ElMessage.error('获取套餐详情失败')
    console.error('获取套餐详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 处理编辑
const handleEdit = () => {
  router.push(`/packages/edit/${route.params.id}`)
}

// 初始化
onMounted(() => {
  fetchPackageDetail()
})
</script>

<style scoped>
.package-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.info-card,
.stats-card,
.price-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-item.full-width {
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.price {
  font-weight: bold;
  font-size: 16px;
}

.sale-price {
  color: #E6A23C;
}

.discount {
  color: #67C23A;
  font-weight: bold;
}

.description {
  margin: 10px 0 0 0;
  color: #303133;
  line-height: 1.6;
}

.features {
  margin-top: 10px;
}

.feature-tag {
  margin-right: 10px;
  margin-bottom: 5px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.price-comparison {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.price-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  min-width: 120px;
}

.price-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.price-value {
  font-size: 20px;
  font-weight: bold;
}

.price-value.original {
  color: #909399;
  text-decoration: line-through;
}

.price-value.sale {
  color: #E6A23C;
}

.price-value.discount {
  color: #67C23A;
}

.price-arrow {
  margin: 0 20px;
  font-size: 20px;
  color: #C0C4CC;
}

.price-analysis {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background-color: #F5F7FA;
  border-radius: 8px;
}

.analysis-item {
  text-align: center;
}

.analysis-item .label {
  display: block;
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.analysis-item .value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}
</style>
