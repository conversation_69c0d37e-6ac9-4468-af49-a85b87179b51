<template>
  <div class="package-edit">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>编辑套餐</h2>
        <p>修改套餐信息</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 套餐表单 -->
    <el-card v-loading="loading" class="form-card">
      <el-form
        ref="formRef"
        :model="packageForm"
        :rules="formRules"
        label-width="120px"
        size="large"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="套餐名称" prop="name">
                <el-input
                  v-model="packageForm.name"
                  placeholder="请输入套餐名称"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="套餐类型" prop="type">
                <el-select
                  v-model="packageForm.type"
                  placeholder="请选择套餐类型"
                  @change="handleTypeChange"
                  style="width: 100%"
                  disabled
                >
                  <el-option label="固定时长套餐" value="fixed_duration" />
                  <el-option label="灵活续费套餐" value="flexible_recharge" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="packageForm.type === 'fixed_duration'">
            <el-col :span="12">
              <el-form-item label="套餐时长" prop="duration_hours">
                <el-input-number
                  v-model="packageForm.duration_hours"
                  :min="1"
                  :max="24"
                  placeholder="小时"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="packageForm.type === 'flexible_recharge'">
            <el-col :span="12">
              <el-form-item label="最小续费时长" prop="min_recharge_hours">
                <el-input-number
                  v-model="packageForm.min_recharge_hours"
                  :min="1"
                  :max="24"
                  placeholder="小时"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大续费时长" prop="max_recharge_hours">
                <el-input-number
                  v-model="packageForm.max_recharge_hours"
                  :min="1"
                  :max="24"
                  placeholder="小时"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="套餐描述" prop="description">
            <el-input
              v-model="packageForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入套餐描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 价格设置 -->
        <div class="form-section">
          <h3>价格设置</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="原价" prop="original_price">
                <el-input-number
                  v-model="packageForm.original_price"
                  :min="0"
                  :precision="2"
                  placeholder="元"
                  style="width: 100%"
                  @change="calculateDiscount"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="售价" prop="sale_price">
                <el-input-number
                  v-model="packageForm.sale_price"
                  :min="0"
                  :precision="2"
                  placeholder="元"
                  style="width: 100%"
                  @change="calculateDiscount"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="折扣率">
                <el-input
                  :value="discountRate + '%'"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 特色功能 -->
        <div class="form-section">
          <h3>特色功能</h3>
          <el-form-item label="功能特色" prop="features">
            <div class="features-input">
              <el-tag
                v-for="(feature, index) in packageForm.features"
                :key="index"
                closable
                @close="removeFeature(index)"
                class="feature-tag"
              >
                {{ feature }}
              </el-tag>
              <el-input
                v-if="inputVisible"
                ref="inputRef"
                v-model="inputValue"
                size="small"
                @keyup.enter="handleInputConfirm"
                @blur="handleInputConfirm"
                class="feature-input"
              />
              <el-button
                v-else
                size="small"
                @click="showInput"
                class="add-feature-btn"
              >
                + 添加特色
              </el-button>
            </div>
          </el-form-item>
        </div>

        <!-- 其他设置 -->
        <div class="form-section">
          <h3>其他设置</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="有效期" prop="valid_days">
                <el-input-number
                  v-model="packageForm.valid_days"
                  :min="1"
                  :max="365"
                  placeholder="天"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="排序" prop="sort_order">
                <el-input-number
                  v-model="packageForm.sort_order"
                  :min="0"
                  placeholder="数字越大排序越靠前"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="状态" prop="is_active">
                <el-switch
                  v-model="packageForm.is_active"
                  active-text="启用"
                  inactive-text="禁用"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button size="large" @click="$router.go(-1)">取消</el-button>
          <el-button type="primary" size="large" @click="handleSubmit" :loading="loading">
            保存修改
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { getPackageDetail, updatePackage } from '@/api/packages'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const formRef = ref()
const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

// 表单数据
const packageForm = reactive({
  name: '',
  type: '',
  duration_hours: null,
  min_recharge_hours: null,
  max_recharge_hours: null,
  original_price: null,
  sale_price: null,
  description: '',
  features: [],
  valid_days: 30,
  sort_order: 0,
  is_active: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入套餐名称', trigger: 'blur' },
    { min: 2, max: 100, message: '套餐名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择套餐类型', trigger: 'change' }
  ],
  duration_hours: [
    { required: true, message: '请输入套餐时长', trigger: 'blur' }
  ],
  min_recharge_hours: [
    { required: true, message: '请输入最小续费时长', trigger: 'blur' }
  ],
  max_recharge_hours: [
    { required: true, message: '请输入最大续费时长', trigger: 'blur' }
  ],
  original_price: [
    { required: true, message: '请输入原价', trigger: 'blur' }
  ],
  sale_price: [
    { required: true, message: '请输入售价', trigger: 'blur' }
  ],
  valid_days: [
    { required: true, message: '请输入有效期', trigger: 'blur' }
  ]
}

// 计算折扣率
const discountRate = computed(() => {
  if (packageForm.original_price && packageForm.sale_price) {
    const rate = ((packageForm.original_price - packageForm.sale_price) / packageForm.original_price * 100)
    return Math.max(0, Math.min(100, rate)).toFixed(2)
  }
  return '0.00'
})

// 获取套餐详情
const fetchPackageDetail = async () => {
  try {
    loading.value = true
    const response = await getPackageDetail(route.params.id)
    const data = response.data
    
    // 填充表单数据
    Object.keys(packageForm).forEach(key => {
      if (data[key] !== undefined) {
        packageForm[key] = data[key]
      }
    })
  } catch (error) {
    ElMessage.error('获取套餐详情失败')
    console.error('获取套餐详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理类型变更
const handleTypeChange = (type) => {
  if (type === 'fixed_duration') {
    packageForm.min_recharge_hours = null
    packageForm.max_recharge_hours = null
  } else {
    packageForm.duration_hours = null
    packageForm.min_recharge_hours = 1
    packageForm.max_recharge_hours = 24
  }
}

// 计算折扣
const calculateDiscount = () => {
  // 折扣率会自动计算
}

// 显示输入框
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 确认输入
const handleInputConfirm = () => {
  if (inputValue.value && !packageForm.features.includes(inputValue.value)) {
    packageForm.features.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 移除特色
const removeFeature = (index) => {
  packageForm.features.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 验证价格
    if (packageForm.sale_price > packageForm.original_price) {
      ElMessage.error('售价不能高于原价')
      return
    }
    
    // 验证续费时长范围
    if (packageForm.type === 'flexible_recharge') {
      if (packageForm.min_recharge_hours >= packageForm.max_recharge_hours) {
        ElMessage.error('最小续费时长必须小于最大续费时长')
        return
      }
    }
    
    loading.value = true
    
    // 准备提交数据
    const submitData = { ...packageForm }
    
    // 计算折扣率
    submitData.discount_rate = parseFloat(discountRate.value)
    
    // 根据类型清理不需要的字段
    if (submitData.type === 'fixed_duration') {
      delete submitData.min_recharge_hours
      delete submitData.max_recharge_hours
    } else {
      delete submitData.duration_hours
    }
    
    await updatePackage(route.params.id, submitData)
    ElMessage.success('套餐更新成功')
    router.push('/packages')
  } catch (error) {
    if (error.errors) {
      // 显示验证错误
      Object.keys(error.errors).forEach(key => {
        ElMessage.error(error.errors[key][0])
      })
    } else {
      ElMessage.error('更新失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchPackageDetail()
})
</script>

<style scoped>
.package-edit {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.form-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.form-section:last-of-type {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.features-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.feature-tag {
  margin: 0;
}

.feature-input {
  width: 120px;
}

.add-feature-btn {
  border-style: dashed;
}

.form-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 10px;
}
</style>
