/* ===== 现代化组件样式 ===== */

/* 1. Element Plus 组件样式覆盖 */

/* 按钮组件 */
.el-button {
  border-radius: var(--radius-lg) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all var(--transition-normal) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-xs) !important;
}

.el-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-button--primary {
  background: var(--gradient-primary) !important;
  border: none !important;
  color: white !important;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600)) !important;
  box-shadow: var(--shadow-md) !important;
}

.el-button--success {
  background: var(--gradient-success) !important;
  border: none !important;
}

.el-button--warning {
  background: var(--gradient-warning) !important;
  border: none !important;
}

.el-button--danger {
  background: var(--gradient-error) !important;
  border: none !important;
}

/* 卡片组件 */
.el-card {
  border-radius: var(--radius-xl) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all var(--transition-normal) !important;
  overflow: hidden !important;
}

.el-card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

.el-card__header {
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  padding: var(--space-4) var(--space-6) !important;
}

.el-card__body {
  padding: var(--space-6) !important;
}

/* 表格组件 */
.el-table {
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  border: 1px solid var(--border-primary) !important;
}

.el-table__header-wrapper {
  background: var(--bg-secondary) !important;
}

.el-table th {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  border-bottom: 1px solid var(--border-primary) !important;
}

.el-table td {
  border-bottom: 1px solid var(--border-primary) !important;
}

.el-table__row:hover {
  background: var(--bg-secondary) !important;
}

/* 输入框组件 */
.el-input__wrapper {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--border-primary) !important;
  transition: all var(--transition-fast) !important;
  box-shadow: var(--shadow-xs) !important;
}

.el-input__wrapper:hover {
  border-color: var(--primary-300) !important;
}

.el-input__wrapper.is-focus {
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

/* 选择器组件 */
.el-select__wrapper {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--border-primary) !important;
  transition: all var(--transition-fast) !important;
}

.el-select__wrapper:hover {
  border-color: var(--primary-300) !important;
}

.el-select__wrapper.is-focus {
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

/* 下拉菜单 */
.el-dropdown-menu {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-lg) !important;
  padding: var(--space-2) !important;
}

.el-dropdown-menu__item {
  border-radius: var(--radius-md) !important;
  margin-bottom: var(--space-1) !important;
  transition: all var(--transition-fast) !important;
}

.el-dropdown-menu__item:hover {
  background: var(--bg-secondary) !important;
  color: var(--primary-500) !important;
}

/* 分页组件 */
.el-pagination {
  gap: var(--space-2) !important;
}

.el-pagination .btn-prev,
.el-pagination .btn-next,
.el-pagination .el-pager li {
  border-radius: var(--radius-md) !important;
  border: 1px solid var(--border-primary) !important;
  transition: all var(--transition-fast) !important;
}

.el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover,
.el-pagination .el-pager li:hover {
  background: var(--primary-500) !important;
  color: white !important;
  border-color: var(--primary-500) !important;
}

.el-pagination .el-pager li.is-active {
  background: var(--gradient-primary) !important;
  color: white !important;
  border: none !important;
}

/* 标签组件 */
.el-tag {
  border-radius: var(--radius-full) !important;
  border: none !important;
  font-weight: var(--font-weight-medium) !important;
  padding: var(--space-1) var(--space-3) !important;
}

.el-tag--success {
  background: var(--success-50) !important;
  color: var(--success-700) !important;
}

.el-tag--warning {
  background: var(--warning-50) !important;
  color: var(--warning-700) !important;
}

.el-tag--danger {
  background: var(--error-50) !important;
  color: var(--error-700) !important;
}

.el-tag--info {
  background: var(--info-50) !important;
  color: var(--info-700) !important;
}

/* 对话框组件 */
.el-dialog {
  border-radius: var(--radius-2xl) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-2xl) !important;
  overflow: hidden !important;
}

.el-dialog__header {
  background: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  padding: var(--space-6) !important;
}

.el-dialog__body {
  padding: var(--space-6) !important;
}

.el-dialog__footer {
  background: var(--bg-secondary) !important;
  border-top: 1px solid var(--border-primary) !important;
  padding: var(--space-4) var(--space-6) !important;
}

/* 消息提示 */
.el-message {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--border-primary) !important;
  box-shadow: var(--shadow-lg) !important;
  backdrop-filter: blur(8px) !important;
}

.el-message--success {
  background: rgba(34, 197, 94, 0.1) !important;
  border-color: var(--success-200) !important;
  color: var(--success-700) !important;
}

.el-message--warning {
  background: rgba(245, 158, 11, 0.1) !important;
  border-color: var(--warning-200) !important;
  color: var(--warning-700) !important;
}

.el-message--error {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: var(--error-200) !important;
  color: var(--error-700) !important;
}

.el-message--info {
  background: rgba(59, 130, 246, 0.1) !important;
  border-color: var(--info-200) !important;
  color: var(--info-700) !important;
}

/* 2. 自定义组件类 */

/* 现代化统计卡片 */
.modern-stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.modern-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.modern-stat-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-200);
}

/* 现代化数据表格 */
.modern-table-container {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.modern-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.modern-table-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.modern-table-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

/* 现代化搜索框 */
.modern-search-container {
  position: relative;
  max-width: 400px;
}

.modern-search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-12);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.modern-search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.modern-search-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

/* 现代化状态徽章 */
.modern-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.modern-badge::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.modern-badge--success {
  background: var(--success-50);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

.modern-badge--warning {
  background: var(--warning-50);
  color: var(--warning-700);
  border: 1px solid var(--warning-200);
}

.modern-badge--error {
  background: var(--error-50);
  color: var(--error-700);
  border: 1px solid var(--error-200);
}

.modern-badge--info {
  background: var(--info-50);
  color: var(--info-700);
  border: 1px solid var(--info-200);
}
