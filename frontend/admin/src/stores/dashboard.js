import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getDashboardData, getRoomStatistics, getIncomeReport } from '@/api/dashboard'

export const useDashboardStore = defineStore('dashboard', () => {
  // 仪表盘数据
  const dashboardData = ref({
    todayIncome: 0,
    totalRooms: 0,
    occupiedRooms: 0,
    availableRooms: 0,
    activeOrders: 0,
    onlineDevices: 0,
    offlineDevices: 0,
    roomStatusChart: [],
    incomeChart: [],
    orderTrendChart: []
  })

  // 房间统计数据
  const roomStats = ref({
    total: 0,
    available: 0,
    occupied: 0,
    maintenance: 0
  })

  // 收入统计数据
  const incomeStats = ref({
    today: 0,
    yesterday: 0,
    thisWeek: 0,
    thisMonth: 0,
    chartData: []
  })

  // 加载状态
  const loading = ref(false)

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      loading.value = true
      const response = await getDashboardData()
      if (response.code === 200) {
        dashboardData.value = response.data
      } else {
        console.error('获取仪表盘数据失败:', response.message)
        // 初始化为空数据
        dashboardData.value = {
          todayIncome: 0,
          totalRooms: 0,
          occupiedRooms: 0,
          availableRooms: 0,
          activeOrders: 0,
          onlineDevices: 0,
          offlineDevices: 0,
          roomStatusChart: [],
          incomeChart: [],
          orderTrendChart: []
        }
      }
    } catch (error) {
      console.error('获取仪表盘数据失败:', error)
      // 初始化为空数据
      dashboardData.value = {
        todayIncome: 0,
        totalRooms: 0,
        occupiedRooms: 0,
        availableRooms: 0,
        activeOrders: 0,
        onlineDevices: 0,
        offlineDevices: 0,
        roomStatusChart: [],
        incomeChart: [],
        orderTrendChart: []
      }
    } finally {
      loading.value = false
    }
  }

  // 获取房间统计
  const fetchRoomStatistics = async () => {
    try {
      const response = await getRoomStatistics()
      if (response.code === 200) {
        // 将后端返回的对象格式转换为组件期望的格式
        roomStats.value = response.data
      } else {
        console.error('获取房间统计失败:', response.message)
        // 初始化为空数据
        roomStats.value = {
          total: 0,
          available: 0,
          occupied: 0,
          maintenance: 0
        }
      }
    } catch (error) {
      console.error('获取房间统计失败:', error)
      // 初始化为空数据
      roomStats.value = {
        total: 0,
        available: 0,
        occupied: 0,
        maintenance: 0
      }
    }
  }

  // 获取收入报表
  const fetchIncomeReport = async (startDate, endDate) => {
    try {
      const response = await getIncomeReport(startDate, endDate)
      if (response.code === 200) {
        incomeStats.value = response.data
      } else {
        console.error('获取收入报表失败:', response.message)
        // 初始化为空数据
        incomeStats.value = {
          today: 0,
          yesterday: 0,
          thisWeek: 0,
          thisMonth: 0,
          chartData: []
        }
      }
    } catch (error) {
      console.error('获取收入报表失败:', error)
      // 初始化为空数据
      incomeStats.value = {
        today: 0,
        yesterday: 0,
        thisWeek: 0,
        thisMonth: 0,
        chartData: []
      }
    }
  }



  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchDashboardData(),
      fetchRoomStatistics()
    ])
  }

  return {
    dashboardData,
    roomStats,
    incomeStats,
    loading,
    fetchDashboardData,
    fetchRoomStatistics,
    fetchIncomeReport,
    refreshAll
  }
})
