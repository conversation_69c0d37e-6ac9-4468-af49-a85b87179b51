import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSystemStore = defineStore('system', () => {
  // 系统信息
  const systemInfo = ref({
    name: '自助麻将室管理系统',
    version: '1.0.0',
    author: 'Mahjong System Team'
  })

  // 用户信息
  const userInfo = ref({
    id: null,
    username: '',
    nickname: '',
    avatar: '',
    role: 'admin'
  })

  // 系统配置
  const systemConfig = ref({
    theme: 'light',
    language: 'zh-CN',
    sidebarCollapsed: false
  })

  // 加载状态
  const loading = ref(false)

  // 初始化系统
  const initSystem = async () => {
    try {
      loading.value = true
      
      // 从localStorage恢复配置
      const savedConfig = localStorage.getItem('system_config')
      if (savedConfig) {
        systemConfig.value = { ...systemConfig.value, ...JSON.parse(savedConfig) }
      }

      // 从localStorage恢复用户信息
      const savedUser = localStorage.getItem('user_info')
      if (savedUser) {
        userInfo.value = { ...userInfo.value, ...JSON.parse(savedUser) }
      }

    } catch (error) {
      console.error('系统初始化失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 设置用户信息
  const setUserInfo = (user) => {
    userInfo.value = { ...userInfo.value, ...user }
    localStorage.setItem('user_info', JSON.stringify(userInfo.value))
  }

  // 设置系统配置
  const setSystemConfig = (config) => {
    systemConfig.value = { ...systemConfig.value, ...config }
    localStorage.setItem('system_config', JSON.stringify(systemConfig.value))
  }

  // 切换侧边栏
  const toggleSidebar = () => {
    systemConfig.value.sidebarCollapsed = !systemConfig.value.sidebarCollapsed
    setSystemConfig(systemConfig.value)
  }

  // 切换主题
  const toggleTheme = () => {
    systemConfig.value.theme = systemConfig.value.theme === 'light' ? 'dark' : 'light'
    setSystemConfig(systemConfig.value)
    
    // 应用主题到document
    if (systemConfig.value.theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // 登出
  const logout = () => {
    userInfo.value = {
      id: null,
      username: '',
      nickname: '',
      avatar: '',
      role: 'admin'
    }
    localStorage.removeItem('user_info')
    localStorage.removeItem('admin_token')
  }

  return {
    systemInfo,
    userInfo,
    systemConfig,
    loading,
    initSystem,
    setUserInfo,
    setSystemConfig,
    toggleSidebar,
    toggleTheme,
    logout
  }
})
