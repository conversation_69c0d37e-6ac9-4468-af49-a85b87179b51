/* 导入设计系统 */
@import './styles/design-system.css';
@import './styles/components.css';

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 16px;
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  height: 100%;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

#app {
  height: 100%;
}

/* 现代化布局样式 */
.layout-container {
  height: 100vh;
  background: var(--bg-secondary);
}

.layout-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(8px);
  transition: all var(--transition-normal);
}

.layout-aside {
  background: linear-gradient(180deg, var(--gray-900) 0%, var(--gray-800) 100%);
  overflow: hidden;
  border-right: 1px solid var(--border-primary);
}

.layout-main {
  background: var(--bg-secondary);
  padding: var(--space-6);
  overflow-y: auto;
  min-height: calc(100vh - 60px);
}

/* 现代化卡片样式 */
.dashboard-card {
  margin-bottom: var(--space-6);
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.stat-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--primary-200);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon.income {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;
}

.stat-icon.rooms {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
}

.stat-icon.orders {
  background: linear-gradient(135deg, var(--info-500), var(--info-600));
  color: white;
}

.stat-icon.users {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  color: white;
}

.stat-info {
  flex: 1;
  text-align: left;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  line-height: var(--line-height-tight);
}

.stat-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* 现代化表格样式 */
.table-container {
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.table-container:hover {
  box-shadow: var(--shadow-md);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.table-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.table-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

/* 现代化状态标签样式 */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all var(--transition-fast);
}

.status-tag::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-available {
  background: var(--success-50);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

.status-occupied {
  background: var(--warning-50);
  color: var(--warning-700);
  border: 1px solid var(--warning-200);
}

.status-maintenance {
  background: var(--error-50);
  color: var(--error-700);
  border: 1px solid var(--error-200);
}

.status-online {
  background: var(--success-50);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

.status-offline {
  background: var(--gray-50);
  color: var(--gray-600);
  border: 1px solid var(--gray-200);
}

.status-pending {
  background: var(--info-50);
  color: var(--info-700);
  border: 1px solid var(--info-200);
}

.status-verified {
  background: var(--success-50);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

/* 现代化响应式设计 */
@media (max-width: 640px) {
  .layout-main {
    padding: var(--space-4);
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }

  .stat-icon {
    align-self: center;
  }

  .modern-search-container {
    max-width: 100%;
  }

  .header-right {
    gap: var(--space-1);
  }

  .user-info .username {
    display: none;
  }
}

@media (max-width: 768px) {
  .layout-main {
    padding: var(--space-5);
  }

  .dashboard-card {
    margin-bottom: var(--space-4);
  }

  .stat-card {
    padding: var(--space-4);
  }

  .table-container {
    padding: var(--space-4);
  }
}

@media (max-width: 1024px) {
  .header-left .system-title {
    font-size: var(--font-size-base);
  }

  .stat-number {
    font-size: var(--font-size-2xl);
  }
}

/* 现代化动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: all var(--transition-normal);
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.slide-enter-active, .slide-leave-active {
  transition: all var(--transition-normal);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* 现代化自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-400), var(--primary-500));
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

::-webkit-scrollbar-corner {
  background: var(--bg-secondary);
}

/* 页面加载动画 */
.page-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 悬浮效果 */
.hover-lift {
  transition: all var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
