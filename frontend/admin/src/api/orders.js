import request from './request'

// 获取订单列表
export const getOrderList = (params) => {
  return request({
    url: '/orders',
    method: 'get',
    params
  })
}

// 获取订单详情
export const getOrderDetail = (id) => {
  return request({
    url: `/orders/${id}`,
    method: 'get'
  })
}

// 取消订单
export const cancelOrder = (id) => {
  return request({
    url: `/orders/${id}/cancel`,
    method: 'post'
  })
}

// 完成订单
export const completeOrder = (id) => {
  return request({
    url: `/orders/${id}/complete`,
    method: 'post'
  })
}

// 延长订单
export const extendOrder = (id, data) => {
  return request({
    url: `/orders/${id}/extend`,
    method: 'post',
    data
  })
}

// 获取房间活跃订单
export const getActiveOrderByRoom = (roomId) => {
  return request({
    url: `/orders/room/${roomId}/active`,
    method: 'get'
  })
}

// 计算订单金额
export const calculateOrderAmount = (params) => {
  return request({
    url: '/orders/calculate-amount',
    method: 'get',
    params
  })
}
