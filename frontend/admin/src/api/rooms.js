import request from './request'

// 获取房间列表
export const getRoomList = (params) => {
  return request({
    url: '/admin/rooms',
    method: 'get',
    params
  })
}

// 获取房间详情
export const getRoomDetail = (id) => {
  return request({
    url: `/rooms/${id}`,
    method: 'get'
  })
}

// 创建房间
export const createRoom = (data) => {
  return request({
    url: '/admin/rooms',
    method: 'post',
    data
  })
}

// 更新房间
export const updateRoom = (id, data) => {
  return request({
    url: `/admin/rooms/${id}`,
    method: 'put',
    data
  })
}

// 删除房间
export const deleteRoom = (id) => {
  return request({
    url: `/admin/rooms/${id}`,
    method: 'delete'
  })
}

// 更新房间状态
export const updateRoomStatus = (id, status) => {
  return request({
    url: `/admin/rooms/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取房间设备信息
export const getRoomDevices = (id) => {
  return request({
    url: `/rooms/${id}/devices`,
    method: 'get'
  })
}

// 获取可用房间
export const getAvailableRooms = () => {
  return request({
    url: '/rooms/available',
    method: 'get'
  })
}
