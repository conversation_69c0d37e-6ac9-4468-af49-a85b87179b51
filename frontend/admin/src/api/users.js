import request from './request'

// 获取用户列表
export const getUserList = (params) => {
  return request({
    url: '/admin/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export const getUserDetail = (id) => {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

// 更新用户信息
export const updateUser = (id, data) => {
  return request({
    url: `/users/${id}`,
    method: 'put',
    data
  })
}

// 获取用户统计
export const getUserStats = () => {
  return request({
    url: '/admin/users/stats',
    method: 'get'
  })
}

// 获取用户订单
export const getUserOrders = (userId, params) => {
  return request({
    url: `/users/${userId}/orders`,
    method: 'get',
    params
  })
}

// 用户充值
export const rechargeUser = (userId, data) => {
  return request({
    url: `/users/${userId}/recharge`,
    method: 'post',
    data
  })
}
