import request from './request'

// 获取计费规则列表
export const getPricingRuleList = (params) => {
  return request({
    url: '/admin/pricing-rules',
    method: 'get',
    params
  })
}

// 获取计费规则详情
export const getPricingRuleDetail = (id) => {
  return request({
    url: `/admin/pricing-rules/${id}`,
    method: 'get'
  })
}

// 创建计费规则
export const createPricingRule = (data) => {
  return request({
    url: '/admin/pricing-rules',
    method: 'post',
    data
  })
}

// 更新计费规则
export const updatePricingRule = (id, data) => {
  return request({
    url: `/admin/pricing-rules/${id}`,
    method: 'put',
    data
  })
}

// 删除计费规则
export const deletePricingRule = (id) => {
  return request({
    url: `/admin/pricing-rules/${id}`,
    method: 'delete'
  })
}
