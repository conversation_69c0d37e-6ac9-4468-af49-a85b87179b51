import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import router from '@/router'
import { ErrorNotifier, ErrorClassifier, ErrorTypes } from '@/utils/errorHandler'

// 创建axios实例
const request = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加token
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加用户ID（临时方案）
    const userInfo = localStorage.getItem('user_info')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      if (user.id) {
        config.headers['X-User-ID'] = user.id
      }
    }

    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }

    // 检查业务状态码
    if (data.code !== undefined && data.code !== 200) {
      // 创建业务错误对象
      const businessError = new Error(data.message || '请求失败')
      businessError.response = { data, status: response.status }
      businessError.isBusinessError = true

      // 使用增强的错误处理
      const { type } = ErrorClassifier.classify(businessError)
      if (type === ErrorTypes.BUSINESS_ERROR) {
        ElMessage.error(data.message || '请求失败')
      } else {
        ErrorNotifier.notify(businessError, '业务处理')
      }

      return Promise.reject(businessError)
    }

    return data
  },
  (error) => {
    console.error('响应错误:', error)

    // 使用增强的错误处理机制
    const { type, level } = ErrorClassifier.classify(error)

    // 特殊处理认证错误
    if (type === ErrorTypes.AUTH_ERROR) {
      ElMessageBox.confirm(
        '登录状态已过期，请重新登录',
        '系统提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        localStorage.removeItem('admin_token')
        localStorage.removeItem('user_info')
        router.push('/login')
      }).catch(() => {
        // 用户取消登录
      })
    } else {
      // 使用统一的错误通知机制
      ErrorNotifier.notify(error, '请求处理')
    }

    return Promise.reject(error)
  }
)

export default request
