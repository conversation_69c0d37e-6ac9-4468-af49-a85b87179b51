import request from './request'

// 获取仪表盘数据
export const getDashboardData = () => {
  return request({
    url: '/admin/dashboard',
    method: 'get'
  })
}

// 获取房间统计
export const getRoomStatistics = () => {
  return request({
    url: '/admin/rooms/statistics',
    method: 'get'
  })
}

// 获取今日收入
export const getTodayIncome = () => {
  return request({
    url: '/admin/orders/today-income',
    method: 'get'
  })
}

// 获取收入报表
export const getIncomeReport = (startDate, endDate) => {
  return request({
    url: '/admin/orders/income-report',
    method: 'get',
    params: {
      start_date: startDate,
      end_date: endDate
    }
  })
}
