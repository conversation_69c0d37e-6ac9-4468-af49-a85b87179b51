import request from '@/api/request'

// 获取套餐列表（管理端）
export function getPackageList(params) {
  return request({
    url: '/admin/packages',
    method: 'get',
    params
  })
}

// 获取套餐统计
export function getPackageStats() {
  return request({
    url: '/admin/packages/stats',
    method: 'get'
  })
}

// 获取套餐详情
export function getPackageDetail(id) {
  return request({
    url: `/admin/packages/${id}`,
    method: 'get'
  })
}

// 创建套餐
export function createPackage(data) {
  return request({
    url: '/admin/packages',
    method: 'post',
    data
  })
}

// 更新套餐
export function updatePackage(id, data) {
  return request({
    url: `/admin/packages/${id}`,
    method: 'put',
    data
  })
}

// 删除套餐
export function deletePackage(id) {
  return request({
    url: `/admin/packages/${id}`,
    method: 'delete'
  })
}

// 更新套餐状态
export function updatePackageStatus(id, isActive) {
  return request({
    url: `/admin/packages/${id}/status`,
    method: 'put',
    data: {
      is_active: isActive
    }
  })
}

// 获取有效套餐列表（用户端）
export function getActivePackages(params) {
  return request({
    url: '/packages',
    method: 'get',
    params
  })
}

// 购买套餐
export function purchasePackage(id, data) {
  return request({
    url: `/packages/${id}/purchase`,
    method: 'post',
    data
  })
}

// 续费套餐
export function rechargePackage(data) {
  return request({
    url: '/packages/recharge',
    method: 'post',
    data
  })
}

// 获取用户套餐列表
export function getUserPackages(params) {
  return request({
    url: '/user/packages',
    method: 'get',
    params
  })
}

// 获取用户套餐统计
export function getUserPackageStats() {
  return request({
    url: '/user/packages/stats',
    method: 'get'
  })
}

// 获取用户套餐详情
export function getUserPackageDetail(id) {
  return request({
    url: `/user/packages/${id}`,
    method: 'get'
  })
}

// 获取套餐使用记录
export function getPackageUsageLogs(id, params) {
  return request({
    url: `/user/packages/${id}/logs`,
    method: 'get',
    params
  })
}

// 使用套餐开房
export function usePackage(id, data) {
  return request({
    url: `/user/packages/${id}/use`,
    method: 'post',
    data
  })
}
