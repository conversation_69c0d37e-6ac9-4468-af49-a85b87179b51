import request from './request'

// 获取设备列表
export const getDeviceList = (params) => {
  return request({
    url: '/devices',
    method: 'get',
    params
  })
}

// 获取房间设备状态
export const getRoomDeviceStatus = (roomId) => {
  return request({
    url: `/mqtt/room/${roomId}/devices`,
    method: 'get'
  })
}

// 控制房间门锁
export const controlRoomLock = (roomId, action) => {
  return request({
    url: `/mqtt/room/${roomId}/lock`,
    method: 'post',
    data: { action }
  })
}

// 控制房间电源
export const controlRoomPower = (roomId, action) => {
  return request({
    url: `/mqtt/room/${roomId}/power`,
    method: 'post',
    data: { action }
  })
}

// 发送自定义消息
export const sendCustomMessage = (roomId, data) => {
  return request({
    url: `/mqtt/room/${roomId}/message`,
    method: 'post',
    data
  })
}

// 获取设备连接状态
export const getConnectionStatus = () => {
  return request({
    url: '/mqtt/connection-status',
    method: 'get'
  })
}



// 获取房间设备信息
export const getDevicesByRoom = (roomId) => {
  return request({
    url: `/admin/rooms/${roomId}/devices`,
    method: 'get'
  })
}

// 控制设备
export const controlDevice = (roomId, deviceType, action) => {
  return request({
    url: `/admin/rooms/${roomId}/devices/${deviceType}/control`,
    method: 'post',
    data: { action }
  })
}
