import request from './request'

// 获取系统配置
export const getSystemConfig = () => {
  return request({
    url: '/system/config',
    method: 'get'
  })
}

// 更新系统配置
export const updateSystemConfig = (data) => {
  return request({
    url: '/system/config',
    method: 'put',
    data
  })
}

// 获取系统状态
export const getSystemStatus = () => {
  return request({
    url: '/system/status',
    method: 'get'
  })
}

// 获取系统日志
export const getSystemLogs = (params) => {
  return request({
    url: '/system/logs',
    method: 'get',
    params
  })
}

// 清理系统缓存
export const clearSystemCache = () => {
  return request({
    url: '/system/cache/clear',
    method: 'post'
  })
}

// 备份数据
export const backupData = () => {
  return request({
    url: '/system/backup',
    method: 'post'
  })
}

// 恢复数据
export const restoreData = (data) => {
  return request({
    url: '/system/restore',
    method: 'post',
    data
  })
}
