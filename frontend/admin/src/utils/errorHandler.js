// 增强的错误处理工具
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

// 错误类型枚举
export const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 错误级别
export const ErrorLevels = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

// 错误处理配置
const errorConfig = {
  // 重试配置
  retry: {
    maxAttempts: 3,
    delay: 1000, // 基础延迟时间（毫秒）
    backoff: 2,  // 退避倍数
    retryableErrors: [
      ErrorTypes.NETWORK_ERROR,
      ErrorTypes.TIMEOUT_ERROR,
      ErrorTypes.SERVER_ERROR
    ]
  },
  // 通知配置
  notification: {
    duration: 4000,
    showClose: true
  },
  // 消息配置
  message: {
    duration: 3000,
    showClose: true
  }
}

// 错误分类器
export class ErrorClassifier {
  static classify(error) {
    if (!error) return { type: ErrorTypes.UNKNOWN_ERROR, level: ErrorLevels.LOW }

    // 网络错误
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return { type: ErrorTypes.TIMEOUT_ERROR, level: ErrorLevels.MEDIUM }
    }

    if (error.code === 'NETWORK_ERROR' || !error.response) {
      return { type: ErrorTypes.NETWORK_ERROR, level: ErrorLevels.HIGH }
    }

    // HTTP状态码错误
    if (error.response) {
      const status = error.response.status
      
      switch (status) {
        case 401:
          return { type: ErrorTypes.AUTH_ERROR, level: ErrorLevels.CRITICAL }
        case 403:
          return { type: ErrorTypes.PERMISSION_ERROR, level: ErrorLevels.HIGH }
        case 400:
        case 422:
          return { type: ErrorTypes.VALIDATION_ERROR, level: ErrorLevels.MEDIUM }
        case 500:
        case 502:
        case 503:
        case 504:
          return { type: ErrorTypes.SERVER_ERROR, level: ErrorLevels.HIGH }
        default:
          return { type: ErrorTypes.BUSINESS_ERROR, level: ErrorLevels.MEDIUM }
      }
    }

    return { type: ErrorTypes.UNKNOWN_ERROR, level: ErrorLevels.LOW }
  }
}

// 错误消息生成器
export class ErrorMessageGenerator {
  static generate(error, context = '') {
    const { type } = ErrorClassifier.classify(error)
    const contextPrefix = context ? `${context}: ` : ''

    const messages = {
      [ErrorTypes.NETWORK_ERROR]: `${contextPrefix}网络连接失败，请检查网络后重试`,
      [ErrorTypes.TIMEOUT_ERROR]: `${contextPrefix}请求超时，请稍后重试`,
      [ErrorTypes.SERVER_ERROR]: `${contextPrefix}服务器暂时不可用，请稍后重试`,
      [ErrorTypes.BUSINESS_ERROR]: `${contextPrefix}${error.response?.data?.message || '操作失败'}`,
      [ErrorTypes.AUTH_ERROR]: `${contextPrefix}登录已过期，请重新登录`,
      [ErrorTypes.PERMISSION_ERROR]: `${contextPrefix}没有权限执行此操作`,
      [ErrorTypes.VALIDATION_ERROR]: `${contextPrefix}${error.response?.data?.message || '输入数据有误'}`,
      [ErrorTypes.UNKNOWN_ERROR]: `${contextPrefix}未知错误，请稍后重试`
    }

    return messages[type] || messages[ErrorTypes.UNKNOWN_ERROR]
  }
}

// 重试机制
export class RetryManager {
  static async executeWithRetry(fn, options = {}) {
    const config = { ...errorConfig.retry, ...options }
    let lastError = null

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        const { type } = ErrorClassifier.classify(error)

        // 检查是否可重试
        if (!config.retryableErrors.includes(type) || attempt === config.maxAttempts) {
          throw error
        }

        // 计算延迟时间
        const delay = config.delay * Math.pow(config.backoff, attempt - 1)
        
        console.warn(`请求失败，${delay}ms后进行第${attempt + 1}次重试:`, error.message)
        
        // 显示重试提示
        if (attempt === 1) {
          ElMessage.warning(`网络不稳定，正在重试...`)
        }

        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw lastError
  }
}

// 错误通知器
export class ErrorNotifier {
  static notify(error, context = '', options = {}) {
    const { type, level } = ErrorClassifier.classify(error)
    const message = ErrorMessageGenerator.generate(error, context)

    // 根据错误级别选择通知方式
    switch (level) {
      case ErrorLevels.CRITICAL:
        this.showCriticalError(message, error)
        break
      case ErrorLevels.HIGH:
        this.showHighError(message, options)
        break
      case ErrorLevels.MEDIUM:
        this.showMediumError(message, options)
        break
      case ErrorLevels.LOW:
      default:
        this.showLowError(message, options)
        break
    }

    // 记录错误日志
    this.logError(error, context, type, level)
  }

  static showCriticalError(message, error) {
    ElMessageBox.alert(message, '系统错误', {
      type: 'error',
      confirmButtonText: '确定',
      callback: () => {
        // 关键错误可能需要刷新页面或跳转
        if (error.response?.status === 401) {
          window.location.href = '/login'
        }
      }
    })
  }

  static showHighError(message, options) {
    ElNotification({
      title: '错误',
      message,
      type: 'error',
      duration: options.duration || errorConfig.notification.duration,
      showClose: errorConfig.notification.showClose
    })
  }

  static showMediumError(message, options) {
    ElMessage({
      message,
      type: 'error',
      duration: options.duration || errorConfig.message.duration,
      showClose: errorConfig.message.showClose
    })
  }

  static showLowError(message, options) {
    ElMessage({
      message,
      type: 'warning',
      duration: options.duration || errorConfig.message.duration,
      showClose: errorConfig.message.showClose
    })
  }

  static logError(error, context, type, level) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      context,
      type,
      level,
      message: error.message,
      stack: error.stack,
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    // 发送到控制台
    console.error('错误详情:', errorInfo)

    // 可以发送到错误监控服务
    // this.sendToErrorService(errorInfo)
  }

  // 发送错误到监控服务（可选）
  static async sendToErrorService(errorInfo) {
    try {
      // 这里可以集成第三方错误监控服务
      // 如 Sentry, LogRocket 等
      console.log('发送错误到监控服务:', errorInfo)
    } catch (e) {
      console.warn('发送错误日志失败:', e)
    }
  }
}

// 全局错误处理器
export class GlobalErrorHandler {
  static install() {
    // 捕获未处理的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise错误:', event.reason)
      ErrorNotifier.notify(event.reason, '系统异常')
      event.preventDefault()
    })

    // 捕获JavaScript运行时错误
    window.addEventListener('error', (event) => {
      console.error('JavaScript错误:', event.error)
      ErrorNotifier.notify(event.error, '脚本错误')
    })

    // 捕获资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        console.error('资源加载错误:', event.target.src || event.target.href)
        ElMessage.warning('资源加载失败，请刷新页面重试')
      }
    }, true)
  }
}

// 便捷的错误处理函数
export const handleError = (error, context = '', options = {}) => {
  ErrorNotifier.notify(error, context, options)
}

export const handleErrorWithRetry = async (fn, context = '', options = {}) => {
  try {
    return await RetryManager.executeWithRetry(fn, options)
  } catch (error) {
    ErrorNotifier.notify(error, context)
    throw error
  }
}

// 创建带重试的API调用包装器
export const createRetryableApi = (apiFunction, context = '') => {
  return async (...args) => {
    return handleErrorWithRetry(
      () => apiFunction(...args),
      context,
      {
        maxAttempts: 3, // 增加重试次数
        delay: 500,     // 减少延迟时间
        retryableErrors: [
          ErrorTypes.NETWORK_ERROR,
          ErrorTypes.TIMEOUT_ERROR
          // 移除 SERVER_ERROR，避免对正常的服务器响应进行重试
        ]
      }
    )
  }
}

// 导出默认配置
export default {
  ErrorTypes,
  ErrorLevels,
  ErrorClassifier,
  ErrorMessageGenerator,
  RetryManager,
  ErrorNotifier,
  GlobalErrorHandler,
  handleError,
  handleErrorWithRetry,
  createRetryableApi
}
