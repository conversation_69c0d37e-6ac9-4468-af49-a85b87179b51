<template>
  <div v-if="showStatus" class="network-status" :class="statusClass">
    <div class="status-content">
      <el-icon class="status-icon">
        <component :is="statusIcon" />
      </el-icon>
      <span class="status-text">{{ statusText }}</span>
      <el-button 
        v-if="showRetryButton" 
        type="text" 
        size="small" 
        @click="handleRetry"
        class="retry-button"
      >
        重试
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { WifiOff, Wifi, Warning, Loading } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 是否自动检测网络状态
  autoDetect: {
    type: Boolean,
    default: true
  },
  // 检测间隔（毫秒）
  checkInterval: {
    type: Number,
    default: 30000
  },
  // 是否显示重试按钮
  showRetry: {
    type: <PERSON>olean,
    default: true
  }
})

// Emits
const emit = defineEmits(['retry', 'statusChange'])

// 响应式数据
const isOnline = ref(navigator.onLine)
const isChecking = ref(false)
const lastCheckTime = ref(null)
const checkTimer = ref(null)

// 计算属性
const showStatus = computed(() => {
  return !isOnline.value || isChecking.value
})

const statusClass = computed(() => {
  if (isChecking.value) return 'checking'
  if (!isOnline.value) return 'offline'
  return 'online'
})

const statusIcon = computed(() => {
  if (isChecking.value) return Loading
  if (!isOnline.value) return WifiOff
  return Wifi
})

const statusText = computed(() => {
  if (isChecking.value) return '正在检查网络连接...'
  if (!isOnline.value) return '网络连接已断开'
  return '网络连接正常'
})

const showRetryButton = computed(() => {
  return props.showRetry && !isOnline.value && !isChecking.value
})

// 方法
const checkNetworkStatus = async () => {
  if (isChecking.value) return

  isChecking.value = true
  
  try {
    // 尝试发送一个轻量级的请求来检测网络
    const response = await fetch('/api/v1/health', {
      method: 'HEAD',
      cache: 'no-cache',
      timeout: 5000
    })
    
    const newStatus = response.ok
    if (newStatus !== isOnline.value) {
      isOnline.value = newStatus
      emit('statusChange', newStatus)
      
      if (newStatus) {
        ElMessage.success('网络连接已恢复')
      } else {
        ElMessage.warning('网络连接不稳定')
      }
    }
  } catch (error) {
    const newStatus = false
    if (newStatus !== isOnline.value) {
      isOnline.value = newStatus
      emit('statusChange', newStatus)
      ElMessage.error('网络连接已断开')
    }
  } finally {
    isChecking.value = false
    lastCheckTime.value = new Date()
  }
}

const handleRetry = () => {
  emit('retry')
  checkNetworkStatus()
}

const startAutoCheck = () => {
  if (!props.autoDetect) return
  
  checkTimer.value = setInterval(() => {
    checkNetworkStatus()
  }, props.checkInterval)
}

const stopAutoCheck = () => {
  if (checkTimer.value) {
    clearInterval(checkTimer.value)
    checkTimer.value = null
  }
}

// 监听浏览器网络状态变化
const handleOnline = () => {
  isOnline.value = true
  emit('statusChange', true)
  ElMessage.success('网络连接已恢复')
}

const handleOffline = () => {
  isOnline.value = false
  emit('statusChange', false)
  ElMessage.warning('网络连接已断开')
}

// 生命周期
onMounted(() => {
  // 监听浏览器网络事件
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 启动自动检测
  startAutoCheck()
  
  // 初始检测
  if (props.autoDetect) {
    setTimeout(checkNetworkStatus, 1000)
  }
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  
  // 停止自动检测
  stopAutoCheck()
})

// 暴露方法给父组件
defineExpose({
  checkNetworkStatus,
  isOnline: computed(() => isOnline.value),
  lastCheckTime: computed(() => lastCheckTime.value)
})
</script>

<style scoped>
.network-status {
  position: fixed;
  top: 60px;
  right: 20px;
  z-index: 2000;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  max-width: 300px;
}

.network-status.online {
  background: rgba(103, 194, 58, 0.9);
  color: white;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.network-status.offline {
  background: rgba(245, 108, 108, 0.9);
  color: white;
  border: 1px solid rgba(245, 108, 108, 0.3);
  animation: pulse 2s infinite;
}

.network-status.checking {
  background: rgba(230, 162, 60, 0.9);
  color: white;
  border: 1px solid rgba(230, 162, 60, 0.3);
}

.status-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.retry-button {
  color: white !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 4px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transition: all 0.2s ease !important;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .network-status {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .status-text {
    font-size: 13px;
  }
}

/* 暗色主题适配 */
.dark .network-status {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .network-status.online {
  background: rgba(103, 194, 58, 0.8);
}

.dark .network-status.offline {
  background: rgba(245, 108, 108, 0.8);
}

.dark .network-status.checking {
  background: rgba(230, 162, 60, 0.8);
}
</style>
