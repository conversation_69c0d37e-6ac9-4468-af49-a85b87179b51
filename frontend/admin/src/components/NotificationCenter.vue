<template>
  <el-popover
    placement="bottom-end"
    :width="320"
    trigger="click"
  >
    <template #reference>
      <el-badge :value="unreadCount" :hidden="unreadCount === 0">
        <el-button type="text" size="large">
          <el-icon size="18"><Bell /></el-icon>
        </el-button>
      </el-badge>
    </template>
    
    <div class="notification-panel">
      <div class="panel-header">
        <span>通知中心</span>
        <el-button type="text" size="small" @click="markAllRead">
          全部已读
        </el-button>
      </div>
      
      <div class="notification-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ unread: !notification.read }"
        >
          <div class="notification-icon">
            <el-icon :color="getIconColor(notification.type)">
              <component :is="getIcon(notification.type)" />
            </el-icon>
          </div>
          <div class="notification-content">
            <div class="title">{{ notification.title }}</div>
            <div class="message">{{ notification.message }}</div>
            <div class="time">{{ formatTime(notification.time) }}</div>
          </div>
        </div>
      </div>
      
      <div class="panel-footer">
        <el-button type="text" @click="viewAll">查看全部</el-button>
      </div>
    </div>
  </el-popover>
</template>

<script setup>
import { ref, computed } from 'vue'

const notifications = ref([
  {
    id: 1,
    type: 'order',
    title: '新订单提醒',
    message: '房间A01有新的预订订单',
    time: new Date(),
    read: false
  },
  {
    id: 2,
    type: 'device',
    title: '设备异常',
    message: '房间B02的门锁设备离线',
    time: new Date(Date.now() - 30 * 60 * 1000),
    read: false
  },
  {
    id: 3,
    type: 'system',
    title: '系统更新',
    message: '系统已更新到最新版本',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: true
  }
])

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

const getIcon = (type) => {
  const icons = {
    order: 'Document',
    device: 'Monitor',
    system: 'Setting',
    user: 'User'
  }
  return icons[type] || 'Bell'
}

const getIconColor = (type) => {
  const colors = {
    order: '#1890ff',
    device: '#ff4d4f',
    system: '#52c41a',
    user: '#faad14'
  }
  return colors[type] || '#666'
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return time.toLocaleDateString()
}

const markAllRead = () => {
  notifications.value.forEach(n => n.read = true)
}

const viewAll = () => {
  // 跳转到通知列表页面
  console.log('查看全部通知')
}
</script>

<style scoped>
.notification-panel {
  max-height: 400px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
}

.notification-item:hover {
  background: #fafafa;
}

.notification-item.unread {
  background: #f6ffed;
}

.notification-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
}

.notification-content {
  flex: 1;
}

.title {
  font-weight: 500;
  margin-bottom: 4px;
}

.message {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.time {
  font-size: 11px;
  color: #999;
}

.panel-footer {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}
</style>
