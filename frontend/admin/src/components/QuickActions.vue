<template>
  <div class="quick-actions">
    <el-card class="action-card">
      <template #header>
        <div class="card-header">
          <span>快捷操作</span>
          <el-button type="text" @click="refreshData">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div class="action-grid">
        <div class="action-item" @click="$router.push('/rooms/create')">
          <el-icon size="24" color="#1890ff"><Plus /></el-icon>
          <span>添加房间</span>
        </div>
        
        <div class="action-item" @click="$router.push('/orders/list')">
          <el-icon size="24" color="#52c41a"><Document /></el-icon>
          <span>查看订单</span>
        </div>
        
        <div class="action-item" @click="$router.push('/devices/control')">
          <el-icon size="24" color="#faad14"><Setting /></el-icon>
          <span>设备控制</span>
        </div>
        
        <div class="action-item" @click="$router.push('/finance/report')">
          <el-icon size="24" color="#722ed1"><DataAnalysis /></el-icon>
          <span>财务报表</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const refreshData = () => {
  // 刷新当前页面数据
  window.location.reload()
}
</script>

<style scoped>
.quick-actions {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.action-item span {
  font-size: 14px;
  color: #666;
}
</style>
