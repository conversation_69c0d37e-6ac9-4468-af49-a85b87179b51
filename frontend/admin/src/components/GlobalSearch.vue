<template>
  <el-select
    v-model="searchValue"
    filterable
    remote
    reserve-keyword
    placeholder="搜索房间、订单、用户..."
    :remote-method="remoteMethod"
    :loading="loading"
    @change="handleSelect"
    style="width: 300px"
  >
    <el-option-group
      v-for="group in searchResults"
      :key="group.label"
      :label="group.label"
    >
      <el-option
        v-for="item in group.options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
        <div class="search-item">
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.label }}</span>
          <span class="search-type">{{ item.type }}</span>
        </div>
      </el-option>
    </el-option-group>
  </el-select>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const searchValue = ref('')
const loading = ref(false)
const searchResults = ref([])

// 模拟搜索数据
const mockData = [
  // 房间数据
  { id: 1, name: '牡丹厅', type: '房间', path: '/rooms/edit/1', icon: 'House' },
  { id: 2, name: '梅花厅', type: '房间', path: '/rooms/edit/2', icon: 'House' },
  { id: 3, name: '兰花厅', type: '房间', path: '/rooms/edit/3', icon: 'House' },
  
  // 订单数据
  { id: 'ORD001', name: 'ORD000001 - 牡丹厅', type: '订单', path: '/orders/detail/1', icon: 'Document' },
  { id: 'ORD002', name: 'ORD000002 - 梅花厅', type: '订单', path: '/orders/detail/2', icon: 'Document' },
  
  // 用户数据
  { id: 'U001', name: '张三 - 138****1234', type: '用户', path: '/users/list?search=张三', icon: 'User' },
  { id: 'U002', name: '李四 - 138****5678', type: '用户', path: '/users/list?search=李四', icon: 'User' },
]

const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    
    // 模拟API搜索延迟
    setTimeout(() => {
      const filtered = mockData.filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase())
      )
      
      // 按类型分组
      const grouped = {}
      filtered.forEach(item => {
        if (!grouped[item.type]) {
          grouped[item.type] = []
        }
        grouped[item.type].push({
          value: item.path,
          label: item.name,
          type: item.type,
          icon: item.icon
        })
      })
      
      searchResults.value = Object.keys(grouped).map(key => ({
        label: key,
        options: grouped[key]
      }))
      
      loading.value = false
    }, 200)
  } else {
    searchResults.value = []
  }
}

const handleSelect = (value) => {
  if (value) {
    router.push(value)
    searchValue.value = ''
    searchResults.value = []
  }
}
</script>

<style scoped>
.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-type {
  margin-left: auto;
  font-size: 12px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
}
</style>
