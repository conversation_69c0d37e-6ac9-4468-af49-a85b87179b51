<template>
  <div class="expense-trend-chart">
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-else-if="!data || data.length === 0" class="chart-empty">
      <el-icon><DocumentRemove /></el-icon>
      <span>暂无数据</span>
    </div>
    <div v-else ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { Loading, DocumentRemove } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartRef.value || !props.data || props.data.length === 0) return

  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>支出: ¥${param.value.toLocaleString()}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.map(item => item.date),
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}',
        fontSize: 10
      }
    },
    series: [
      {
        name: '支出',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 2,
          color: '#ef4444'
        },
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(239, 68, 68, 0.6)'
            },
            {
              offset: 1,
              color: 'rgba(239, 68, 68, 0.1)'
            }
          ])
        },
        itemStyle: {
          color: '#ef4444'
        },
        data: props.data.map(item => item.amount)
      }
    ]
  }

  chartInstance.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })
}

const updateChart = () => {
  if (chartInstance && props.data && props.data.length > 0) {
    const option = {
      xAxis: {
        data: props.data.map(item => item.date)
      },
      series: [
        {
          data: props.data.map(item => item.amount)
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    if (props.data && props.data.length > 0) {
      if (chartInstance) {
        updateChart()
      } else {
        initChart()
      }
    }
  })
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.expense-trend-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart-loading, .chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 12px;
}

.chart-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.chart-empty .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}
</style>
