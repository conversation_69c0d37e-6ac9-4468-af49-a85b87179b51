<template>
  <div class="chart-container">
    <v-chart 
      class="chart" 
      :option="chartOption" 
      :loading="loading"
      autoresize
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  Canvas<PERSON>enderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      total: 0,
      available: 0,
      occupied: 0,
      maintenance: 0
    })
  }
})

const loading = ref(false)

// 图表配置
const chartOption = computed(() => {
  const { available, occupied, maintenance } = props.data
  
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['可用', '占用', '维护']
    },
    series: [
      {
        name: '房间状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: available, 
            name: '可用',
            itemStyle: { color: '#52c41a' }
          },
          { 
            value: occupied, 
            name: '占用',
            itemStyle: { color: '#fa8c16' }
          },
          { 
            value: maintenance, 
            name: '维护',
            itemStyle: { color: '#ff4d4f' }
          }
        ]
      }
    ]
  }
})
</script>

<style scoped>
.chart-container {
  height: 300px;
  width: 100%;
}

.chart {
  height: 100%;
  width: 100%;
}
</style>
