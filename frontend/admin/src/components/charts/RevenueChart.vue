<template>
  <div class="revenue-chart">
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-else-if="!data || data.length === 0" class="chart-empty">
      <el-icon><DocumentRemove /></el-icon>
      <span>暂无数据</span>
    </div>
    <div v-else ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { Loading, DocumentRemove } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'daily'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartRef.value || !props.data || props.data.length === 0) return

  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        const param = params[0]
        return `${param.name}<br/>收入: ¥${param.value.toLocaleString()}`
      }
    },
    legend: {
      data: ['收入']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: '收入',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(102, 126, 234, 0.8)'
            },
            {
              offset: 1,
              color: 'rgba(102, 126, 234, 0.1)'
            }
          ])
        },
        itemStyle: {
          color: '#667eea'
        },
        data: props.data.map(item => item.revenue)
      }
    ]
  }

  chartInstance.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })
}

const updateChart = () => {
  if (chartInstance && props.data && props.data.length > 0) {
    const option = {
      xAxis: {
        data: props.data.map(item => item.date)
      },
      series: [
        {
          data: props.data.map(item => item.revenue)
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    if (props.data && props.data.length > 0) {
      if (chartInstance) {
        updateChart()
      } else {
        initChart()
      }
    }
  })
}, { deep: true })

watch(() => props.type, () => {
  nextTick(() => {
    initChart()
  })
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.revenue-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart-loading, .chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.chart-loading .el-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.chart-empty .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}
</style>
