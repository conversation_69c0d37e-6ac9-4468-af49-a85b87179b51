<template>
  <div class="profit-analysis-chart">
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-else-if="!data || data.length === 0" class="chart-empty">
      <el-icon><DocumentRemove /></el-icon>
      <span>暂无数据</span>
    </div>
    <div v-else ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { Loading, DocumentRemove } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  type: {
    type: String,
    default: 'monthly'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartRef.value || !props.data || props.data.length === 0) return

  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params) {
        let result = `${params[0].name}<br/>`
        params.forEach(param => {
          const value = param.seriesName === '利润率' ? 
            `${param.value}%` : 
            `¥${param.value.toLocaleString()}`
          result += `${param.marker}${param.seriesName}: ${value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['收入', '支出', '利润', '利润率']
    },
    xAxis: [
      {
        type: 'category',
        data: props.data.map(item => item.period),
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额',
        min: 0,
        axisLabel: {
          formatter: '¥{value}'
        }
      },
      {
        type: 'value',
        name: '利润率',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '收入',
        type: 'bar',
        data: props.data.map(item => item.revenue),
        itemStyle: {
          color: '#10b981'
        }
      },
      {
        name: '支出',
        type: 'bar',
        data: props.data.map(item => item.expense),
        itemStyle: {
          color: '#ef4444'
        }
      },
      {
        name: '利润',
        type: 'bar',
        data: props.data.map(item => item.profit),
        itemStyle: {
          color: '#3b82f6'
        }
      },
      {
        name: '利润率',
        type: 'line',
        yAxisIndex: 1,
        data: props.data.map(item => item.profitMargin),
        lineStyle: {
          color: '#f59e0b',
          width: 3
        },
        itemStyle: {
          color: '#f59e0b'
        }
      }
    ]
  }

  chartInstance.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })
}

const updateChart = () => {
  if (chartInstance && props.data && props.data.length > 0) {
    const option = {
      xAxis: [
        {
          data: props.data.map(item => item.period)
        }
      ],
      series: [
        {
          data: props.data.map(item => item.revenue)
        },
        {
          data: props.data.map(item => item.expense)
        },
        {
          data: props.data.map(item => item.profit)
        },
        {
          data: props.data.map(item => item.profitMargin)
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    if (props.data && props.data.length > 0) {
      if (chartInstance) {
        updateChart()
      } else {
        initChart()
      }
    }
  })
}, { deep: true })

watch(() => props.type, () => {
  nextTick(() => {
    initChart()
  })
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.profit-analysis-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart-loading, .chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.chart-loading .el-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.chart-empty .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}
</style>
