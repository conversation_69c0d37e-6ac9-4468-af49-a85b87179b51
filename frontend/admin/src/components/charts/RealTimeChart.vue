<template>
  <div class="realtime-chart">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import * as echarts from 'echarts'

const chartContainer = ref(null)
const autoRefresh = ref(true)
let chart = null
let refreshTimer = null

const dashboardStore = useDashboardStore()

// 使用真实数据而不是模拟数据
const metrics = computed(() => ({
  onlineRooms: dashboardStore.dashboardData.availableRooms || 0,
  activeOrders: dashboardStore.dashboardData.activeOrders || 0,
  todayIncome: `¥${(dashboardStore.dashboardData.todayIncome || 0).toFixed(2)}`,
  onlineDevices: dashboardStore.dashboardData.onlineDevices || 0
}))

// 模拟实时数据
const realtimeData = ref({
  times: [],
  orders: [],
  income: [],
  devices: []
})

const initChart = () => {
  if (!chartContainer.value) return
  
  chart = echarts.init(chartContainer.value)
  
  const option = {
    title: {
      text: '实时数据趋势',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['订单数', '收入', '设备在线'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: realtimeData.value.times
    },
    yAxis: [
      {
        type: 'value',
        name: '订单/设备',
        position: 'left',
        min: 0,
        max: function(value) {
          return Math.max(value.max, 10) // 最小显示范围为10
        }
      },
      {
        type: 'value',
        name: '收入(元)',
        position: 'right',
        min: 0,
        max: function(value) {
          return Math.max(value.max, 100) // 最小显示范围为100
        }
      }
    ],
    series: [
      {
        name: '订单数',
        type: 'line',
        data: realtimeData.value.orders,
        smooth: true,
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '收入',
        type: 'line',
        yAxisIndex: 1,
        data: realtimeData.value.income,
        smooth: true,
        itemStyle: { color: '#52c41a' }
      },
      {
        name: '设备在线',
        type: 'line',
        data: realtimeData.value.devices,
        smooth: true,
        itemStyle: { color: '#faad14' }
      }
    ]
  }
  
  chart.setOption(option)
}

const generateRealTimeData = () => {
  const now = new Date()
  const time = now.toLocaleTimeString()

  // 保持最近20个数据点
  if (realtimeData.value.times.length >= 20) {
    realtimeData.value.times.shift()
    realtimeData.value.orders.shift()
    realtimeData.value.income.shift()
    realtimeData.value.devices.shift()
  }

  // 使用真实的Dashboard数据
  const dashboardData = dashboardStore.dashboardData

  realtimeData.value.times.push(time)
  realtimeData.value.orders.push(dashboardData.activeOrders || 0)
  realtimeData.value.income.push(dashboardData.todayIncome || 0)
  realtimeData.value.devices.push(dashboardData.onlineDevices || 0)
}

const refreshData = async () => {
  try {
    // 获取真实数据
    await dashboardStore.fetchDashboardData()

    // 使用真实数据生成图表数据点
    generateRealTimeData()

    if (chart) {
      chart.setOption({
        xAxis: {
          data: realtimeData.value.times
        },
        series: [
          { data: realtimeData.value.orders },
          { data: realtimeData.value.income },
          { data: realtimeData.value.devices }
        ]
      })
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

const toggleAutoRefresh = (value) => {
  if (value) {
    refreshTimer = setInterval(refreshData, 3000) // 每3秒刷新
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

onMounted(async () => {
  // 初始化真实数据
  await dashboardStore.fetchDashboardData()

  // 初始化图表数据（使用真实数据生成初始数据点）
  for (let i = 0; i < 10; i++) {
    generateRealTimeData()
    // 为了显示趋势，稍微延迟一下时间戳
    if (i > 0) {
      const lastTime = realtimeData.value.times[realtimeData.value.times.length - 1]
      const newTime = new Date(Date.now() - (10 - i) * 3000).toLocaleTimeString()
      realtimeData.value.times[realtimeData.value.times.length - 1] = newTime
    }
  }

  initChart()

  if (autoRefresh.value) {
    toggleAutoRefresh(true)
  }

  // 响应式处理
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  if (chart) {
    chart.dispose()
  }
})
</script>

<style scoped>
.realtime-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 300px;
  min-height: 300px;
}
</style>
