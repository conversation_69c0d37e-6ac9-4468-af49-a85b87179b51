<template>
  <div class="revenue-source-chart">
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-else-if="!data || data.length === 0" class="chart-empty">
      <el-icon><DocumentRemove /></el-icon>
      <span>暂无数据</span>
    </div>
    <div v-else ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { Loading, DocumentRemove } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref(null)
let chartInstance = null

const initChart = () => {
  if (!chartRef.value || !props.data || props.data.length === 0) return

  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: props.data.map(item => item.name)
    },
    series: [
      {
        name: '收入来源',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map((item, index) => ({
          value: item.amount,
          name: item.name,
          itemStyle: {
            color: getColor(index)
          }
        }))
      }
    ]
  }

  chartInstance.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })
}

const getColor = (index) => {
  const colors = [
    '#667eea',
    '#f093fb',
    '#4facfe',
    '#43e97b',
    '#f59e0b',
    '#ef4444',
    '#8b5cf6',
    '#10b981'
  ]
  return colors[index % colors.length]
}

const updateChart = () => {
  if (chartInstance && props.data && props.data.length > 0) {
    const option = {
      legend: {
        data: props.data.map(item => item.name)
      },
      series: [
        {
          data: props.data.map((item, index) => ({
            value: item.amount,
            name: item.name,
            itemStyle: {
              color: getColor(index)
            }
          }))
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

watch(() => props.data, () => {
  nextTick(() => {
    if (props.data && props.data.length > 0) {
      if (chartInstance) {
        updateChart()
      } else {
        initChart()
      }
    }
  })
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.revenue-source-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart-loading, .chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.chart-loading .el-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.chart-empty .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}
</style>
