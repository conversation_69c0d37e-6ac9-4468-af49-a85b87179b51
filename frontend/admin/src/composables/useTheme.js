import { ref, watch, onMounted } from 'vue'

// 主题状态
const isDark = ref(false)
const theme = ref('light')

// 主题切换函数
export function useTheme() {
  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    
    if (savedTheme) {
      theme.value = savedTheme
      isDark.value = savedTheme === 'dark'
    } else {
      theme.value = prefersDark ? 'dark' : 'light'
      isDark.value = prefersDark
    }
    
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    const root = document.documentElement
    root.setAttribute('data-theme', theme.value)
    
    if (theme.value === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    isDark.value = theme.value === 'dark'
    localStorage.setItem('theme', theme.value)
    applyTheme()
  }

  // 设置特定主题
  const setTheme = (newTheme) => {
    theme.value = newTheme
    isDark.value = newTheme === 'dark'
    localStorage.setItem('theme', newTheme)
    applyTheme()
  }

  // 监听系统主题变化
  const watchSystemTheme = () => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        theme.value = e.matches ? 'dark' : 'light'
        isDark.value = e.matches
        applyTheme()
      }
    })
  }

  // 监听主题变化
  watch(theme, () => {
    applyTheme()
  })

  // 组件挂载时初始化
  onMounted(() => {
    initTheme()
    watchSystemTheme()
  })

  return {
    isDark,
    theme,
    toggleTheme,
    setTheme,
    initTheme
  }
}

// 全局主题状态
export const globalTheme = {
  isDark,
  theme,
  toggleTheme: () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    isDark.value = theme.value === 'dark'
    localStorage.setItem('theme', theme.value)
    
    const root = document.documentElement
    root.setAttribute('data-theme', theme.value)
    
    if (theme.value === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }
}
