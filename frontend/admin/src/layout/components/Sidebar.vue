<template>
  <div class="sidebar-container">
    <!-- Logo -->
    <div class="logo-container">
      <div class="logo">
        <el-icon size="24" color="#1890ff">
          <House />
        </el-icon>
        <span v-show="!systemConfig.sidebarCollapsed" class="logo-text">
          麻将室管理
        </span>
      </div>
    </div>

    <!-- 菜单 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="systemConfig.sidebarCollapsed"
      :unique-opened="true"
      background-color="#001529"
      text-color="#ffffff"
      active-text-color="#ffffff"
    >
      <template v-for="route in menuRoutes" :key="route.path">
        <!-- 单级菜单 -->
        <el-menu-item
          v-if="!route.children || route.children.length <= 1"
          :index="route.path"
          @click="handleMenuClick(route.path)"
        >
          <el-icon>
            <component :is="route.meta?.icon || 'Document'" />
          </el-icon>
          <template #title>
            {{ route.meta?.title }}
          </template>
        </el-menu-item>

        <!-- 多级菜单 -->
        <el-sub-menu 
          v-else
          :index="route.path"
        >
          <template #title>
            <el-icon>
              <component :is="route.meta?.icon || 'Document'" />
            </el-icon>
            <span>{{ route.meta?.title }}</span>
          </template>
          
          <el-menu-item
            v-for="child in route.children?.filter(c => !c.meta?.hidden)"
            :key="child.path"
            :index="`${route.path}/${child.path}`"
            @click="handleMenuClick(`${route.path}/${child.path}`)"
          >
            <el-icon>
              <component :is="child.meta?.icon || 'Document'" />
            </el-icon>
            <template #title>{{ child.meta?.title }}</template>
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSystemStore } from '@/stores/system'

const route = useRoute()
const router = useRouter()
const systemStore = useSystemStore()

const systemConfig = computed(() => systemStore.systemConfig)

// 当前激活的菜单
const activeMenu = computed(() => {
  const { path } = route

  // 处理子路由的情况，例如 /rooms/edit/1 应该激活 /rooms
  if (path.includes('/rooms')) {
    return '/rooms'
  } else if (path.includes('/orders')) {
    return '/orders'
  } else if (path.includes('/users')) {
    return '/users'
  } else if (path.includes('/devices')) {
    return '/devices'
  } else if (path.includes('/system')) {
    return '/system'
  } else if (path.includes('/platform')) {
    return path // 返回完整路径，如 /platform/orders
  } else if (path.includes('/finance')) {
    return path // 返回完整路径，如 /finance/report
  }

  return path
})

// 处理菜单点击事件
const handleMenuClick = (path) => {
  console.log('菜单点击:', path)
  router.push(path)
}

// 菜单路由（过滤掉登录页和隐藏的路由）
const menuRoutes = computed(() => {
  return router.getRoutes().filter(route => {
    // 过滤条件：
    // 1. 不是登录页
    // 2. 不是根路径
    // 3. 有标题
    // 4. 不是隐藏的
    // 5. 有children的路由（主菜单项）
    return route.path !== '/login' &&
           route.path !== '/' &&
           route.meta?.title &&
           !route.meta?.hidden &&
           route.children?.length > 0
  }).map(route => {
    // 如果路由只有一个子路由，直接使用子路由的路径
    if (route.children?.length === 1 && !route.children[0].meta?.hidden) {
      return {
        ...route,
        path: route.path + '/' + route.children[0].path,
        meta: route.children[0].meta
      }
    }
    return route
  })
})
</script>

<style scoped>
.sidebar-container {
  height: 100vh;
  background: linear-gradient(180deg, var(--gray-900) 0%, var(--gray-800) 100%);
  position: relative;
  overflow: hidden;
}

.sidebar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: #fff;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.logo:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(1.02);
}

.logo-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  white-space: nowrap;
  background: linear-gradient(135deg, #ffffff 0%, var(--primary-300) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

:deep(.el-menu) {
  border-right: none;
  background: transparent !important;
  padding: var(--space-2);
}

:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-1);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

:deep(.el-menu-item::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--gradient-primary);
  transform: scaleY(0);
  transition: transform var(--transition-normal);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

:deep(.el-menu-item .el-icon) {
  color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-normal);
  margin-right: var(--space-3);
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-1);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

:deep(.el-sub-menu .el-sub-menu__title .el-icon) {
  color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-normal);
  margin-right: var(--space-3);
}

/* 子菜单样式 */
:deep(.el-sub-menu .el-menu-item) {
  color: rgba(255, 255, 255, 0.7);
  background-color: transparent;
  margin-left: var(--space-4);
  margin-bottom: var(--space-1);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

:deep(.el-sub-menu .el-menu-item .el-icon) {
  color: rgba(255, 255, 255, 0.7);
  transition: all var(--transition-normal);
}

/* 现代化选中状态样式 */
:deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600)) !important;
  color: #ffffff !important;
  box-shadow: var(--shadow-md);
  transform: translateX(4px);
}

:deep(.el-menu-item.is-active::before) {
  transform: scaleY(1);
}

:deep(.el-menu-item.is-active .el-icon) {
  color: #ffffff !important;
  transform: scale(1.1);
}

/* 现代化悬停状态样式 */
:deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.08) !important;
  color: #ffffff !important;
  transform: translateX(2px);
}

:deep(.el-menu-item:hover .el-icon) {
  color: #ffffff !important;
  transform: scale(1.05);
}

:deep(.el-sub-menu__title:hover) {
  background: rgba(255, 255, 255, 0.08) !important;
  color: #ffffff !important;
  transform: translateX(2px);
}

:deep(.el-sub-menu__title:hover .el-icon) {
  color: #ffffff !important;
  transform: scale(1.05);
}

/* 子菜单现代化样式 */
:deep(.el-sub-menu .el-menu-item.is-active) {
  background: linear-gradient(135deg, var(--primary-400), var(--primary-500)) !important;
  color: #ffffff !important;
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

:deep(.el-sub-menu .el-menu-item.is-active .el-icon) {
  color: #ffffff !important;
  transform: scale(1.1);
}

:deep(.el-sub-menu .el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.06) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  transform: translateX(2px);
}

:deep(.el-sub-menu .el-menu-item:hover .el-icon) {
  color: rgba(255, 255, 255, 0.9) !important;
  transform: scale(1.05);
}

/* 折叠状态优化 */
:deep(.el-menu--collapse .el-menu-item) {
  text-align: center;
}

:deep(.el-menu--collapse .el-menu-item .el-icon) {
  margin-right: 0;
}
</style>
