<template>
  <div class="header-container">
    <!-- 左侧 -->
    <div class="header-left">
      <el-button 
        :icon="systemConfig.sidebarCollapsed ? 'Expand' : 'Fold'"
        text
        @click="toggleSidebar"
      />
      <span class="system-title">自助麻将室管理系统</span>
    </div>

    <!-- 右侧 -->
    <div class="header-right">
      <!-- 全局搜索 -->
      <GlobalSearch />

      <!-- 通知中心 -->
      <NotificationCenter />

      <!-- 主题切换 -->
      <el-tooltip content="切换主题" placement="bottom">
        <el-button 
          :icon="systemConfig.theme === 'dark' ? 'Sunny' : 'Moon'"
          text
          @click="toggleTheme"
        />
      </el-tooltip>

      <!-- 全屏 -->
      <el-tooltip content="全屏" placement="bottom">
        <el-button 
          icon="FullScreen"
          text
          @click="toggleFullscreen"
        />
      </el-tooltip>

      <!-- 刷新 -->
      <el-tooltip content="刷新" placement="bottom">
        <el-button 
          icon="Refresh"
          text
          @click="refresh"
        />
      </el-tooltip>

      <!-- 用户菜单 -->
      <el-dropdown @command="handleCommand">
        <div class="user-info">
          <el-avatar 
            :src="userInfo.avatar" 
            :size="32"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ userInfo.nickname || userInfo.username || '管理员' }}</span>
          <el-icon class="arrow-down"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人资料
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useSystemStore } from '@/stores/system'
import GlobalSearch from '@/components/GlobalSearch.vue'
import NotificationCenter from '@/components/NotificationCenter.vue'

const router = useRouter()
const systemStore = useSystemStore()

const systemConfig = computed(() => systemStore.systemConfig)
const userInfo = computed(() => systemStore.userInfo)

// 切换侧边栏
const toggleSidebar = () => {
  systemStore.toggleSidebar()
}

// 切换主题
const toggleTheme = () => {
  systemStore.toggleTheme()
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 刷新页面
const refresh = () => {
  window.location.reload()
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      router.push('/system/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '系统提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    systemStore.logout()
    router.push('/login')
    ElMessage.success('退出登录成功')
  })
}
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 var(--space-6);
  background: var(--bg-primary);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid var(--border-primary);
  position: relative;
}

.header-container::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
  opacity: 0.3;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.system-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all var(--transition-normal);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-4);
  cursor: pointer;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-xs);
}

.user-info:hover {
  background: var(--bg-secondary);
  border-color: var(--primary-300);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.username {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.arrow-down {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  transition: all var(--transition-fast);
}

.user-info:hover .arrow-down {
  color: var(--primary-500);
  transform: rotate(180deg);
}

/* 顶部按钮样式优化 */
:deep(.el-button) {
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-primary);
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-300);
}

:deep(.el-button.is-text) {
  border: none;
  background: transparent;
}

:deep(.el-button.is-text:hover) {
  background: var(--bg-secondary);
  color: var(--primary-500);
}
</style>
