<template>
  <el-breadcrumb separator="/" class="breadcrumb">
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbList"
      :key="item.path"
      :to="index === breadcrumbList.length - 1 ? undefined : item.path"
    >
      <el-icon v-if="item.icon" class="breadcrumb-icon">
        <component :is="item.icon" />
      </el-icon>
      {{ item.title }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 面包屑列表
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const breadcrumbs = []
  
  // 添加首页
  if (route.path !== '/dashboard') {
    breadcrumbs.push({
      path: '/dashboard',
      title: '首页',
      icon: 'House'
    })
  }
  
  // 处理匹配的路由
  matched.forEach((item, index) => {
    // 跳过根路径
    if (item.path === '/') return
    
    const breadcrumb = {
      path: item.path,
      title: item.meta.title,
      icon: item.meta.icon
    }
    
    // 如果是最后一个且有参数，处理动态路由
    if (index === matched.length - 1 && route.params.id) {
      if (item.name === 'RoomEdit') {
        breadcrumb.title = `编辑房间 #${route.params.id}`
      } else if (item.name === 'OrderDetail') {
        breadcrumb.title = `订单详情 #${route.params.id}`
      }
    }
    
    breadcrumbs.push(breadcrumb)
  })
  
  return breadcrumbs
})
</script>

<style scoped>
.breadcrumb {
  margin-bottom: 16px;
}

.breadcrumb-icon {
  margin-right: 4px;
  font-size: 14px;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #606266;
  cursor: default;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover) {
  color: #606266;
}
</style>
