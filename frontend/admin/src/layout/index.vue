<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside 
        :width="systemConfig.sidebarCollapsed ? '64px' : '200px'"
        class="layout-aside"
      >
        <Sidebar />
      </el-aside>
      
      <!-- 主体内容 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header height="60px" class="layout-header">
          <Header />
        </el-header>
        
        <!-- 主要内容区域 -->
        <el-main class="layout-main">
          <!-- 面包屑导航 -->
          <Breadcrumb />

          <!-- 页面内容 -->
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useSystemStore } from '@/stores/system'
import Header from './components/Header.vue'
import Sidebar from './components/Sidebar.vue'
import Breadcrumb from './components/Breadcrumb.vue'

defineOptions({
  name: 'Layout'
})

const systemStore = useSystemStore()
const systemConfig = computed(() => systemStore.systemConfig)
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.layout-aside {
  transition: width 0.3s;
}

.layout-header {
  display: flex;
  align-items: center;
  padding: 0;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.layout-main {
  background: #f0f2f5;
  overflow-y: auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .layout-aside {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .layout-aside.mobile-open {
    transform: translateX(0);
  }

  .layout-main {
    margin-left: 0 !important;
  }
}

/* 平板适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .layout-aside {
    width: 180px !important;
  }
}
</style>
