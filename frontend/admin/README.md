# 自助麻将室系统 - 管理端Web系统

## 项目概述

这是自助麻将室系统的管理端Web界面，为管理员提供完整的系统管理功能。

## 功能模块

1. **仪表盘**
   - 实时房间状态总览
   - 今日收入统计
   - 近期订单趋势图

2. **房间管理**
   - 房间列表与状态查看
   - 房间信息编辑
   - 计费规则设置
   - 设备绑定与状态监控

3. **订单管理**
   - 所有订单列表查看
   - 订单状态筛选
   - 异常订单处理
   - 退款操作
   - 外卖平台订单核销管理

4. **用户管理**
   - 用户信息查看
   - 会员等级管理
   - 用户行为分析

5. **财务管理**
   - 收入统计报表
   - 财务对账
   - 发票管理

6. **系统设置**
   - 计费规则配置
   - 语音播报内容设置
   - 营业时间设置
   - 优惠活动管理

## 技术选型

- Vue.js 3 + Element Plus
- 状态管理：Pinia
- 构建工具：Vite
- UI组件库：Element Plus
- 网络请求：Axios

## 开发计划

1. 搭建基础项目结构
2. 实现路由和布局
3. 开发各功能模块
4. 集成图表和数据可视化
5. 测试和优化

## 目录结构

```
src/
├── views/          # 页面组件
├── components/     # 公共组件
├── assets/         # 静态资源
├── router/         # 路由配置
├── store/          # 状态管理
├── utils/          # 工具函数
└── api/            # 接口调用
```
