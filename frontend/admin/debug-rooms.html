<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间数据调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
            margin-top: 15px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status-success { color: #52c41a; }
        .status-error { color: #ff4d4f; }
        .status-warning { color: #faad14; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 房间数据调试工具</h1>
        
        <div class="debug-section">
            <div class="debug-title">📡 API数据测试</div>
            <button class="test-button" onclick="testAPI()">测试后端API</button>
            <button class="test-button" onclick="testFrontendProxy()">测试前端代理</button>
            <div id="api-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🎯 前端页面检查</div>
            <button class="test-button" onclick="checkPageErrors()">检查页面错误</button>
            <button class="test-button" onclick="loadRoomPage()">加载房间页面</button>
            <div id="page-result" class="result-box" style="display: none;"></div>
            
            <div class="iframe-container">
                <iframe id="room-frame" src="about:blank"></iframe>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🔧 数据结构验证</div>
            <div id="data-structure" class="result-box">
                <strong>预期的房间数据结构:</strong><br>
                {<br>
                &nbsp;&nbsp;id: number,<br>
                &nbsp;&nbsp;roomNumber: string,<br>
                &nbsp;&nbsp;name: string,<br>
                &nbsp;&nbsp;description: string,<br>
                &nbsp;&nbsp;status: 'available' | 'occupied' | 'maintenance',<br>
                &nbsp;&nbsp;pricingRule: {<br>
                &nbsp;&nbsp;&nbsp;&nbsp;id: number,<br>
                &nbsp;&nbsp;&nbsp;&nbsp;pricePerHour: number,<br>
                &nbsp;&nbsp;&nbsp;&nbsp;name: string<br>
                &nbsp;&nbsp;},<br>
                &nbsp;&nbsp;facilities: string[],<br>
                &nbsp;&nbsp;createdAt: string,<br>
                &nbsp;&nbsp;updatedAt: string<br>
                }
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">📋 问题诊断清单</div>
            <div style="line-height: 1.6;">
                <p><strong>可能的问题原因:</strong></p>
                <ol>
                    <li><span class="status-success">✅ 函数名称不匹配</span> - 已修复</li>
                    <li><span class="status-warning">⚠️ 数据加载失败</span> - 需要检查</li>
                    <li><span class="status-warning">⚠️ 数据格式错误</span> - 需要检查</li>
                    <li><span class="status-warning">⚠️ 表格渲染错误</span> - 需要检查</li>
                    <li><span class="status-warning">⚠️ JavaScript错误</span> - 需要检查</li>
                </ol>
                
                <p><strong>调试步骤:</strong></p>
                <ol>
                    <li>点击"测试后端API"检查数据源</li>
                    <li>点击"测试前端代理"检查代理配置</li>
                    <li>点击"加载房间页面"查看实际页面</li>
                    <li>在浏览器开发者工具中查看控制台错误</li>
                    <li>检查网络请求是否成功</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<span class="status-warning">正在测试后端API...</span>';
            
            try {
                const response = await fetch('http://localhost:8080/api/v1/rooms');
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    resultDiv.innerHTML = `
                        <span class="status-success">✅ 后端API正常</span><br>
                        <strong>状态:</strong> ${response.status}<br>
                        <strong>数据条数:</strong> ${data.data.data.length}<br>
                        <strong>总数:</strong> ${data.data.total}<br>
                        <strong>响应数据:</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <span class="status-error">❌ API响应异常</span><br>
                        <strong>状态:</strong> ${response.status}<br>
                        <strong>响应:</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <span class="status-error">❌ API请求失败</span><br>
                    <strong>错误:</strong> ${error.message}<br>
                    <strong>可能原因:</strong> 后端服务未启动或网络问题
                `;
            }
        }

        async function testFrontendProxy() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<span class="status-warning">正在测试前端代理...</span>';
            
            try {
                const response = await fetch('/api/v1/rooms');
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    resultDiv.innerHTML = `
                        <span class="status-success">✅ 前端代理正常</span><br>
                        <strong>状态:</strong> ${response.status}<br>
                        <strong>数据条数:</strong> ${data.data.data.length}<br>
                        <strong>房间列表:</strong><br>
                        ${data.data.data.map(room => 
                            `- ${room.room_number} ${room.name} (${room.status})`
                        ).join('<br>')}<br>
                        <strong>完整响应:</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <span class="status-error">❌ 代理响应异常</span><br>
                        <strong>状态:</strong> ${response.status}<br>
                        <strong>响应:</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <span class="status-error">❌ 代理请求失败</span><br>
                    <strong>错误:</strong> ${error.message}<br>
                    <strong>可能原因:</strong> 前端代理配置问题
                `;
            }
        }

        function checkPageErrors() {
            const resultDiv = document.getElementById('page-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <span class="status-warning">检查页面错误...</span><br>
                请打开浏览器开发者工具 (F12) 查看:<br>
                1. Console 标签页 - 查看JavaScript错误<br>
                2. Network 标签页 - 查看API请求状态<br>
                3. Elements 标签页 - 查看DOM结构<br><br>
                <strong>常见错误类型:</strong><br>
                - 函数未定义错误<br>
                - 数据格式错误<br>
                - 网络请求失败<br>
                - 组件渲染错误
            `;
        }

        function loadRoomPage() {
            const iframe = document.getElementById('room-frame');
            iframe.src = 'http://localhost:3000/rooms/list';
            
            const resultDiv = document.getElementById('page-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <span class="status-success">✅ 正在加载房间页面</span><br>
                页面地址: http://localhost:3000/rooms/list<br>
                请在下方iframe中查看页面效果，并检查:<br>
                1. 表格是否显示数据<br>
                2. 是否有错误提示<br>
                3. 分页信息是否正确
            `;
        }

        // 页面加载时自动测试
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
