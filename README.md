# 自助麻将室系统

## 项目概述

自助麻将室系统是一个集成了智能硬件控制、移动支付、预约管理等功能的综合性解决方案。该系统旨在为麻将馆经营者提供一套完整的无人值守管理系统，同时为用户提供便捷的自助服务体验。

## 文档结构

### 需求文档

- [系统功能需求](docs/requirements.md) - 详细描述了系统的功能需求，包括用户端、管理端和硬件系统功能

### 设计文档

- [小程序设计](docs/design/mini_program_design.md) - 用户端小程序的设计规范
- [管理端Web设计](docs/design/admin_web_design.md) - 管理端Web界面的设计规范
- [后端设计](docs/design/backend_design.md) - 后端服务的架构设计和API接口规范
- [数据库设计](docs/design/database_design.md) - 数据库表结构和关系设计
- [硬件通信协议](docs/design/hardware_protocol.md) - 后端与硬件设备的通信协议
- [系统架构](docs/design/system_architecture.md) - 整体系统架构设计

## 项目结构

```
.
├── docs/                  # 文档目录
│   ├── requirements.md    # 功能需求文档
│   └── design/            # 设计文档目录
│       ├── mini_program_design.md  # 小程序设计文档
│       ├── admin_web_design.md     # 管理端Web设计文档
│       ├── backend_design.md       # 后端设计文档
│       ├── database_design.md      # 数据库设计文档
│       ├── hardware_protocol.md    # 硬件通信协议文档
│       └── system_architecture.md  # 系统架构文档
├── backend/               # 后端代码目录
├── frontend/              # 前端代码目录
│   ├── mini-program/      # 用户端小程序
│   └── admin/             # 管理端Web系统
├── database/              # 数据库相关文件
└── hardware/              # 硬件相关文件
```

## 技术栈

- **前端**：微信/支付宝小程序 + Vue.js 3 + Element Plus
- **后端**：Golang + Gin + SQLite
- **通信协议**：MQTT + HTTPS
- **硬件**：智能门锁、智能插座、环境传感器
- **第三方服务**：微信支付、美团/饿了么开放平台

## 主要功能

1. **用户自助服务**
   - 扫码开台与计时
   - 在线支付与续费
   - 房间预约
   - 外卖平台订单核销

2. **管理后台**
   - 房间与设备管理
   - 订单与财务管理
   - 用户管理
   - 系统设置

3. **硬件控制**
   - 智能门锁控制
   - 设备通断电控制
   - 环境参数监测

## 部署说明

系统可部署在单台云服务器上，通过Docker容器化部署或直接部署二进制文件。详细部署说明请参考[系统架构文档](docs/design/system_architecture.md)。

## 数据库初始化

系统使用SQLite数据库，需要先初始化数据库表结构和基础数据。

```bash
cd backend
# 如果尚未安装Go依赖，请先运行：
# go mod tidy

# 运行数据库初始化脚本
go run init_db.go
```

## 开发指南

1. 克隆项目代码
2. 根据设计文档实现各模块功能
3. 进行单元测试和集成测试
4. 部署到测试环境进行验证

## 贡献指南

欢迎提交Issue和Pull Request来改进本项目。

## 许可证

本项目仅供学习和参考使用。