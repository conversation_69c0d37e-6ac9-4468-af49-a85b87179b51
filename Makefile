# 自助麻将室系统 Makefile

.PHONY: help init-db build-backend run-backend test clean

# 默认目标
help:
	@echo "自助麻将室系统开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  init-db      - 初始化数据库"
	@echo "  build-backend - 编译后端服务"
	@echo "  run-backend  - 运行后端服务"
	@echo "  test         - 运行测试"
	@echo "  clean        - 清理编译文件"
	@echo "  deps         - 安装依赖"

# 初始化数据库
init-db:
	@echo "初始化数据库..."
	cd backend && go run init_db.go

# 安装后端依赖
deps:
	@echo "安装后端依赖..."
	cd backend && go mod tidy

# 编译后端服务
build-backend:
	@echo "编译后端服务..."
	cd backend && go build -o bin/mahjong-backend main.go

# 运行后端服务
run-backend:
	@echo "运行后端服务..."
	cd backend && go run main.go

# 运行测试
test:
	@echo "运行后端测试..."
	cd backend && go test ./...

# 清理编译文件
clean:
	@echo "清理编译文件..."
	rm -rf backend/bin/
	rm -rf backend/logs/
	rm -rf frontend/*/dist/
	rm -rf frontend/*/node_modules/

# 创建必要的目录
setup-dirs:
	@echo "创建必要的目录..."
	mkdir -p backend/bin
	mkdir -p backend/logs
	mkdir -p database
	mkdir -p frontend/mini-program/src
	mkdir -p frontend/admin/src

# 完整初始化项目
init: setup-dirs deps init-db
	@echo "项目初始化完成！"

# 开发模式运行
dev: init-db
	@echo "启动开发模式..."
	cd backend && go run main.go
