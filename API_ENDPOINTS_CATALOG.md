# 🔌 棋牌室管理系统 API 端点完整清单

## 📋 概览

本文档详细列出了棋牌室管理系统的所有API端点，包括用户端和管理端接口。

### 🏗️ API 基础信息
- **基础URL**: `http://localhost:8080/api/v1`
- **认证方式**: Header中的 `X-User-ID` 和 `Authorization: Bearer <token>`
- **响应格式**: JSON
- **状态码**: 标准HTTP状态码

---

## 👤 用户管理 API

### 用户认证
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| POST | `/users/register` | 用户注册 | `{openid, nickname, avatar_url, phone}` |
| POST | `/users/login` | 用户登录 | `{openid}` |

### 用户资料
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/users/profile` | 获取用户资料 | Header: X-User-ID |
| PUT | `/users/profile` | 更新用户资料 | `{nickname, avatar_url, phone}` |

### 用户余额
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| POST | `/users/recharge` | 用户充值 | `{amount, payment_method}` |
| GET | `/users/balance-records` | 余额变动记录 | Query: page, page_size |

---

## 🏠 房间管理 API

### 房间查询
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/rooms` | 获取房间列表 | Query: page, page_size, status |
| GET | `/rooms/available` | 获取可用房间 | - |
| GET | `/rooms/{id}` | 获取房间详情 | Path: id |
| GET | `/rooms/number/{number}` | 根据房间号获取房间 | Path: number |
| GET | `/rooms/{id}/devices` | 获取房间设备信息 | Path: id |

### 房间管理（管理端）
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| POST | `/admin/rooms` | 创建房间 | `{room_number, name, description, pricing_rule_id}` |
| PUT | `/admin/rooms/{id}` | 更新房间 | `{name, description, pricing_rule_id}` |
| DELETE | `/admin/rooms/{id}` | 删除房间 | Path: id |
| PUT | `/admin/rooms/{id}/status` | 更新房间状态 | `{status}` |
| GET | `/admin/rooms/statistics` | 房间统计数据 | - |

---

## 📋 订单管理 API

### 订单操作
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| POST | `/orders` | 创建订单 | `{room_id, start_time, total_amount}` |
| GET | `/orders` | 获取订单列表 | Query: page, page_size, status, start_date, end_date |
| GET | `/orders/{id}` | 获取订单详情 | Path: id |
| GET | `/orders/user` | 获取用户订单 | Header: X-User-ID, Query: page, page_size |

### 订单支付
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| POST | `/orders/{id}/payment` | 订单支付 | `{payment_method, amount}` |
| POST | `/orders/{id}/extend` | 订单续费 | `{extend_hours, amount}` |
| PUT | `/orders/{id}/end` | 结束订单 | - |

---

## 🔧 设备控制 API (MQTT)

### 设备状态
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| POST | `/mqtt/heartbeat` | 设备心跳上报 | `{device_id, mac_address, status}` |
| POST | `/mqtt/status` | 设备状态上报 | `{device_id, status, data}` |
| GET | `/mqtt/connection-status` | MQTT连接状态 | - |

### 设备控制
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/mqtt/room/{room_id}/devices` | 获取房间设备状态 | Path: room_id |
| POST | `/mqtt/room/{room_id}/lock` | 控制房间门锁 | `{action: "open/close"}` |
| POST | `/mqtt/room/{room_id}/power` | 控制房间电源 | `{action: "on/off"}` |
| POST | `/mqtt/room/{room_id}/message` | 发送自定义消息 | `{message, type}` |

---

## 📊 管理端专用 API

### 仪表盘
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/admin/dashboard` | 仪表盘数据 | - |

### 用户管理
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/admin/users` | 用户列表 | Query: page, page_size, nickname, phone, start_date, end_date |
| GET | `/admin/users/stats` | 用户统计 | - |

### 设备管理
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/admin/devices` | 设备列表 | Query: page, page_size, type, status, room_id |
| POST | `/admin/devices` | 添加设备 | `{type, room_id, mac_address}` |
| PUT | `/admin/devices/{id}` | 更新设备 | `{type, room_id, status}` |
| DELETE | `/admin/devices/{id}` | 删除设备 | Path: id |
| POST | `/admin/devices/batch/power-off` | 批量关闭电源 | `{room_ids: []}` |

### 财务管理
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/admin/finance/report` | 财务报告 | Query: start_date, end_date, type |
| GET | `/admin/finance/income` | 收入统计 | Query: period |
| GET | `/admin/finance/expenses` | 支出记录 | Query: page, page_size, type, start_date, end_date |
| POST | `/admin/finance/expenses` | 添加支出 | `{type, amount, date, description}` |

---

## 🎁 套餐管理 API

### 套餐查询
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/packages` | 获取活跃套餐 | - |
| GET | `/packages/{id}` | 套餐详情 | Path: id |

### 套餐购买
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| POST | `/packages/{id}/purchase` | 购买套餐 | `{payment_method}` |
| POST | `/packages/recharge` | 套餐充值 | `{package_id, amount}` |

### 用户套餐
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/user/packages` | 用户套餐列表 | Header: X-User-ID |
| GET | `/user/packages/stats` | 用户套餐统计 | Header: X-User-ID |
| GET | `/user/packages/{id}` | 用户套餐详情 | Path: id |
| GET | `/user/packages/{id}/logs` | 套餐使用记录 | Path: id |
| POST | `/user/packages/{id}/use` | 使用套餐 | `{room_id, hours}` |

---

## 🏪 外卖平台 API

### 平台订单
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/admin/platform/orders` | 平台订单列表 | Query: page, page_size, platform_type, status |
| POST | `/admin/platform/orders/verify` | 订单核销 | `{platform_order_id, verification_code}` |
| PUT | `/admin/platform/orders/{id}/status` | 更新订单状态 | `{status}` |

---

## ⚙️ 系统配置 API

### 系统设置
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/system/config` | 系统配置 | - |
| GET | `/system/status` | 系统状态 | - |

### 计费规则
| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/admin/pricing-rules` | 计费规则列表 | - |
| POST | `/admin/pricing-rules` | 创建计费规则 | `{name, price_per_hour, overnight_price}` |
| PUT | `/admin/pricing-rules/{id}` | 更新计费规则 | `{name, price_per_hour, overnight_price}` |
| DELETE | `/admin/pricing-rules/{id}` | 删除计费规则 | Path: id |

---

## 📝 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": { ... }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "error message",
  "data": null
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "page": 1,
    "page_size": 10,
    "total": 100,
    "total_pages": 10,
    "data": [ ... ]
  }
}
```
