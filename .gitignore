# 编译输出
/backend/bin/
/backend/logs/
/frontend/*/dist/
/frontend/*/node_modules/

# 数据库文件
/database/*.db
/database/*.db-journal

# 配置文件（包含敏感信息）
/backend/config/production.yaml
/backend/config/local.yaml

# 日志文件
*.log

# 操作系统文件
.DS_Store
Thumbs.db

# IDE 文件
.vscode/
.idea/
*.swp
*.swo

# Go 相关
/backend/vendor/
/backend/go.sum

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 测试覆盖率
coverage/
*.cover

# 环境变量文件
.env
.env.local
.env.production

# 证书文件
*.pem
*.key
*.crt

# MQTT 相关
/mqtt/data/
/mqtt/log/
