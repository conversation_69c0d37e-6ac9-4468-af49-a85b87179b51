# 自助麻将室系统小程序设计文档

## 1. 概述

本文档详细描述了自助麻将室系统用户端小程序的设计，包括功能模块、交互流程、UI组件等。

## 2. 技术选型

- 微信小程序原生开发
- 支付宝小程序原生开发
- 网络请求：wx.request（微信）/my.request（支付宝）
- 跨平台框架：Taro（用于代码复用）

## 3. 项目结构

```
frontend/
└── mini-program/          # 用户端小程序
    ├── src/               # 源代码目录
    │   ├── pages/         # 页面目录
    │   ├── components/    # 公共组件
    │   ├── utils/         # 工具函数
    │   ├── assets/        # 静态资源
    │   └── app.js         # 小程序入口文件
    ├── dist/              # 构建输出目录
    │   ├── weapp/         # 微信小程序构建输出
    │   └── alipay/        # 支付宝小程序构建输出
    └── config/            # 配置文件目录
        ├── weapp.js       # 微信小程序配置
        └── alipay.js      # 支付宝小程序配置
```

## 4. 功能模块

1. **首页/房间列表**
   - 显示所有房间状态（空闲/使用中）
   - 支持按房间号搜索
   - 快速扫码开台入口

2. **扫码开台**
   - 扫描房间二维码后自动识别房间
   - 显示房间信息和计费规则
   - 预支付功能
   - 开台确认按钮

3. **房间使用中**
   - 实时显示已用时长和剩余时间
   - 续费/加时功能
   - 离场结束使用
   - 外卖点餐入口（美团/饿了么）

4. **预约功能**
   - 选择日期和时间段
   - 查看房间可用性
   - 预约支付
   - 预约记录查看

5. **个人中心**
   - 个人信息展示与编辑
   - 历史订单查看
   - 消费统计
   - 优惠券管理

6. **支付与订单**
   - 微信支付集成
   - 支付宝支付集成
   - 支付结果展示
   - 订单详情查看
   - 外卖平台订单核销

## 5. UI设计规范

- 主色调：中国红 (#D32F2F) 和金色 (#FFD700)
- 辅助色：深灰 (#333333) 和浅灰 (#F5F5F5)
- 字体：默认使用系统字体
- 按钮圆角：8px
- 页面边距：16px

## 6. 交互流程

1. 用户扫码 -> 识别房间 -> 显示房间信息 -> 预支付 -> 开台
2. 使用中 -> 实时计时 -> 续费/加时 -> 离场结算 -> 支付
3. 预约 -> 选择时间 -> 确认房间 -> 预支付 -> 预约成功

## 7. 接口规范

- `/api/rooms` - 获取房间列表
- `/api/rooms/{id}` - 获取房间详情
- `/api/orders` - 创建订单
- `/api/orders/{id}/payment` - 订单支付
- `/api/reservations` - 预约相关接口

## 8. 安全性考虑

- 所有接口调用使用HTTPS协议
- 用户敏感信息加密传输
- 微信/支付宝登录授权机制
- 防止重复提交机制

## 9. 性能优化

- 图片资源压缩
- 组件懒加载
- 接口请求缓存