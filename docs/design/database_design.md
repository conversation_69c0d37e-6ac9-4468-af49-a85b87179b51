# 自助麻将室系统数据库设计文档

## 1. 概述

本文档详细描述了自助麻将室系统的数据库设计，包括所有数据表的结构、字段说明、关系约束以及索引设计。

## 2. 技术选型

- 数据库系统：SQLite
- 字符集：UTF-8
- 排序规则：默认排序规则

## 3. 数据库表结构

### 3.1 用户表 (users)

存储系统用户的基本信息和余额。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 用户ID |
| openid | TEXT | NO | NO | NO | YES | - | 微信/支付宝 openid |
| nickname | TEXT | YES | NO | NO | NO | NULL | 用户昵称 |
| avatar_url | TEXT | YES | NO | NO | NO | NULL | 头像URL |
| phone | TEXT | YES | NO | NO | NO | NULL | 手机号码 |
| balance | DECIMAL(10,2) | NO | NO | NO | NO | 0.00 | 用户余额 |
| created_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 注册时间 |
| updated_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 更新时间 |

### 3.2 房间表 (rooms)

存储房间的基本信息和当前状态。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 房间ID |
| room_number | TEXT | NO | NO | NO | YES | - | 房间编号 |
| name | TEXT | NO | NO | NO | NO | - | 房间名称 |
| description | TEXT | YES | NO | NO | NO | NULL | 房间描述 |
| status | TEXT | NO | NO | NO | NO | 'available' | 房间状态 (available, occupied, maintenance) |
| pricing_rule_id | INTEGER | YES | NO | NO | NO | NULL | 计费规则ID |
| created_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 更新时间 |

### 3.3 计费规则表 (pricing_rules)

存储不同时间段和条件下的计费规则。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 规则ID |
| name | TEXT | NO | NO | NO | NO | - | 规则名称 |
| price_per_hour | DECIMAL(10,2) | NO | NO | NO | NO | - | 每小时价格 |
| overnight_price | DECIMAL(10,2) | YES | NO | NO | NO | NULL | 包夜价格 |
| start_time | TEXT | YES | NO | NO | NO | NULL | 规则生效时间 (HH:MM) |
| end_time | TEXT | YES | NO | NO | NO | NULL | 规则结束时间 (HH:MM) |
| is_weekend | BOOLEAN | NO | NO | NO | NO | FALSE | 是否适用于周末 |
| is_holiday | BOOLEAN | NO | NO | NO | NO | FALSE | 是否适用于节假日 |
| created_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 创建时间 |

### 3.4 订单表 (orders)

存储用户使用房间的订单信息。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 订单ID |
| user_id | INTEGER | NO | NO | NO | NO | - | 用户ID |
| room_id | INTEGER | NO | NO | NO | NO | - | 房间ID |
| start_time | DATETIME | NO | NO | NO | NO | - | 开始时间 |
| end_time | DATETIME | YES | NO | NO | NO | NULL | 结束时间 |
| total_amount | DECIMAL(10,2) | NO | NO | NO | NO | - | 总费用 |
| paid_amount | DECIMAL(10,2) | NO | NO | NO | NO | 0.00 | 已支付金额 |
| status | TEXT | NO | NO | NO | NO | 'pending' | 订单状态 (pending, paid, completed, cancelled) |
| created_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 更新时间 |

### 3.5 外卖平台订单表 (platform_orders)

存储来自美团、饿了么等外卖平台的订单信息。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 订单ID |
| platform_type | TEXT | NO | NO | NO | NO | - | 平台类型 (meituan, eleme) |
| platform_order_id | TEXT | NO | NO | NO | YES | - | 平台订单号 |
| room_id | INTEGER | NO | NO | NO | NO | - | 关联房间ID |
| user_id | INTEGER | NO | NO | NO | NO | - | 用户ID |
| original_amount | DECIMAL(10,2) | NO | NO | NO | NO | - | 原价 |
| discount_amount | DECIMAL(10,2) | NO | NO | NO | NO | 0.00 | 折扣金额 |
| paid_amount | DECIMAL(10,2) | NO | NO | NO | NO | - | 实际支付金额 |
| order_status | TEXT | NO | NO | NO | NO | 'pending' | 订单状态 (pending, paid, completed, cancelled) |
| verification_status | TEXT | NO | NO | NO | NO | 'pending' | 核销状态 (pending, verified, refunded) |
| created_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 创建时间 |
| verified_at | DATETIME | YES | NO | NO | NO | NULL | 核销时间 |

### 3.6 设备表 (devices)

存储硬件设备的信息和状态。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 设备ID |
| type | TEXT | NO | NO | NO | NO | - | 设备类型 (lock, socket, sensor) |
| room_id | INTEGER | YES | NO | NO | NO | NULL | 关联房间ID |
| mac_address | TEXT | YES | NO | NO | YES | NULL | MAC地址 |
| status | TEXT | NO | NO | NO | NO | 'online' | 设备状态 (online, offline, maintenance) |
| last_heartbeat | DATETIME | YES | NO | NO | NO | NULL | 最后心跳时间 |
| installed_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 安装时间 |

### 3.7 预约表 (reservations)

存储用户的房间预约信息。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 预约ID |
| user_id | INTEGER | NO | NO | NO | NO | - | 用户ID |
| room_id | INTEGER | NO | NO | NO | NO | - | 房间ID |
| start_time | DATETIME | NO | NO | NO | NO | - | 预约开始时间 |
| end_time | DATETIME | NO | NO | NO | NO | - | 预约结束时间 |
| status | TEXT | NO | NO | NO | NO | 'confirmed' | 预约状态 (confirmed, cancelled, completed) |
| created_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 创建时间 |

### 3.8 优惠活动表 (promotions)

存储优惠活动信息。

| 字段名 | 数据类型 | 允许空 | 主键 | 自增 | 唯一 | 默认值 | 说明 |
|--------|---------|-------|------|------|------|--------|------|
| id | INTEGER | NO | YES | YES | NO | - | 活动ID |
| name | TEXT | NO | NO | NO | NO | - | 活动名称 |
| description | TEXT | YES | NO | NO | NO | NULL | 活动描述 |
| discount | DECIMAL(3,2) | NO | NO | NO | NO | - | 折扣率 (0.00 - 1.00) |
| start_time | DATETIME | NO | NO | NO | NO | - | 活动开始时间 |
| end_time | DATETIME | NO | NO | NO | NO | - | 活动结束时间 |
| is_active | BOOLEAN | NO | NO | NO | NO | 1 | 是否激活 |
| created_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | NO | NO | NO | NO | CURRENT_TIMESTAMP | 更新时间 |

## 4. 表关系

```mermaid
erDiagram
    users ||--o{ orders : "places"
    users ||--o{ reservations : "makes"
    users ||--o{ platform_orders : "places"
    rooms ||--o{ orders : "contains"
    rooms ||--o{ reservations : "reserved"
    rooms ||--o{ devices : "equipped_with"
    rooms ||--|| pricing_rules : "has"
    orders ||--o{ platform_orders : "associated_with"
    orders ||--o{ promotions : "applied"
```

## 5. 索引设计

### 5.1 用户表索引

- 主键索引：`id`
- 唯一索引：`openid`
- 普通索引：`created_at`

### 5.2 房间表索引

- 主键索引：`id`
- 唯一索引：`room_number`
- 普通索引：`status`, `pricing_rule_id`

### 5.3 计费规则表索引

- 主键索引：`id`
- 普通索引：`is_weekend`, `is_holiday`

### 5.4 订单表索引

- 主键索引：`id`
- 普通索引：`user_id`, `room_id`, `status`, `created_at`

### 5.5 外卖平台订单表索引

- 主键索引：`id`
- 唯一索引：`platform_order_id`
- 普通索引：`platform_type`, `room_id`, `user_id`, `order_status`, `verification_status`

### 5.6 设备表索引

- 主键索引：`id`
- 唯一索引：`mac_address`
- 普通索引：`type`, `status`, `room_id`

### 5.7 预约表索引

- 主键索引：`id`
- 普通索引：`user_id`, `room_id`, `status`, `start_time`, `end_time`

### 5.8 优惠活动表索引

- 主键索引：`id`
- 普通索引：`is_active`, `start_time`, `end_time`

## 6. 数据初始化

### 6.1 初始计费规则

```sql
-- 工作日每小时计费规则
INSERT INTO pricing_rules (name, price_per_hour, is_weekend, is_holiday) 
VALUES ('工作日每小时', 20.00, FALSE, FALSE);

-- 周末每小时计费规则
INSERT INTO pricing_rules (name, price_per_hour, is_weekend, is_holiday) 
VALUES ('周末每小时', 25.00, TRUE, FALSE);

-- 包夜计费规则
INSERT INTO pricing_rules (name, overnight_price, start_time, end_time) 
VALUES ('包夜', 150.00, '22:00', '06:00');
```

### 6.2 初始房间数据

```sql
-- 示例房间数据
INSERT INTO rooms (room_number, name, description, pricing_rule_id) 
VALUES 
('A01', '牡丹厅', '豪华大包间，配备高级麻将机', 1),
('A02', '梅花厅', '标准包间，舒适环境', 1),
('B01', '兰花厅', '中等包间，性价比高', 1);
```

### 6.3 初始优惠活动数据

```sql
-- 示例优惠活动数据
INSERT INTO promotions (name, description, discount, start_time, end_time, is_active) 
VALUES 
('新用户首单立减', '新用户首次下单立减10元', 0.10, '2023-01-01 00:00:00', '2023-12-31 23:59:59', 1),
('周末狂欢', '周末所有订单享受8折优惠', 0.20, '2023-01-01 00:00:00', '2023-12-31 23:59:59', 1);
```

## 7. 备份与维护

- 每日定时备份数据库
- 定期清理过期数据
- 监控数据库性能
- 定期更新统计信息