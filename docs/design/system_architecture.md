# 自助麻将室系统架构设计文档

## 1. 概述

本文档详细描述了自助麻将室系统的整体架构设计，包括系统组成、模块间关系、数据流向、部署架构等。

## 2. 系统组成

系统由以下主要组件构成：

1. **用户端小程序**：提供给用户使用的微信小程序，支持扫码开台、预约、支付等功能
2. **管理端Web系统**：提供给管理员使用的Web管理系统，支持房间管理、订单管理、用户管理等功能
3. **后端服务**：核心业务逻辑处理，包括API服务、MQTT通信、数据库访问等
4. **数据库**：存储系统所有数据，使用SQLite数据库
5. **MQTT消息代理**：处理后端与硬件设备间的通信
6. **硬件设备**：包括智能门锁、插座、传感器等设备
7. **第三方服务**：微信支付、美团/饿了么等第三方平台

## 3. 架构图

```mermaid
graph TB
    subgraph "用户端"
        A[微信小程序]
    end
    
    subgraph "管理端"
        B[Web管理系统]
    end
    
    subgraph "后端服务"
        C[API服务]
        D[MQTT客户端]
        E[业务逻辑层]
        F[数据访问层]
    end
    
    subgraph "数据存储"
        G[SQLite数据库]
    end
    
    subgraph "消息中间件"
        H[MQTT消息代理]
    end
    
    subgraph "硬件设备"
        I[智能门锁]
        J[智能插座]
        K[环境传感器]
    end
    
    subgraph "第三方服务"
        L[微信支付]
        M[美团/饿了么]
    end
    
    A -- HTTPS --> C
    B -- HTTPS --> C
    C <--> E
    E <--> F
    F <--> G
    D <-- MQTT --> H
    H <-- MQTT --> I
    H <-- MQTT --> J
    H <-- MQTT --> K
    C -- HTTPS --> L
    C -- HTTPS --> M
```

## 4. 模块间关系

### 4.1 前后端关系

- 用户端小程序通过HTTPS协议与后端API服务通信
- 管理端Web系统通过HTTPS协议与后端API服务通信
- 后端API服务提供RESTful接口供前后端调用

### 4.2 后端与数据库关系

- 后端服务通过SQLX库访问SQLite数据库
- 数据访问层封装了所有数据库操作
- 业务逻辑层通过数据访问层操作数据库

### 4.3 后端与硬件关系

- 后端服务作为MQTT客户端连接MQTT消息代理
- 硬件设备作为MQTT客户端连接MQTT消息代理
- 后端与硬件设备通过MQTT协议进行双向通信

### 4.4 后端与第三方服务关系

- 后端服务通过HTTPS协议与微信支付API通信
- 后端服务通过HTTPS协议与美团/饿了么开放平台API通信

## 5. 数据流向

### 5.1 用户开台流程

1. 用户打开小程序，扫描房间二维码
2. 小程序向后端发送开台请求
3. 后端验证用户身份和房间状态
4. 后端通过MQTT向门锁发送开锁指令
5. 门锁执行开锁并上报状态
6. 后端创建订单并开始计时
7. 小程序显示开台成功和计时信息

### 5.2 用户支付流程

1. 用户在小程序中点击支付
2. 小程序向后端发送支付请求
3. 后端调用微信支付统一下单接口
4. 后端返回支付参数给小程序
5. 用户在小程序中完成支付
6. 微信支付异步通知后端支付结果
7. 后端更新订单状态

### 5.3 外卖平台订单核销流程

1. 用户在美团/饿了么平台下单并选择在麻将室使用
2. 美团/饿了么通过API将订单信息推送给后端
3. 用户在小程序中展示订单二维码
4. 管理员在管理端扫描订单二维码
5. 后端验证订单信息并向美团/饿了么发送核销请求
6. 美团/饿了么返回核销结果
7. 后端更新订单状态并记录核销信息

### 5.4 设备状态监控流程

1. 硬件设备定期向MQTT代理发送心跳消息
2. MQTT代理将心跳消息转发给后端服务
3. 后端服务更新设备状态信息
4. 管理端Web系统实时显示设备状态

## 6. 部署架构

### 6.1 服务器部署

- 云服务器：1台2核4GB内存服务器
- 操作系统：Ubuntu 20.04 LTS
- Web服务器：Nginx
- 应用服务器：Golang Gin框架
- 数据库：SQLite
- MQTT代理：EMQX

### 6.2 网络架构

```
Internet
    |
    | (HTTPS)
    |
  Nginx (负载均衡/SSL终止)
    |
    | (反向代理)
    |
  Golang后端服务 ---- MQTT ---- EMQX ---- 硬件设备
    |                               |
    | (数据库访问)                  | (设备通信)
    |                               |
  SQLite数据库                  门锁/插座/传感器
    |
    | (API调用)
    |
  小程序/Web管理端
```

### 6.3 部署目录结构

```
/opt/mahjong/
├── backend/           # 后端服务
│   ├── bin/           # 编译后的二进制文件
│   ├── config/        # 配置文件
│   └── logs/          # 日志文件
├── database/          # 数据库文件
│   └── mahjong.db     # SQLite数据库文件
├── mqtt/              # MQTT代理
│   └── emqx/          # EMQX安装目录
├── frontend/          # 前端静态文件
│   ├── mini-program/  # 小程序静态文件
│   └── admin/         # 管理端静态文件
└── backup/            # 数据备份目录
```

## 7. 安全架构

### 7.1 网络安全

- 所有外部通信使用HTTPS协议
- MQTT通信使用TLS加密
- 防火墙限制端口访问
- 定期安全扫描

### 7.2 身份认证

- 用户端：微信OAuth2.0认证
- 管理端：用户名/密码认证 + JWT Token
- 硬件设备：MQTT用户名/密码认证

### 7.3 数据安全

- 敏感信息加密存储
- 数据库访问权限控制
- API接口权限验证
- 防SQL注入

### 7.4 应用安全

- 防止重复提交
- 输入参数校验
- 限流机制
- 异常日志记录

## 8. 性能架构

### 8.1 性能指标

- API响应时间：< 200ms
- 页面加载时间：< 1s
- MQTT消息延迟：< 100ms
- 数据库查询时间：< 50ms

### 8.2 性能优化

- 数据库索引优化
- 查询缓存
- 连接池管理
- 静态资源CDN
- 图片压缩
- 代码压缩

### 8.3 扩展性设计

- 微服务架构（未来扩展）
- 数据库读写分离（未来扩展）
- 负载均衡支持
- 容器化部署支持

## 9. 监控与运维

### 9.1 监控指标

- 系统可用性：99.9%
- API成功率：99.5%
- 数据库性能
- 硬件设备在线率

### 9.2 日志管理

- 应用日志：记录业务操作
- 错误日志：记录系统异常
- 访问日志：记录API调用
- 安全日志：记录安全事件

### 9.3 告警机制

- 系统宕机告警
- 性能阈值告警
- 硬件故障告警
- 安全事件告警

## 10. 灾备方案

### 10.1 数据备份

- 每日自动备份数据库
- 备份文件异地存储
- 定期备份恢复演练

### 10.2 故障恢复

- 主备服务器切换
- 数据库恢复流程
- 服务重启脚本
- 故障处理手册