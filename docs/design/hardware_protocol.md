# 自助麻将室系统硬件通信协议设计文档

## 1. 概述

本文档详细描述了自助麻将室系统后端与硬件设备之间的通信协议，包括MQTT主题定义、消息格式、QoS等级、安全机制等。

## 2. 技术选型

- 通信协议：MQTT 3.1.1
- 消息代理：EMQX
- QoS等级：0, 1, 2
- 数据格式：JSON
- 安全机制：TLS/SSL + 用户名/密码认证

## 3. MQTT连接配置

### 3.1 连接参数

- Broker地址：`mqtt.mahjong-system.com`
- 端口：8883 (TLS)
- 协议：MQTTS
- 客户端ID：`backend-{instance_id}`
- 用户名：`backend_user`
- 密码：`{password}`

### 3.2 TLS配置

- 协议版本：TLS 1.2+
- 加密套件：TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
- 证书验证：启用

## 4. 主题(Topic)设计

### 4.1 命名规范

所有主题遵循以下命名规范：
```
mahjong/{scope}/{device_type}/{device_id}/{action}
```

其中：
- `scope`：作用域，分为 `system` (系统级) 和 `device` (设备级)
- `device_type`：设备类型，如 `lock` (门锁), `socket` (插座), `sensor` (传感器)
- `device_id`：设备唯一标识符
- `action`：操作类型

### 4.2 控制指令主题

后端向设备发送控制指令的主题：

- `mahjong/device/lock/+/control` - 门锁控制
- `mahjong/device/socket/+/control` - 插座控制
- `mahjong/device/sensor/+/control` - 传感器控制
- `mahjong/system/+/broadcast` - 系统广播消息

### 4.3 状态上报主题

设备向后端上报状态的主题：

- `mahjong/device/lock/+/status` - 门锁状态
- `mahjong/device/socket/+/status` - 插座状态
- `mahjong/device/sensor/+/status` - 传感器状态
- `mahjong/device/+/heartbeat` - 设备心跳

## 5. 消息格式

所有消息均采用JSON格式，UTF-8编码。

### 5.1 控制指令消息

```json
{
  "cmd": "command_name",
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "unique_request_id",
  "params": {
    // 命令特定参数
  }
}
```

### 5.2 状态上报消息

```json
{
  "status": "status_value",
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "optional_request_id",
  "data": {
    // 状态数据
  }
}
```

### 5.3 心跳消息

```json
{
  "heartbeat": true,
  "timestamp": "2023-01-01T12:00:00Z",
  "battery": 80,
  "signal": -70,
  "version": "1.0.0"
}
```

## 6. 指令集定义

### 6.1 门锁控制指令

#### 开锁指令

- 指令名：`open_lock`
- QoS等级：1
- 参数：
  - `duration` (integer, 可选)：开锁持续时间（秒），默认30秒
- 示例：
```json
{
  "cmd": "open_lock",
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "req_001",
  "params": {
    "duration": 60
  }
}
```

#### 关锁指令

- 指令名：`close_lock`
- QoS等级：1
- 参数：无
- 示例：
```json
{
  "cmd": "close_lock",
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "req_002",
  "params": {}
}
```

### 6.2 插座控制指令

#### 通电指令

- 指令名：`power_on`
- QoS等级：1
- 参数：无
- 示例：
```json
{
  "cmd": "power_on",
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "req_003",
  "params": {}
}
```

#### 断电指令

- 指令名：`power_off`
- QoS等级：1
- 参数：无
- 示例：
```json
{
  "cmd": "power_off",
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "req_004",
  "params": {}
}
```

### 6.3 传感器控制指令

#### 数据采集指令

- 指令名：`collect_data`
- QoS等级：0
- 参数：
  - `interval` (integer, 可选)：采集间隔（秒），默认60秒
- 示例：
```json
{
  "cmd": "collect_data",
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "req_005",
  "params": {
    "interval": 30
  }
}
```

## 7. 状态定义

### 7.1 门锁状态

- `locked`：已上锁
- `unlocked`：已解锁
- `jamming`：卡顿
- `offline`：离线
- `error`：错误

### 7.2 插座状态

- `on`：通电
- `off`：断电
- `offline`：离线
- `error`：错误

### 7.3 传感器状态

- `normal`：正常
- `warning`：警告
- `alarm`：报警
- `offline`：离线
- `error`：错误

## 8. QoS等级说明

- QoS 0：最多一次传递，适用于心跳等非关键消息
- QoS 1：至少一次传递，适用于控制指令等重要消息
- QoS 2：恰好一次传递，适用于关键状态变更等消息

## 9. 安全机制

### 9.1 认证机制

- 用户名/密码认证
- 客户端ID唯一性验证

### 9.2 传输安全

- TLS/SSL加密传输
- 客户端证书验证（可选）

### 9.3 消息安全

- 消息完整性校验
- 防重放攻击机制
- 敏感信息加密

## 10. 错误处理

### 10.1 错误码定义

- `0`：成功
- `1001`：参数错误
- `1002`：设备离线
- `1003`：执行超时
- `1004`：设备故障
- `1005`：权限不足
- `1006`：未知命令

### 10.2 错误响应格式

```json
{
  "error": {
    "code": 1001,
    "message": "参数错误",
    "details": "缺少必需参数duration"
  },
  "timestamp": "2023-01-01T12:00:00Z",
  "request_id": "req_001"
}
```

## 11. 心跳机制

### 11.1 心跳间隔

- 设备心跳间隔：60秒
- 心跳超时时间：180秒

### 11.2 心跳处理

- 接收到心跳后更新设备最后在线时间
- 超过超时时间未收到心跳则标记设备为离线
- 定期清理长时间离线的设备状态

## 12. 重连机制

### 12.1 重连策略

- 指数退避算法
- 最大重连间隔：60秒
- 最大重连次数：无限重试

### 12.2 重连处理

- 重连成功后重新订阅主题
- 同步设备状态
- 重新发送未确认的指令