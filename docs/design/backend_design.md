# 自助麻将室系统后端设计文档

## 1. 概述

本文档详细描述了自助麻将室系统的后端设计，包括架构设计、模块划分、API接口规范、数据库设计实现以及与硬件设备和第三方平台的集成方案。

## 2. 技术选型

- 编程语言：Golang
- Web框架：Gin
- 数据库：SQLite
- ORM库：SQLX
- MQTT客户端：Eclipse Paho MQTT
- 配置管理：Viper
- 日志库：Zap
- API文档：Swagger

## 3. 系统架构

```
backend/
├── main.go              # 程序入口
├── config/              # 配置文件
├── models/              # 数据模型
├── controllers/         # 控制器
├── services/            # 业务逻辑层
├── repositories/        # 数据访问层
├── mqtt/                # MQTT通信模块
├── utils/               # 工具函数
└── api/                 # API路由定义
```

## 4. 模块设计

### 4.1 用户模块

**功能描述：**
- 用户注册与登录
- 用户信息管理
- 余额充值与消费
- 会员等级管理

**核心实体：**
- User: 用户基本信息
- BalanceRecord: 余额变动记录

### 4.2 房间模块

**功能描述：**
- 房间信息管理
- 房间状态监控
- 计费规则设置

**核心实体：**
- Room: 房间信息
- PricingRule: 计费规则

### 4.3 订单模块

**功能描述：**
- 订单创建与管理
- 支付处理
- 订单状态跟踪
- 外卖平台订单核销

**核心实体：**
- Order: 普通订单
- PlatformOrder: 外卖平台订单

### 4.4 预约模块

**功能描述：**
- 预约创建与管理
- 预约时间冲突检测
- 预约提醒

**核心实体：**
- Reservation: 预约信息

### 4.5 设备模块

**功能描述：**
- 设备信息管理
- 设备状态监控
- 远程控制指令下发

**核心实体：**
- Device: 设备信息

### 4.6 MQTT通信模块

**功能描述：**
- 与硬件设备通信
- 指令下发与状态接收
- 心跳检测
- 设备状态自动更新

**核心功能：**
- Connect: 建立MQTT连接
- Publish: 发布消息
- Subscribe: 订阅主题
- MessageHandler: 消息处理
- DeviceStatusUpdate: 设备状态更新
- HeartbeatMonitoring: 心跳监控

## 5. API接口设计

### 5.1 用户相关接口

- `POST /api/v1/users/register` - 用户注册
- `POST /api/v1/users/login` - 用户登录
- `GET /api/v1/users/profile` - 获取用户信息
- `PUT /api/v1/users/profile` - 更新用户信息
- `POST /api/v1/users/recharge` - 用户充值

### 5.2 房间相关接口

- `GET /api/v1/rooms` - 获取房间列表
- `GET /api/v1/rooms/{id}` - 获取房间详情
- `POST /api/v1/rooms` - 创建房间
- `PUT /api/v1/rooms/{id}` - 更新房间信息

### 5.3 订单相关接口

- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/orders` - 获取订单列表
- `GET /api/v1/orders/{id}` - 获取订单详情
- `POST /api/v1/orders/{id}/payment` - 订单支付
- `POST /api/v1/orders/{id}/extend` - 订单续费
- `POST /api/v1/orders/{id}/complete` - 完成订单
- `POST /api/v1/platform-orders/verify` - 外卖平台订单核销

### 5.4 预约相关接口

- `POST /api/v1/reservations` - 创建预约
- `GET /api/v1/reservations` - 获取预约列表
- `PUT /api/v1/reservations/{id}/cancel` - 取消预约

### 5.5 管理端接口

- `GET /api/v1/admin/dashboard` - 获取仪表盘数据
- `GET /api/v1/admin/rooms` - 获取房间管理列表
- `POST /api/v1/admin/rooms/control` - 远程控制房间设备
- `GET /api/v1/admin/orders` - 获取订单管理列表
- `POST /api/v1/admin/orders/{id}/refund` - 订单退款
- `GET /api/v1/admin/users` - 获取用户列表
- `GET /api/v1/admin/finance/report` - 获取财务报表
- `GET /api/v1/admin/devices` - 获取设备列表

## 6. 数据库设计实现

### 6.1 用户表 (users)

```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  openid TEXT UNIQUE NOT NULL,
  nickname TEXT,
  avatar_url TEXT,
  phone TEXT,
  balance DECIMAL(10,2) DEFAULT 0.00,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 6.2 房间表 (rooms)

```sql
CREATE TABLE rooms (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  room_number TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'available', -- available, occupied, maintenance
  pricing_rule_id INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 6.3 计费规则表 (pricing_rules)

```sql
CREATE TABLE pricing_rules (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  price_per_hour DECIMAL(10,2) NOT NULL,
  overnight_price DECIMAL(10,2),
  start_time TEXT, -- 规则生效时间
  end_time TEXT,   -- 规则结束时间
  is_weekend BOOLEAN DEFAULT FALSE,
  is_holiday BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 6.4 订单表 (orders)

```sql
CREATE TABLE orders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  room_id INTEGER NOT NULL,
  start_time DATETIME NOT NULL,
  end_time DATETIME,
  total_amount DECIMAL(10,2) NOT NULL,
  paid_amount DECIMAL(10,2) DEFAULT 0.00,
  status TEXT DEFAULT 'pending', -- pending, paid, completed, cancelled
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 6.5 外卖平台订单表 (platform_orders)

```sql
CREATE TABLE platform_orders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  platform_type TEXT NOT NULL, -- meituan, eleme
  platform_order_id TEXT UNIQUE NOT NULL,
  room_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  original_amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0.00,
  paid_amount DECIMAL(10,2) NOT NULL,
  order_status TEXT DEFAULT 'pending', -- pending, paid, completed, cancelled
  verification_status TEXT DEFAULT 'pending', -- pending, verified, refunded
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  verified_at DATETIME
);
```

### 6.6 设备表 (devices)

```sql
CREATE TABLE devices (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type TEXT NOT NULL, -- lock, socket, sensor
  room_id INTEGER,
  mac_address TEXT UNIQUE,
  status TEXT DEFAULT 'online', -- online, offline, maintenance
  last_heartbeat DATETIME,
  installed_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 6.7 预约表 (reservations)

```sql
CREATE TABLE reservations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  room_id INTEGER NOT NULL,
  start_time DATETIME NOT NULL,
  end_time DATETIME NOT NULL,
  status TEXT DEFAULT 'confirmed', -- confirmed, cancelled, completed
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 7. MQTT通信设计

### 7.1 主题定义

- `mahjong/control/{device_type}/{device_id}` - 设备控制指令下发
- `mahjong/status/{device_id}` - 设备状态上报
- `mahjong/heartbeat/{device_id}` - 设备心跳

### 7.2 消息格式

**控制指令：**
```json
{
  "cmd": "open_lock",
  "timestamp": "2023-01-01T12:00:00Z",
  "params": {
    "duration": 30
  }
}
```

**状态上报：**
```json
{
  "status": "online",
  "timestamp": "2023-01-01T12:00:00Z",
  "battery": 80,
  "signal": -70
}
```

### 7.3 消息处理

MQTT客户端实现了消息处理器，能够自动处理设备状态更新和心跳消息：
- 当收到设备状态消息时，自动更新设备在数据库中的状态
- 当收到设备心跳消息时，自动更新设备状态为在线并记录心跳时间
- 消息处理器能够正确解析不同主题的消息并调用相应的服务层函数

### 7.4 设备控制

管理端提供了设备控制接口，可以通过API远程控制房间设备：
- 支持多种控制命令（如开锁、关锁等）
- 通过MQTT协议将控制指令发送到指定设备
- 提供设备列表查询功能，方便管理员查看所有设备状态

## 8. 第三方平台集成

### 8.1 微信支付集成

- 使用微信官方SDK
- 实现统一下单接口
- 支付结果通知处理
- 退款接口实现

### 8.2 外卖平台集成

- 美团/饿了么开放平台API对接
- 订单同步接口
- 核销接口
- 退款接口
- 商品信息同步

## 9. 安全性设计

- HTTPS通信
- JWT Token认证
- 数据库访问权限控制
- 敏感信息加密存储
- 防SQL注入
- 防止重复提交
- 限流机制

## 10. 性能优化

- 数据库索引优化
- 查询缓存
- 连接池管理
- 异步任务处理
- 静态资源压缩