# 自助麻将室系统管理端Web设计文档

## 1. 概述

本文档详细描述了自助麻将室系统管理端Web界面的设计，包括功能模块、交互流程、UI组件等。

## 2. 技术选型

- Vue.js 3 + Element Plus
- 状态管理：Pinia
- 构建工具：Vite
- UI组件库：Element Plus
- 网络请求：Axios

## 3. 项目结构

```
frontend/
└── admin/                 # 管理端Web
    ├── src/               # 源代码目录
    │   ├── views/         # 页面组件
    │   ├── components/    # 公共组件
    │   ├── assets/        # 静态资源
    │   ├── router/        # 路由配置
    │   ├── store/         # 状态管理
    │   ├── utils/         # 工具函数
    │   └── api/           # 接口调用
    └── index.html         # 入口HTML文件
```

## 4. 功能模块

1. **仪表盘**
   - 实时房间状态总览
   - 今日收入统计
   - 近期订单趋势图

2. **房间管理**
   - 房间列表与状态查看
   - 房间信息编辑
   - 计费规则设置
   - 设备绑定与状态监控

3. **订单管理**
   - 所有订单列表查看
   - 订单状态筛选
   - 异常订单处理
   - 退款操作
   - 外卖平台订单核销管理

4. **用户管理**
   - 用户信息查看
   - 会员等级管理
   - 用户行为分析

5. **财务管理**
   - 收入统计报表
   - 财务对账
   - 发票管理

6. **系统设置**
   - 计费规则配置
   - 语音播报内容设置
   - 营业时间设置
   - 优惠活动管理

## 5. UI设计规范

- 布局：左侧导航菜单，顶部工具栏，主内容区域
- 主色调：深蓝 (#1E88E5) 和白色
- 辅助色：浅灰 (#F5F5F5) 和深灰 (#333333)
- 导航高度：60px
- 侧边栏宽度：200px

## 6. 页面原型描述

1. **仪表盘页面**
   - 顶部显示今日收入、使用中房间数、空闲房间数等关键指标
   - 中部显示房间状态分布图
   - 底部显示近期订单趋势图

2. **房间管理页面**
   - 表格形式展示房间列表，包含房间号、状态、计费规则等信息
   - 支持按房间号、状态筛选
   - 提供添加、编辑、删除房间的功能按钮

3. **订单管理页面**
   - 表格形式展示订单列表，包含订单号、用户、房间、金额、状态等信息
   - 支持按时间、状态、用户等条件筛选
   - 提供查看详情、处理异常、退款等操作按钮

## 7. 接口规范

- `/api/admin/dashboard` - 仪表盘数据
- `/api/admin/rooms` - 房间管理接口
- `/api/admin/orders` - 订单管理接口
- `/api/admin/users` - 用户管理接口
- `/api/admin/finance` - 财务管理接口

## 8. 安全性考虑

- 所有接口调用使用HTTPS协议
- 管理员权限分级控制
- 操作日志记录

## 9. 性能优化

- 组件懒加载
- 接口请求缓存
- 静态资源CDN部署