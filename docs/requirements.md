# 自助麻将室系统功能需求详细规划

基于您的需求和市场调研，我为您规划了自助麻将室系统的完整功能需求，包括用户端、管理端和系统架构设计。

## 一、系统核心功能模块

### 1. 用户端功能（小程序/APP）

**扫码开门与计时**
- 支持微信和支付宝扫码开启棋牌室大门和房间门
- 开门后自动开始计时，实时显示已用时长和剩余时间
- 防重复开台机制（同一设备扫码间隔需≥5秒）
- 离场确认功能，用户点击"结束使用"后自动结算

**支付功能**
- 支持微信支付、支付宝等多种支付方式
- 开台前预支付（按小时/包夜等计费规则）
- 使用过程中可在线续费/加时
- 自动计算费用并推送支付链接
- 支持美团/饿了么等外卖平台的订单核销功能，用户可使用平台优惠券并享受相应折扣

**预约功能**
- 通过小程序/APP预约心仪的房间和时间段
- 实时查看房间状态（占用/空闲）
- 提前支付预约费用

**个人中心**
- 查看历史订单（时间、房间、费用、支付方式）
- 消费统计（周/月消费总额、高频房间）
- 个人信息管理

### 2. 管理端功能（后台管理系统）

**房间与设备管理**
- 设置房间编号、计费规则（工作日/周末/节假日不同价格）
- 绑定智能门锁、插座等硬件设备
- 实时查看房间占用/空闲状态
- 设备维护记录和故障报警
- 远程开启/控制硬件设备

**订单与财务管理**
- 查看所有订单记录和实时状态
- 财务统计报表（日/周/月收入）
- 异常订单处理
- 退款管理
- 美团/饿了么等外卖平台订单核销管理

**用户管理**
- 用户信息查看和管理
- 会员等级和优惠设置
- 用户行为分析

**系统设置**
- 计费规则设置（按时/包夜/会员价等）
- 语音播报内容设置
- 营业时间设置
- 优惠活动管理

### 3. 硬件系统功能

**智能门禁系统**
- 智能门锁控制（大门和房间门）
- 用户扫码验证身份后自动开锁
- 使用结束后自动上锁
- 支持管理员通过管理端远程开启门锁

**计时与提醒系统**
- 精确计时从用户开门开始到离场结束
- 临近结束前15分钟通过房间喇叭进行语音播报提醒
- 超时自动结算功能

**环境控制系统**
- 房间内设备通断电控制（扫码后通电，结束时断电）
- 环境参数监测（温度、湿度等）
- 支持管理员通过管理端远程控制设备开关

## 二、数据库设计（SQLite）

考虑到您要求使用SQLite数据库，以下是主要数据表设计：

1. **用户表（users）**
   - 用户ID、微信/支付宝openid、昵称、手机号、余额、注册时间等

2. **房间表（rooms）**
   - 房间ID、房间编号、房间名称、状态（空闲/使用中）、计费规则ID等

3. **计费规则表（pricing_rules）**
   - 规则ID、规则名称、价格（按时/包夜等）、适用时间等

4. **订单表（orders）**
   - 订单ID、用户ID、房间ID、开始时间、结束时间、总费用、支付状态等

5. **设备表（devices）**
   - 设备ID、设备类型、房间ID、设备状态、安装时间等

6. **预约表（reservations）**
   - 预约ID、用户ID、房间ID、预约时间、状态等

7. **外卖平台订单表（platform_orders）**
   - 订单ID、平台类型（美团/饿了么）、平台订单号、关联房间ID、用户ID、原价、折扣金额、实际支付金额、订单状态、核销状态、创建时间、核销时间等

## 三、技术架构建议

1. **前端**：微信小程序 + 管理后台Web界面
2. **后端**：Golang语言开发
3. **数据库**：SQLite（满足您要求）
4. **通信协议**：
   - 前后端通信：HTTP/HTTPS API接口，WebSocket实时通信
   - 后端与硬件设备通信：MQTT协议
   - 后端与外卖平台通信：HTTPS API接口
5. **MQTT通信实现**：
   - 使用EMQX等开源MQTT服务器作为消息代理
   - 后端作为MQTT客户端，使用eclipse/paho.mqtt.golang等库实现
   - 硬件设备也作为MQTT客户端，与MQTT服务器通信
   - 通过特定主题（topic）实现指令下发和状态上报
6. **部署**：可部署在云服务器上，通过MQTT服务器连接硬件设备

**外卖平台对接**
- 集成美团、饿了么等外卖平台开放API
- 实现订单同步、核销、退款等功能
- 定期同步商品和价格信息

## 四、特色功能

1. **语音播报提醒**：在使用结束前15分钟通过房间内喇叭进行语音播报提醒
2. **战绩统计**：记录用户的麻将战绩，提供数据分析
3. **智能环境调控**：根据用户需求自动调节房间内环境参数
4. **24小时无人值守**：全流程自助服务，降低人工成本

这个规划涵盖了自助麻将室的核心功能需求，既满足了用户便利使用的需求，也为经营者提供了完善的管理工具。